#ifndef __CONFIG_APP_H_
#define __CONFIG_APP_H_

#include "mydefine.h"
#include "sampling_types.h"  // 包含采样相关类型定义

// 配置参数结构体 - 扩展支持3通道
typedef struct {
    // 3通道变比配置
    float ch0_ratio;                // 通道0变比参数 (0-100)
    float ch1_ratio;                // 通道1变比参数 (0-100)
    float ch2_ratio;                // 通道2变比参数 (0-100)

    // 3通道阈值配置
    float ch0_limit;                // 通道0阈值参数 (0-200)
    float ch1_limit;                // 通道1阈值参数 (0-200)
    float ch2_limit;                // 通道2阈值参数 (0-200)

    // 系统配置
    sampling_cycle_t sample_cycle;  // 采样周期 (5s/10s/15s)

    // 兼容性字段 (保持向后兼容)
    float ratio;                    // 兼容性：映射到ch0_ratio
    float limit;                    // 兼容性：映射到ch0_limit

    // 新增字段
    uint16_t device_id_ref;         // 设备ID引用
    uint32_t config_version;        // 配置版本号
    uint32_t checksum;              // 配置校验和
} config_params_t;

// 配置管理状态枚举
typedef enum {
    CONFIG_OK = 0,
    CONFIG_ERROR,
    CONFIG_FILE_NOT_FOUND,
    CONFIG_INVALID_PARAM,
    CONFIG_FLASH_ERROR
} config_status_t;

// INI 文件解析相关
#define CONFIG_FILE_NAME "config.ini"
#define CONFIG_MAX_LINE_LENGTH 128
#define CONFIG_MAX_SECTION_NAME 32
#define CONFIG_MAX_KEY_NAME 32
#define CONFIG_MAX_VALUE_NAME 32

// 参数范围定义
#define RATIO_MIN 0.0f
#define RATIO_MAX 100.0f
#define LIMIT_MIN 0.0f
#define LIMIT_MAX 200.0f

// Flash 存储相关
#define CONFIG_FLASH_FILE "config.dat"

// 全局配置参数
extern config_params_t g_config_params;

// 函数声明
void config_app_init(void);                                    // 配置模块初始化
config_status_t config_load_from_sd(void);                     // 从SD卡加载配置文件
config_status_t config_safe_sd_test(void);                     // SD卡文件操作安全测试
config_status_t config_display_from_sd(void);                  // 从SD卡显示配置文件内容（只读模式）
config_status_t config_save_to_flash(void);                    // 保存配置到Flash
config_status_t config_save_to_sd(void);                       // 保存配置到SD卡config.ini文件
config_status_t config_load_from_flash(void);                  // 从Flash加载配置
config_status_t config_ensure_ini_file(void);                  // 确保config.ini文件存在
// 兼容性函数 (保持向后兼容)
config_status_t config_set_ratio(float ratio);                 // 设置变比参数 (映射到ch0)
config_status_t config_set_limit(float limit);                 // 设置阈值参数 (映射到ch0)
config_status_t config_set_sample_cycle(sampling_cycle_t cycle); // 设置采样周期参数
float config_get_ratio(void);                                  // 获取变比参数 (返回ch0)
float config_get_limit(void);                                  // 获取阈值参数 (返回ch0)
sampling_cycle_t config_get_sample_cycle(void);                // 获取采样周期参数
config_status_t config_validate_ratio(float ratio);            // 验证变比参数
config_status_t config_validate_limit(float limit);            // 验证阈值参数

// 3通道配置管理函数
config_status_t config_set_channel_ratio(uint8_t channel, float ratio);    // 设置指定通道变比
config_status_t config_set_channel_limit(uint8_t channel, float limit);    // 设置指定通道阈值
float config_get_channel_ratio(uint8_t channel);                           // 获取指定通道变比
float config_get_channel_limit(uint8_t channel);                           // 获取指定通道阈值
config_status_t config_set_all_ratios(float ch0, float ch1, float ch2);    // 设置所有通道变比
config_status_t config_set_all_limits(float ch0, float ch1, float ch2);    // 设置所有通道阈值
void config_get_all_ratios(float *ch0, float *ch1, float *ch2);            // 获取所有通道变比
void config_get_all_limits(float *ch0, float *ch1, float *ch2);            // 获取所有通道阈值

// SD卡安全读取函数 - 修复get_limit命令数据不符问题
config_status_t config_safe_read_from_sd(config_params_t *params);         // 安全读取SD卡配置（支持3通道）
void config_get_all_limits_from_sd(float *ch0, float *ch1, float *ch2);    // 从SD卡获取所有通道阈值
void config_get_all_ratios_from_sd(float *ch0, float *ch1, float *ch2);    // 从SD卡获取所有通道变比

// SD卡与Flash同步函数 - 确保数据一致性
config_status_t config_sync_sd_to_memory_and_flash(void);                  // 将SD卡数据同步到内存和Flash
uint8_t config_check_sd_flash_sync_needed(void);                           // 检查是否需要同步SD卡数据

// 直接读取函数 - 简化版本，直接从SD卡返回数据
void config_get_limits_direct_from_sd(float *ch0, float *ch1, float *ch2); // 直接从SD卡读取阈值（不依赖同步）
void config_get_ratios_direct_from_sd(float *ch0, float *ch1, float *ch2);  // 直接从SD卡读取变比（不依赖同步）
config_status_t config_validate_channel(uint8_t channel);                  // 验证通道号有效性
uint32_t config_calculate_checksum(const config_params_t *params);         // 计算配置校验和

// INI 文件解析函数
config_status_t parse_ini_file(const char *file_content, config_params_t *params);
config_status_t parse_ini_line(const char *line, char *section, char *key, char *value);
void trim_string(char *str);                                   // 去除字符串前后空格

#endif
