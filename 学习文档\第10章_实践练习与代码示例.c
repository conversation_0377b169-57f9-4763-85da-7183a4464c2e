/**
 * @file    第10章_实践练习与代码示例.c
 * @brief   系统集成与项目构建优化 - 实践练习代码
 * @details 通过实际代码示例帮助理解模块集成、错误处理、内存优化、性能调优的实现原理
 * <AUTHOR>
 * @date    2025-01-07
 */

#include "main.h"
#include "stdio.h"
#include "string.h"
#include "mydefine.h"

// ============================================================================
// 练习1：系统架构设计演示
// ============================================================================

/**
 * @brief 演示系统架构层次
 */
void practice_system_architecture_demo(void)
{
    printf("=== 系统架构设计演示 ===\r\n");
    
    printf("STM32F429数据采集系统架构:\r\n");
    printf("\r\n");
    printf("应用层 (Application Layer)\r\n");
    printf("├── 用户接口模块\r\n");
    printf("│   ├── UART命令处理 (uart_app.c)\r\n");
    printf("│   ├── OLED显示输出 (oled_app.c)\r\n");
    printf("│   └── 按键输入处理 (btn_app.c)\r\n");
    printf("├── 数据处理模块\r\n");
    printf("│   ├── ADC数据采集 (adc_app.c)\r\n");
    printf("│   ├── 数字滤波算法\r\n");
    printf("│   └── 数据分析处理\r\n");
    printf("├── 存储管理模块\r\n");
    printf("│   ├── Flash缓存管理 (flash_app.c)\r\n");
    printf("│   ├── SD卡文件系统 (sd_app.c)\r\n");
    printf("│   └── 配置参数管理 (config_app.c)\r\n");
    printf("└── 系统管理模块\r\n");
    printf("    ├── 系统自检 (selftest_app.c)\r\n");
    printf("    ├── 任务调度 (scheduler.c)\r\n");
    printf("    └── 错误处理机制\r\n");
    
    printf("\r\n");
    printf("中间层 (Middleware Layer)\r\n");
    printf("├── 任务调度器 (Scheduler)\r\n");
    printf("├── 配置管理器 (Config Manager)\r\n");
    printf("├── 文件系统 (FATFS)\r\n");
    printf("└── 数学库 (ARM Math)\r\n");
    
    printf("\r\n");
    printf("硬件抽象层 (HAL Layer)\r\n");
    printf("├── STM32 HAL库\r\n");
    printf("├── 外设驱动 (ADC、UART、SPI、I2C、SDIO)\r\n");
    printf("└── 第三方驱动 (OLED、Flash、SD卡)\r\n");
    
    printf("\r\n");
    printf("硬件层 (Hardware Layer)\r\n");
    printf("├── STM32F429微控制器\r\n");
    printf("├── 外围器件 (传感器、存储器、显示器)\r\n");
    printf("└── 电源管理\r\n");
}

/**
 * @brief 演示模块依赖关系
 */
void practice_module_dependency_demo(void)
{
    printf("=== 模块依赖关系演示 ===\r\n");
    
    printf("模块依赖关系图:\r\n");
    printf("main.c\r\n");
    printf("├── scheduler.c (任务调度核心)\r\n");
    printf("│   ├── led_app.c (LED状态指示)\r\n");
    printf("│   ├── btn_app.c (按键输入处理)\r\n");
    printf("│   ├── uart_app.c (串口通信)\r\n");
    printf("│   ├── adc_app.c (数据采集)\r\n");
    printf("│   ├── oled_app.c (显示输出)\r\n");
    printf("│   ├── config_app.c (配置管理)\r\n");
    printf("│   ├── sd_app.c (存储管理)\r\n");
    printf("│   ├── flash_app.c (Flash操作)\r\n");
    printf("│   ├── rtc_app.c (时间管理)\r\n");
    printf("│   └── selftest_app.c (系统自检)\r\n");
    printf("└── mydefine.h (全局定义)\r\n");
    
    printf("\r\n依赖关系分析:\r\n");
    printf("1. 核心依赖: mydefine.h → 所有模块\r\n");
    printf("2. 调度依赖: scheduler.c → 所有任务模块\r\n");
    printf("3. 配置依赖: config_app.c → flash_app.c, sd_app.c\r\n");
    printf("4. 存储依赖: sd_app.c → rtc_app.c (时间戳)\r\n");
    printf("5. 显示依赖: oled_app.c → adc_app.c (数据显示)\r\n");
    
    printf("\r\n依赖管理原则:\r\n");
    printf("✓ 避免循环依赖\r\n");
    printf("✓ 最小化依赖关系\r\n");
    printf("✓ 使用接口抽象\r\n");
    printf("✓ 分层设计\r\n");
}

/**
 * @brief 演示系统初始化序列
 */
void practice_system_init_demo(void)
{
    printf("=== 系统初始化序列演示 ===\r\n");
    
    printf("系统初始化步骤:\r\n");
    printf("1. 硬件初始化\r\n");
    printf("   HAL_Init()                    // HAL库初始化\r\n");
    printf("   SystemClock_Config()          // 系统时钟配置\r\n");
    
    printf("\r\n2. 外设初始化\r\n");
    printf("   MX_GPIO_Init()               // GPIO初始化\r\n");
    printf("   MX_DMA_Init()                // DMA初始化\r\n");
    printf("   MX_USART1_UART_Init()        // 串口初始化\r\n");
    printf("   MX_ADC1_Init()               // ADC初始化\r\n");
    printf("   MX_TIM3_Init()               // 定时器初始化\r\n");
    printf("   MX_I2C1_Init()               // I2C初始化\r\n");
    printf("   MX_SPI2_Init()               // SPI初始化\r\n");
    printf("   MX_SDIO_SD_Init()            // SD卡初始化\r\n");
    printf("   MX_FATFS_Init()              // 文件系统初始化\r\n");
    printf("   MX_RTC_Init()                // RTC初始化\r\n");
    
    printf("\r\n3. 应用层初始化\r\n");
    printf("   OLED_Init()                  // OLED显示初始化\r\n");
    printf("   rt_ringbuffer_init()         // 环形缓冲区初始化\r\n");
    printf("   app_btn_init()               // 按键应用初始化\r\n");
    printf("   scheduler_init()             // 调度器初始化\r\n");
    
    printf("\r\n4. 主循环\r\n");
    printf("   while (1) {\r\n");
    printf("       scheduler_run();         // 运行任务调度器\r\n");
    printf("   }\r\n");
    
    printf("\r\n初始化顺序原则:\r\n");
    printf("✓ 先初始化底层，再初始化上层\r\n");
    printf("✓ 先初始化依赖项，再初始化使用者\r\n");
    printf("✓ 关键系统优先初始化\r\n");
    printf("✓ 错误检查和处理\r\n");
}

// ============================================================================
// 练习2：模块集成策略演示
// ============================================================================

/**
 * @brief 演示头文件管理策略
 */
void practice_header_management_demo(void)
{
    printf("=== 头文件管理策略演示 ===\r\n");
    
    printf("mydefine.h统一头文件管理:\r\n");
    printf("\r\n");
    printf("// === 标准C库头文件 ===\r\n");
    printf("#include \"stdio.h\"\r\n");
    printf("#include \"string.h\"\r\n");
    printf("#include \"stdarg.h\"\r\n");
    printf("#include \"stdint.h\"\r\n");
    printf("#include \"stdlib.h\"\r\n");
    
    printf("\r\n// === STM32 HAL库和外设头文件 ===\r\n");
    printf("#include \"main.h\"\r\n");
    printf("#include \"usart.h\"\r\n");
    printf("#include \"adc.h\"\r\n");
    printf("#include \"tim.h\"\r\n");
    printf("#include \"rtc.h\"\r\n");
    
    printf("\r\n// === 第三方库头文件 ===\r\n");
    printf("#include \"ringbuffer.h\"     // 环形缓冲区\r\n");
    printf("#include \"arm_math.h\"       // ARM数学库\r\n");
    printf("#include \"gd25qxx.h\"        // Flash驱动\r\n");
    
    printf("\r\n// === 应用层头文件 ===\r\n");
    printf("#include \"scheduler.h\"      // 任务调度器\r\n");
    printf("#include \"adc_app.h\"        // ADC应用层\r\n");
    printf("#include \"led_app.h\"        // LED应用层\r\n");
    printf("#include \"config_app.h\"     // 配置管理应用层\r\n");
    
    printf("\r\n头文件管理优势:\r\n");
    printf("✓ 统一管理，避免重复包含\r\n");
    printf("✓ 减少编译时间\r\n");
    printf("✓ 便于维护和修改\r\n");
    printf("✓ 避免头文件冲突\r\n");
}

/**
 * @brief 演示全局变量管理
 */
void practice_global_variable_demo(void)
{
    printf("=== 全局变量管理演示 ===\r\n");
    
    printf("全局变量声明 (mydefine.h):\r\n");
    printf("extern uint16_t uart_rx_index;              // 串口接收索引\r\n");
    printf("extern uint32_t uart_rx_ticks;              // 串口接收时间戳\r\n");
    printf("extern uint8_t uart_rx_buffer[128];         // 串口接收缓冲区\r\n");
    printf("extern UART_HandleTypeDef huart1;           // 串口1句柄\r\n");
    printf("extern struct rt_ringbuffer uart_ringbuffer; // 串口环形缓冲区\r\n");
    
    printf("\r\n全局变量定义 (对应.c文件):\r\n");
    printf("uint16_t uart_rx_index = 0;\r\n");
    printf("uint32_t uart_rx_ticks = 0;\r\n");
    printf("uint8_t uart_rx_buffer[128] = {0};\r\n");
    printf("struct rt_ringbuffer uart_ringbuffer;\r\n");
    printf("uint8_t uart_send_flag = 0;\r\n");
    
    printf("\r\n全局变量管理原则:\r\n");
    printf("✓ 在头文件中声明 (extern)\r\n");
    printf("✓ 在源文件中定义\r\n");
    printf("✓ 使用有意义的命名\r\n");
    printf("✓ 初始化为安全值\r\n");
    printf("✓ 最小化全局变量使用\r\n");
    
    // 演示当前全局变量状态
    extern uint16_t uart_rx_index;
    extern uint32_t uart_rx_ticks;
    
    printf("\r\n当前全局变量状态:\r\n");
    printf("uart_rx_index: %d\r\n", uart_rx_index);
    printf("uart_rx_ticks: %lu\r\n", uart_rx_ticks);
}

/**
 * @brief 演示模块接口设计
 */
void practice_module_interface_demo(void)
{
    printf("=== 模块接口设计演示 ===\r\n");
    
    printf("统一模块接口设计:\r\n");
    printf("\r\n1. 统一的初始化接口\r\n");
    printf("void module_init(void);\r\n");
    printf("示例: adc_control_init(), config_app_init()\r\n");
    
    printf("\r\n2. 统一的任务接口\r\n");
    printf("void module_task(void);\r\n");
    printf("示例: adc_task(), uart_task(), oled_task()\r\n");
    
    printf("\r\n3. 统一的配置接口\r\n");
    printf("module_status_t module_set_config(module_config_t *config);\r\n");
    printf("module_status_t module_get_config(module_config_t *config);\r\n");
    printf("示例: config_set_ratio(), config_get_limit()\r\n");
    
    printf("\r\n4. 统一的状态查询接口\r\n");
    printf("module_status_t module_get_status(void);\r\n");
    printf("示例: scheduler_get_status(), flash_get_status()\r\n");
    
    printf("\r\n5. 统一的错误处理接口\r\n");
    printf("module_status_t module_handle_error(module_error_t error);\r\n");
    printf("示例: config_handle_error(), sd_handle_error()\r\n");
    
    printf("\r\n接口设计原则:\r\n");
    printf("✓ 命名规范统一\r\n");
    printf("✓ 参数类型一致\r\n");
    printf("✓ 返回值标准化\r\n");
    printf("✓ 功能单一明确\r\n");
}

// ============================================================================
// 练习3：数据流管理演示
// ============================================================================

/**
 * @brief 演示任务调度器设计
 */
void practice_task_scheduler_demo(void)
{
    printf("=== 任务调度器设计演示 ===\r\n");
    
    printf("任务结构体定义:\r\n");
    printf("typedef struct {\r\n");
    printf("    void (*task_func)(void);    // 任务函数指针\r\n");
    printf("    uint32_t rate_ms;           // 任务执行周期（毫秒）\r\n");
    printf("    uint32_t last_run;          // 上次执行时间戳（毫秒）\r\n");
    printf("} task_t;\r\n");
    
    printf("\r\n任务配置表:\r\n");
    printf("static task_t scheduler_task[] = {\r\n");
    printf("    {led_task,           1,    0},      // LED任务：1ms周期\r\n");
    printf("    {btn_task,           5,    0},      // 按键任务：5ms周期\r\n");
    printf("    {uart_task,          5,    0},      // 串口任务：5ms周期\r\n");
    printf("    {adc_task,           100,  0},      // ADC任务：100ms周期\r\n");
    printf("    {adc_led1_blink_task, 1000, 0},    // LED闪烁：1000ms周期\r\n");
    printf("    {oled_task,          100, 0}       // OLED任务：100ms周期\r\n");
    printf("};\r\n");
    
    printf("\r\n调度算法:\r\n");
    printf("void scheduler_run(void) {\r\n");
    printf("    for (uint8_t i = 0; i < task_num; i++) {\r\n");
    printf("        uint32_t now_time = HAL_GetTick();\r\n");
    printf("        \r\n");
    printf("        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run) {\r\n");
    printf("            scheduler_task[i].last_run = now_time;\r\n");
    printf("            scheduler_task[i].task_func();\r\n");
    printf("        }\r\n");
    printf("    }\r\n");
    printf("}\r\n");
    
    // 显示当前调度器状态
    extern uint8_t task_num;
    printf("\r\n当前调度器状态:\r\n");
    printf("任务总数: %d\r\n", task_num);
    printf("调度器状态: %s\r\n", scheduler_get_status() ? "正常" : "异常");
}

/**
 * @brief 演示数据传递机制
 */
void practice_data_transfer_demo(void)
{
    printf("=== 数据传递机制演示 ===\r\n");
    
    printf("1. 全局变量方式 (适用于频繁访问的数据):\r\n");
    printf("extern float g_current_voltage;        // 当前电压值\r\n");
    printf("extern uint8_t g_system_state;         // 系统状态\r\n");
    printf("优点: 访问快速，实现简单\r\n");
    printf("缺点: 耦合度高，线程安全问题\r\n");
    
    printf("\r\n2. 函数参数方式 (适用于临时数据传递):\r\n");
    printf("void process_adc_data(uint32_t adc_value, float *voltage_out);\r\n");
    printf("优点: 接口清晰，参数明确\r\n");
    printf("缺点: 参数过多时复杂\r\n");
    
    printf("\r\n3. 结构体方式 (适用于复杂数据):\r\n");
    printf("typedef struct {\r\n");
    printf("    float voltage;\r\n");
    printf("    uint32_t timestamp;\r\n");
    printf("    uint8_t over_limit;\r\n");
    printf("} measurement_data_t;\r\n");
    printf("优点: 数据组织清晰，易于扩展\r\n");
    printf("缺点: 内存开销较大\r\n");
    
    printf("\r\n4. 回调函数方式 (适用于事件驱动):\r\n");
    printf("typedef void (*data_ready_callback_t)(measurement_data_t *data);\r\n");
    printf("void register_data_callback(data_ready_callback_t callback);\r\n");
    printf("优点: 解耦合，事件驱动\r\n");
    printf("缺点: 实现复杂，调试困难\r\n");
}

// ============================================================================
// 练习4：错误处理机制演示
// ============================================================================

/**
 * @brief 演示统一错误码定义
 */
void practice_error_code_demo(void)
{
    printf("=== 统一错误码定义演示 ===\r\n");
    
    printf("系统错误码枚举:\r\n");
    printf("typedef enum {\r\n");
    printf("    SYSTEM_OK = 0,              // 操作成功\r\n");
    printf("    SYSTEM_ERROR,               // 通用错误\r\n");
    printf("    SYSTEM_TIMEOUT,             // 超时错误\r\n");
    printf("    SYSTEM_INVALID_PARAM,       // 参数无效\r\n");
    printf("    SYSTEM_RESOURCE_BUSY,       // 资源忙\r\n");
    printf("    SYSTEM_HARDWARE_ERROR,      // 硬件错误\r\n");
    printf("    SYSTEM_MEMORY_ERROR,        // 内存错误\r\n");
    printf("    SYSTEM_COMMUNICATION_ERROR  // 通信错误\r\n");
    printf("} system_status_t;\r\n");
    
    printf("\r\n错误码使用示例:\r\n");
    printf("system_status_t result = some_function();\r\n");
    printf("switch (result) {\r\n");
    printf("    case SYSTEM_OK:\r\n");
    printf("        // 处理成功情况\r\n");
    printf("        break;\r\n");
    printf("    case SYSTEM_TIMEOUT:\r\n");
    printf("        // 处理超时情况\r\n");
    printf("        break;\r\n");
    printf("    default:\r\n");
    printf("        // 处理其他错误\r\n");
    printf("        break;\r\n");
    printf("}\r\n");
    
    printf("\r\n错误码设计原则:\r\n");
    printf("✓ 0表示成功，非0表示错误\r\n");
    printf("✓ 错误码有明确含义\r\n");
    printf("✓ 分类清晰，便于处理\r\n");
    printf("✓ 预留扩展空间\r\n");
}

/**
 * @brief 演示错误处理策略
 */
void practice_error_handling_demo(void)
{
    printf("=== 错误处理策略演示 ===\r\n");
    
    printf("分层错误处理示例:\r\n");
    printf("system_status_t high_level_function(void) {\r\n");
    printf("    system_status_t status = low_level_function();\r\n");
    printf("    \r\n");
    printf("    switch (status) {\r\n");
    printf("        case SYSTEM_OK:\r\n");
    printf("            return SYSTEM_OK;\r\n");
    printf("            \r\n");
    printf("        case SYSTEM_TIMEOUT:\r\n");
    printf("            // 重试机制\r\n");
    printf("            for (int retry = 0; retry < 3; retry++) {\r\n");
    printf("                HAL_Delay(100);\r\n");
    printf("                status = low_level_function();\r\n");
    printf("                if (status == SYSTEM_OK) {\r\n");
    printf("                    return SYSTEM_OK;\r\n");
    printf("                }\r\n");
    printf("            }\r\n");
    printf("            log_error(\"Function timeout after 3 retries\");\r\n");
    printf("            return SYSTEM_TIMEOUT;\r\n");
    printf("            \r\n");
    printf("        case SYSTEM_HARDWARE_ERROR:\r\n");
    printf("            // 硬件重新初始化\r\n");
    printf("            reinitialize_hardware();\r\n");
    printf("            return SYSTEM_HARDWARE_ERROR;\r\n");
    printf("            \r\n");
    printf("        default:\r\n");
    printf("            return status;\r\n");
    printf("    }\r\n");
    printf("}\r\n");
    
    printf("\r\n错误处理策略:\r\n");
    printf("✓ 重试机制 - 临时性错误\r\n");
    printf("✓ 降级处理 - 功能受限但可用\r\n");
    printf("✓ 重新初始化 - 硬件错误\r\n");
    printf("✓ 记录日志 - 便于调试\r\n");
    printf("✓ 用户通知 - 严重错误\r\n");
}

// ============================================================================
// 主练习函数
// ============================================================================

/**
 * @brief 第10章所有练习的入口函数
 */
void chapter10_practice_all(void)
{
    printf("\r\n");
    printf("========================================\r\n");
    printf("    第10章：系统集成与项目构建优化\r\n");
    printf("           实践练习开始\r\n");
    printf("========================================\r\n");
    
    practice_system_architecture_demo();
    printf("\r\n");
    
    practice_module_dependency_demo();
    printf("\r\n");
    
    practice_system_init_demo();
    printf("\r\n");
    
    practice_header_management_demo();
    printf("\r\n");
    
    practice_global_variable_demo();
    printf("\r\n");
    
    practice_module_interface_demo();
    printf("\r\n");
    
    practice_task_scheduler_demo();
    printf("\r\n");
    
    practice_data_transfer_demo();
    printf("\r\n");
    
    practice_error_code_demo();
    printf("\r\n");
    
    practice_error_handling_demo();
    printf("\r\n");
    
    printf("========================================\r\n");
    printf("    🎉 恭喜！学习计划全部完成！ 🎉\r\n");
    printf("========================================\r\n");
    printf("您已掌握STM32F429嵌入式数据采集系统的:\r\n");
    printf("✅ 系统架构设计和模块集成\r\n");
    printf("✅ 数据流管理和任务调度\r\n");
    printf("✅ 错误处理和异常恢复\r\n");
    printf("✅ 内存优化和性能调优\r\n");
    printf("✅ 项目构建和调试技巧\r\n");
    printf("\r\n");
    printf("现在您可以独立开发类似的嵌入式项目了！\r\n");
    printf("========================================\r\n");
    printf("\r\n");
}
