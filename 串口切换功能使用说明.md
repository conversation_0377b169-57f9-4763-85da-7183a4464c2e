# 串口切换功能使用说明

## 功能概述

本项目已实现串口1和串口2的切换功能，通过修改宏定义即可在两个串口之间切换所有数据的收发。

## 硬件配置

### 串口1 (USART1)
- **引脚**: PA9(TX), PA10(RX)
- **波特率**: 460800
- **DMA**: 支持DMA接收和发送
- **功能**: 完整的命令解析、数据收发、环形缓冲区

### 串口2 (USART2)
- **引脚**: PA2(TX), PA3(RX)
- **波特率**: 9600
- **DMA**: 支持DMA接收和发送
- **功能**: 完整的命令解析、数据收发、环形缓冲区

## 使用方法

### 1. 切换到串口1
```c
// 在 sysFunction/mydefine.h 文件中修改：
#define UART_SELECT 1  // 使用串口1
```

### 2. 切换到串口2
```c
// 在 sysFunction/mydefine.h 文件中修改：
#define UART_SELECT 2  // 使用串口2
```

### 3. 重新编译和烧录
修改宏定义后，需要重新编译整个工程并烧录到单片机。

## 功能特性

### 自动切换的功能
- ✅ 串口数据接收（DMA + 环形缓冲区）
- ✅ 串口数据发送（所有printf输出）
- ✅ 命令解析和处理
- ✅ 中断回调函数
- ✅ 二进制协议处理
- ✅ 所有系统输出信息

### 新增函数
- `uart_printf()`: 自动选择当前配置串口的printf函数
- `uart_switch_test()`: 串口切换测试函数
- `uart_communication_test()`: 串口通信测试函数

## 测试命令

### uart_test 命令
在串口终端中输入 `uart_test` 可以测试当前串口配置：

```
uart_test
```

输出示例：
```
=== UART Switch Test ===
Current UART: UART1
Baud Rate: 460800
GPIO: PA9(TX), PA10(RX)
=== UART Communication Test ===
Test 1: Basic output - OK
Test 2: Formatted output - int:123, float:45.67
Test 3: Long string output - This is a longer test string...
=== Test Complete ===
=== Switch Instructions ===
To switch UART:
1. Edit mydefine.h
2. Change UART_SELECT to 1 or 2
3. Rebuild and flash
========================
```

## 技术实现

### 宏定义切换机制
```c
// 串口选择宏
#define UART_SELECT 1  // 1=串口1, 2=串口2

// 自动选择当前串口句柄
#if UART_SELECT == 1
    #define CURRENT_UART huart1
    #define CURRENT_DMA_RX hdma_usart1_rx
#elif UART_SELECT == 2
    #define CURRENT_UART huart2
    #define CURRENT_DMA_RX hdma_usart2_rx
#endif
```

### 兼容性保留
- 原有的 `my_printf()` 函数保持兼容，但内部已修改为使用宏定义选择串口
- 新增的 `uart_printf()` 函数提供更简洁的调用方式
- 所有现有代码无需修改即可使用串口切换功能

## 注意事项

1. **波特率差异**: 串口1使用460800波特率，串口2使用9600波特率，请相应调整终端设置
2. **引脚连接**: 确保硬件连接到正确的GPIO引脚
3. **DMA配置**: 两个串口都已配置DMA，确保DMA通道无冲突
4. **编译重建**: 修改宏定义后必须重新编译整个工程

## 验证步骤

1. 修改 `UART_SELECT` 为 1，编译烧录
2. 使用460800波特率连接PA9/PA10，测试命令功能
3. 修改 `UART_SELECT` 为 2，编译烧录
4. 使用9600波特率连接PA2/PA3，测试命令功能
5. 运行 `uart_test` 命令验证切换成功

## 文件修改清单

- `sysFunction/mydefine.h`: 添加串口选择宏定义
- `sysFunction/usart_app.c`: 修改串口处理函数支持切换
- `sysFunction/usart_app.h`: 添加新函数声明
- `Core/Src/usart.c`: 修改fputc函数支持切换
- `sysFunction/uart_test.c`: 新增测试功能
- `sysFunction/uart_test.h`: 新增测试头文件

功能已完成，可以通过修改一个宏定义实现串口1和串口2的完整切换！
