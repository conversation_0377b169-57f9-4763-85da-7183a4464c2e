/**
 * @file    第4章_实践练习与代码示例.c
 * @brief   串口通信与环形缓冲区实现 - 实践练习代码
 * @details 通过实际代码示例帮助理解UART配置、DMA传输、环形缓冲区和命令解析的实现原理
 * <AUTHOR>
 * @date    2025-01-07
 */

#include "main.h"
#include "stdio.h"
#include "string.h"
#include "stdarg.h"
#include "usart.h"

// ============================================================================
// 练习1：UART配置详解
// ============================================================================

/**
 * @brief 演示UART_HandleTypeDef结构体配置
 */
void practice_uart_config_demo(void)
{
    printf("=== UART配置演示 ===\r\n");
    
    // 分析项目中的UART配置
    extern UART_HandleTypeDef huart1;
    
    printf("UART_HandleTypeDef结构体大小: %d 字节\r\n", sizeof(UART_HandleTypeDef));
    printf("当前UART配置:\r\n");
    printf("  外设实例: USART%d\r\n", 
           (huart1.Instance == USART1) ? 1 : 
           (huart1.Instance == USART2) ? 2 : 0);
    printf("  波特率: %d bps\r\n", huart1.Init.BaudRate);
    printf("  数据位: %s\r\n", 
           (huart1.Init.WordLength == UART_WORDLENGTH_8B) ? "8位" : "9位");
    printf("  停止位: %s\r\n", 
           (huart1.Init.StopBits == UART_STOPBITS_1) ? "1位" : "2位");
    printf("  校验位: %s\r\n", 
           (huart1.Init.Parity == UART_PARITY_NONE) ? "无" : 
           (huart1.Init.Parity == UART_PARITY_EVEN) ? "偶校验" : "奇校验");
    printf("  工作模式: %s\r\n", 
           (huart1.Init.Mode == UART_MODE_TX_RX) ? "收发" : 
           (huart1.Init.Mode == UART_MODE_TX) ? "仅发送" : "仅接收");
    printf("  流控制: %s\r\n", 
           (huart1.Init.HwFlowCtl == UART_HWCONTROL_NONE) ? "无" : "硬件流控");
    
    // 计算实际波特率
    uint32_t system_clock = HAL_RCC_GetPCLK2Freq();  // USART1在APB2上
    uint32_t usartdiv = system_clock / (16 * huart1.Init.BaudRate);
    uint32_t actual_baudrate = system_clock / (16 * usartdiv);
    
    printf("  系统时钟: %d Hz\r\n", system_clock);
    printf("  USARTDIV: %d\r\n", usartdiv);
    printf("  实际波特率: %d bps\r\n", actual_baudrate);
    printf("  波特率误差: %.2f%%\r\n", 
           ((float)(actual_baudrate - huart1.Init.BaudRate) / huart1.Init.BaudRate) * 100);
}

/**
 * @brief 演示不同波特率的配置
 */
void practice_baudrate_config_demo(void)
{
    printf("=== 波特率配置演示 ===\r\n");
    
    uint32_t baudrates[] = {9600, 38400, 115200, 460800, 921600};
    uint8_t baudrate_count = sizeof(baudrates) / sizeof(uint32_t);
    
    printf("常用波特率配置分析:\r\n");
    printf("波特率\t\tUSARTDIV\t实际波特率\t误差\r\n");
    printf("----------------------------------------------------\r\n");
    
    uint32_t pclk2_freq = HAL_RCC_GetPCLK2Freq();
    
    for (uint8_t i = 0; i < baudrate_count; i++) {
        uint32_t target_baudrate = baudrates[i];
        uint32_t usartdiv = pclk2_freq / (16 * target_baudrate);
        uint32_t actual_baudrate = pclk2_freq / (16 * usartdiv);
        float error_percent = ((float)(actual_baudrate - target_baudrate) / target_baudrate) * 100;
        
        printf("%d\t\t%d\t\t%d\t\t%.2f%%\r\n", 
               target_baudrate, usartdiv, actual_baudrate, error_percent);
    }
}

// ============================================================================
// 练习2：DMA传输机制演示
// ============================================================================

/**
 * @brief 演示DMA配置分析
 */
void practice_dma_config_demo(void)
{
    printf("=== DMA配置演示 ===\r\n");
    
    extern DMA_HandleTypeDef hdma_usart1_rx;
    
    printf("DMA_HandleTypeDef结构体大小: %d 字节\r\n", sizeof(DMA_HandleTypeDef));
    printf("USART1 RX DMA配置:\r\n");
    printf("  DMA实例: DMA2_Stream2\r\n");
    printf("  通道: %d\r\n", hdma_usart1_rx.Init.Channel);
    printf("  方向: %s\r\n", 
           (hdma_usart1_rx.Init.Direction == DMA_PERIPH_TO_MEMORY) ? "外设到内存" : 
           (hdma_usart1_rx.Init.Direction == DMA_MEMORY_TO_PERIPH) ? "内存到外设" : "内存到内存");
    printf("  外设地址递增: %s\r\n", 
           (hdma_usart1_rx.Init.PeriphInc == DMA_PINC_ENABLE) ? "使能" : "禁用");
    printf("  内存地址递增: %s\r\n", 
           (hdma_usart1_rx.Init.MemInc == DMA_MINC_ENABLE) ? "使能" : "禁用");
    printf("  外设数据宽度: %s\r\n", 
           (hdma_usart1_rx.Init.PeriphDataAlignment == DMA_PDATAALIGN_BYTE) ? "8位" : 
           (hdma_usart1_rx.Init.PeriphDataAlignment == DMA_PDATAALIGN_HALFWORD) ? "16位" : "32位");
    printf("  内存数据宽度: %s\r\n", 
           (hdma_usart1_rx.Init.MemDataAlignment == DMA_MDATAALIGN_BYTE) ? "8位" : 
           (hdma_usart1_rx.Init.MemDataAlignment == DMA_MDATAALIGN_HALFWORD) ? "16位" : "32位");
    printf("  工作模式: %s\r\n", 
           (hdma_usart1_rx.Init.Mode == DMA_NORMAL) ? "普通模式" : "循环模式");
    printf("  优先级: %s\r\n", 
           (hdma_usart1_rx.Init.Priority == DMA_PRIORITY_LOW) ? "低" : 
           (hdma_usart1_rx.Init.Priority == DMA_PRIORITY_MEDIUM) ? "中" : 
           (hdma_usart1_rx.Init.Priority == DMA_PRIORITY_HIGH) ? "高" : "极高");
}

/**
 * @brief 演示DMA传输过程
 */
void practice_dma_transfer_demo(void)
{
    printf("=== DMA传输过程演示 ===\r\n");
    
    static uint8_t demo_rx_buffer[64];
    static uint8_t demo_tx_buffer[] = "DMA Transfer Test\r\n";
    
    printf("DMA传输演示:\r\n");
    printf("1. 发送缓冲区地址: 0x%08X\r\n", (uint32_t)demo_tx_buffer);
    printf("2. 接收缓冲区地址: 0x%08X\r\n", (uint32_t)demo_rx_buffer);
    printf("3. 发送数据长度: %d 字节\r\n", sizeof(demo_tx_buffer) - 1);
    
    // 演示DMA发送
    printf("4. 启动DMA发送...\r\n");
    HAL_StatusTypeDef status = HAL_UART_Transmit_DMA(&huart1, demo_tx_buffer, sizeof(demo_tx_buffer) - 1);
    
    switch (status) {
        case HAL_OK:
            printf("   DMA发送启动成功\r\n");
            break;
        case HAL_ERROR:
            printf("   DMA发送启动失败\r\n");
            break;
        case HAL_BUSY:
            printf("   UART忙碌中\r\n");
            break;
        case HAL_TIMEOUT:
            printf("   启动超时\r\n");
            break;
    }
    
    // 等待发送完成
    while (HAL_UART_GetState(&huart1) != HAL_UART_STATE_READY) {
        HAL_Delay(1);
    }
    printf("5. DMA发送完成\r\n");
}

// ============================================================================
// 练习3：环形缓冲区实现
// ============================================================================

/**
 * @brief 简化版环形缓冲区结构体
 */
typedef struct {
    uint8_t *buffer;        // 缓冲区指针
    uint16_t size;          // 缓冲区大小
    uint16_t read_index;    // 读指针
    uint16_t write_index;   // 写指针
    uint8_t full;           // 满标志
} simple_ringbuffer_t;

/**
 * @brief 简化版环形缓冲区演示
 */
void practice_simple_ringbuffer_demo(void)
{
    printf("=== 简化版环形缓冲区演示 ===\r\n");
    
    // 创建环形缓冲区
    static uint8_t buffer_pool[16];
    simple_ringbuffer_t rb = {
        .buffer = buffer_pool,
        .size = sizeof(buffer_pool),
        .read_index = 0,
        .write_index = 0,
        .full = 0
    };
    
    printf("环形缓冲区初始化:\r\n");
    printf("  缓冲区大小: %d 字节\r\n", rb.size);
    printf("  读指针: %d\r\n", rb.read_index);
    printf("  写指针: %d\r\n", rb.write_index);
    printf("  满标志: %d\r\n", rb.full);
    
    // 写入数据演示
    printf("\n写入数据演示:\r\n");
    const char *test_data = "Hello World!";
    uint16_t data_len = strlen(test_data);
    
    for (uint16_t i = 0; i < data_len; i++) {
        if (!rb.full) {
            rb.buffer[rb.write_index] = test_data[i];
            rb.write_index = (rb.write_index + 1) % rb.size;
            
            if (rb.write_index == rb.read_index) {
                rb.full = 1;
            }
            
            printf("  写入 '%c', 写指针: %d, 满标志: %d\r\n", 
                   test_data[i], rb.write_index, rb.full);
        } else {
            printf("  缓冲区已满，无法写入 '%c'\r\n", test_data[i]);
        }
    }
    
    // 读取数据演示
    printf("\n读取数据演示:\r\n");
    while (!(rb.read_index == rb.write_index && !rb.full)) {
        char data = rb.buffer[rb.read_index];
        rb.read_index = (rb.read_index + 1) % rb.size;
        rb.full = 0;
        
        printf("  读取 '%c', 读指针: %d\r\n", data, rb.read_index);
    }
    
    printf("缓冲区已空\r\n");
}

/**
 * @brief RT-Thread环形缓冲区使用演示
 */
void practice_rt_ringbuffer_demo(void)
{
    printf("=== RT-Thread环形缓冲区演示 ===\r\n");
    
    // 使用项目中的环形缓冲区
    extern struct rt_ringbuffer uart_ringbuffer;
    extern uint8_t ringbuffer_pool[128];
    
    printf("RT-Thread环形缓冲区信息:\r\n");
    printf("  缓冲区地址: 0x%08X\r\n", (uint32_t)uart_ringbuffer.buffer_ptr);
    printf("  缓冲区大小: %d 字节\r\n", uart_ringbuffer.buffer_size);
    printf("  读指针: %d (镜像位: %d)\r\n", 
           uart_ringbuffer.read_index, uart_ringbuffer.read_mirror);
    printf("  写指针: %d (镜像位: %d)\r\n", 
           uart_ringbuffer.write_index, uart_ringbuffer.write_mirror);
    
    // 计算可用空间和数据长度
    uint16_t space_len = rt_ringbuffer_space_len(&uart_ringbuffer);
    uint16_t data_len = rt_ringbuffer_data_len(&uart_ringbuffer);
    
    printf("  可用空间: %d 字节\r\n", space_len);
    printf("  数据长度: %d 字节\r\n", data_len);
    printf("  使用率: %.1f%%\r\n", ((float)data_len / uart_ringbuffer.buffer_size) * 100);
    
    // 演示数据操作
    const char *test_msg = "RingBuffer Test";
    uint16_t put_len = rt_ringbuffer_put(&uart_ringbuffer, 
                                        (const uint8_t *)test_msg, 
                                        strlen(test_msg));
    printf("写入数据: \"%s\" (%d 字节)\r\n", test_msg, put_len);
    
    // 读取数据
    char read_buffer[32];
    uint16_t get_len = rt_ringbuffer_get(&uart_ringbuffer, 
                                        (uint8_t *)read_buffer, 
                                        sizeof(read_buffer) - 1);
    read_buffer[get_len] = '\0';
    printf("读取数据: \"%s\" (%d 字节)\r\n", read_buffer, get_len);
}

// ============================================================================
// 练习4：命令解析状态机
// ============================================================================

/**
 * @brief 简化版命令解析演示
 */
typedef enum {
    CMD_STATE_NORMAL,
    CMD_STATE_WAIT_PARAM
} cmd_state_t;

typedef struct {
    const char *cmd_name;
    void (*handler)(const char *param);
} simple_cmd_t;

static cmd_state_t cmd_state = CMD_STATE_NORMAL;
static char waiting_cmd[32] = {0};

void handle_test_command(const char *param)
{
    printf("执行测试命令，参数: %s\r\n", param ? param : "无");
}

void handle_set_command(const char *param)
{
    printf("执行设置命令，参数: %s\r\n", param ? param : "无");
}

void handle_get_command(const char *param)
{
    printf("执行获取命令，参数: %s\r\n", param ? param : "无");
}

static const simple_cmd_t simple_cmd_table[] = {
    {"test", handle_test_command},
    {"set", handle_set_command},
    {"get", handle_get_command},
    {NULL, NULL}
};

void practice_command_parser_demo(void)
{
    printf("=== 命令解析状态机演示 ===\r\n");
    
    const char *test_commands[] = {
        "test",
        "set 123",
        "get voltage",
        "unknown",
        "test param1 param2"
    };
    
    uint8_t cmd_count = sizeof(test_commands) / sizeof(char *);
    
    printf("支持的命令:\r\n");
    for (uint8_t i = 0; simple_cmd_table[i].cmd_name != NULL; i++) {
        printf("  %s\r\n", simple_cmd_table[i].cmd_name);
    }
    
    printf("\n命令解析测试:\r\n");
    for (uint8_t i = 0; i < cmd_count; i++) {
        printf("输入: \"%s\"\r\n", test_commands[i]);
        
        // 解析命令
        char cmd_copy[64];
        strncpy(cmd_copy, test_commands[i], sizeof(cmd_copy) - 1);
        cmd_copy[sizeof(cmd_copy) - 1] = '\0';
        
        char *cmd_name = strtok(cmd_copy, " ");
        char *param = strtok(NULL, "");
        
        // 查找命令
        uint8_t found = 0;
        for (uint8_t j = 0; simple_cmd_table[j].cmd_name != NULL; j++) {
            if (strcmp(cmd_name, simple_cmd_table[j].cmd_name) == 0) {
                simple_cmd_table[j].handler(param);
                found = 1;
                break;
            }
        }
        
        if (!found) {
            printf("未知命令: %s\r\n", cmd_name);
        }
        
        printf("\r\n");
    }
}

// ============================================================================
// 练习5：串口数据处理流程
// ============================================================================

/**
 * @brief 演示完整的串口数据处理流程
 */
void practice_uart_data_flow_demo(void)
{
    printf("=== 串口数据处理流程演示 ===\r\n");
    
    printf("数据流程:\r\n");
    printf("1. 硬件接收 → UART RX引脚接收数据\r\n");
    printf("2. DMA传输 → DMA自动将数据搬移到内存\r\n");
    printf("3. 中断回调 → HAL_UARTEx_RxEventCallback被调用\r\n");
    printf("4. 环形缓冲区 → 数据写入ringbuffer\r\n");
    printf("5. 任务处理 → uart_task读取并处理数据\r\n");
    printf("6. 命令解析 → 解析命令并执行对应功能\r\n");
    printf("7. 响应输出 → 通过UART发送响应信息\r\n");
    
    // 模拟数据处理
    printf("\n模拟数据处理:\r\n");
    const char *mock_data = "test command\r\n";
    printf("接收数据: \"%s\"\r\n", mock_data);
    
    // 模拟写入环形缓冲区
    printf("写入环形缓冲区...\r\n");
    
    // 模拟从环形缓冲区读取
    printf("从环形缓冲区读取数据...\r\n");
    
    // 模拟命令解析
    printf("解析命令: \"test\"\r\n");
    
    // 模拟执行命令
    printf("执行测试命令...\r\n");
    
    // 模拟发送响应
    printf("发送响应: \"Test completed\\r\\n\"\r\n");
}

// ============================================================================
// 主练习函数
// ============================================================================

/**
 * @brief 第4章所有练习的入口函数
 */
void chapter4_practice_all(void)
{
    printf("\r\n");
    printf("========================================\r\n");
    printf("    第4章：串口通信与环形缓冲区实现\r\n");
    printf("           实践练习开始\r\n");
    printf("========================================\r\n");
    
    practice_uart_config_demo();
    printf("\r\n");
    
    practice_baudrate_config_demo();
    printf("\r\n");
    
    practice_dma_config_demo();
    printf("\r\n");
    
    practice_dma_transfer_demo();
    printf("\r\n");
    
    practice_simple_ringbuffer_demo();
    printf("\r\n");
    
    practice_rt_ringbuffer_demo();
    printf("\r\n");
    
    practice_command_parser_demo();
    printf("\r\n");
    
    practice_uart_data_flow_demo();
    printf("\r\n");
    
    printf("========================================\r\n");
    printf("           实践练习结束\r\n");
    printf("========================================\r\n");
    printf("\r\n");
}
