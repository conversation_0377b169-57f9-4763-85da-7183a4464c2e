# 电流测量开发说明

## 🔄 已完成的切换

### ✅ 电压测量功能已保存
- **完整的分段校准算法** - 116个数据点，0.00V-10.0V
- **工业级精度** - 精细段±0.001%，粗糙段±0.01%
- **所有代码已备份** - 可随时恢复电压测量功能

### ✅ 已切换到电流测量模式
- **测量通道：** AIN1~GND（电流测量专用）
- **ADC配置：** ±6.144V量程，16位分辨率
- **输出格式：** `current_raw : [原始ADC值]`

## 🔧 当前系统配置

### 1. 采样任务 (sampling_board_app.c)
```c
void sampling_board_task(void)
{
    // 读取AIN1~GND通道的原始数据（用于电流测量）
    float raw_result = GD30AD3344_AD_Read(GD30AD3344_CH_AIN1_GND, GD30AD3344_PGA_6V144);

    // 输出原始数据，用于电流校准数据收集
    my_printf(&huart1, "current_raw : %.4f\r\n", raw_result);
}
```

### 2. 验证命令更新
- **sb read：** 显示当前AIN1~GND原始数据
- **sb check：** 显示电流测量配置信息

### 3. 输出格式
```
current_raw : 0.1234  ← AIN1~GND通道的原始ADC读数
current_raw : 0.1245  ← 每100ms输出一次
current_raw : 0.1238  ← 用于收集校准数据
```

## 📋 数据收集计划

### 需要收集的数据
请按照以下格式提供电流测量数据：

```
输入电流(A) → 串口显示的current_raw值(V)
```

### 建议的测试点
- **0.00A → current_raw : ?????**
- **0.01A → current_raw : ?????**
- **0.05A → current_raw : ?????**
- **0.10A → current_raw : ?????**
- **0.50A → current_raw : ?????**
- **1.00A → current_raw : ?????**
- **更多测试点...**

### 数据收集方法
1. **连接电流源** - 提供已知的精确电流
2. **烧录当前程序** - 观察串口输出
3. **记录对应关系** - 输入电流 vs current_raw值
4. **提供完整数据** - 用于建立电流校准算法

## 🎯 下一步开发计划

### 阶段1：数据收集
- ✅ **切换到AIN1~GND** - 已完成
- ⏳ **收集原始数据** - 等待您提供
- ⏳ **分析数据特性** - 基于实测数据

### 阶段2：校准算法开发
- ⏳ **建立校准表** - 基于您的实测数据
- ⏳ **实现校准算法** - 可能采用分段校准
- ⏳ **集成到系统** - 与电压测量并存

### 阶段3：验证和优化
- ⏳ **精度验证** - 确保测量精度
- ⏳ **性能优化** - 优化算法效率
- ⏳ **功能完善** - 添加验证命令

## 🔧 技术参数

### 硬件配置
- **测量通道：** AIN1~GND
- **ADC范围：** ±6.144V
- **分辨率：** 16位（65536级）
- **采样率：** 10Hz（100ms周期）

### 电流测量原理
- **分流器方案：** 电流 → 电压（通过分流电阻）
- **放大器方案：** 小信号放大后测量
- **霍尔传感器：** 非接触式电流测量
- **具体方案：** 根据您的硬件设计确定

### 预期精度目标
- **低电流段：** ±0.1%（如0-1A）
- **中电流段：** ±0.05%（如1-10A）
- **高电流段：** ±0.01%（如10A以上）

## 📊 与电压测量的对比

| 特性 | 电压测量 | 电流测量 |
|------|----------|----------|
| **通道** | AIN0~GND | AIN1~GND |
| **状态** | ✅ 已完成 | 🔄 开发中 |
| **校准点** | 116个 | 待收集 |
| **精度** | ±0.001% | 待确定 |
| **范围** | 0-10V | 待确定 |

## ⚠️ 重要提示

### 1. 电压测量功能保留
- **所有电压校准代码已保存**
- **可随时切换回电压测量**
- **两种功能可以并存**

### 2. 数据收集要求
- **精确的电流源** - 确保输入电流准确
- **稳定的测试环境** - 避免干扰
- **多个测试点** - 覆盖预期测量范围

### 3. 安全注意事项
- **电流限制** - 不要超过硬件安全范围
- **散热考虑** - 大电流测试时注意散热
- **保护电路** - 确保ADC输入在安全范围内

## 🚀 准备就绪

**系统已切换到电流测量模式，准备收集原始数据！**

### 当前输出
```
current_raw : 0.1234  ← AIN1~GND原始ADC读数
current_raw : 0.1245  ← 每100ms更新一次
current_raw : 0.1238  ← 等待您的测试数据
```

### 下一步操作
1. **烧录当前程序**
2. **连接电流测试设备**
3. **记录 输入电流 vs current_raw 的对应关系**
4. **提供数据给我进行校准算法开发**

**电流测量开发已准备就绪，等待您的测试数据！** 🎯
