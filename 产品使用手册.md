# 嵌入式电压采集系统产品使用手册

## 1. 产品概述

### 1.1 产品简介
本产品是一款基于STM32的高精度电压采集系统，专为嵌入式竞赛设计。系统具备实时电压监测、数据存储、超限报警、数据加密等功能，支持多种采样周期和灵活的配置管理。

### 1.2 主要功能
- **高精度电压采集**：支持0-3.3V电压范围，三级滤波算法确保测量精度
- **实时数据显示**：OLED屏幕实时显示时间和电压值
- **数据存储管理**：支持SD卡和Flash双重存储，四类数据分离管理
- **超限报警功能**：可设置电压阈值，超限时LED报警并记录
- **数据加密传输**：支持HEX格式数据加密输出
- **灵活配置管理**：支持变比、阈值、采样周期等参数配置
- **完善的日志系统**：详细记录系统操作和状态变化

### 1.3 技术规格
- **处理器**：STM32F407VGT6
- **电压测量范围**：0-3.3V
- **测量精度**：±0.01V
- **采样周期**：5s/10s/15s可选
- **存储介质**：SD卡 + 内部Flash
- **通信接口**：UART串口
- **显示屏**：OLED 128×64
- **指示灯**：LED1（采样指示）、LED2（超限报警）

## 2. 硬件连接

### 2.1 接口说明
- **电源接口**：5V DC电源输入
- **串口接口**：UART1（波特率115200）
- **SD卡槽**：支持FAT32格式SD卡
- **电压输入**：ADC输入端子（0-3.3V）
- **按键**：KEY1（采样启停控制）
- **指示灯**：LED1（采样状态）、LED2（超限报警）
- **显示屏**：OLED显示屏

### 2.2 连接步骤
1. **电源连接**：将5V电源适配器连接到电源接口
2. **串口连接**：使用USB转串口线连接UART1接口到PC
3. **SD卡安装**：将格式化的SD卡插入SD卡槽
4. **电压输入**：将待测电压信号连接到ADC输入端子
5. **上电启动**：接通电源，系统自动启动

## 3. 系统操作

### 3.1 系统启动
系统上电后自动执行以下启动序列：
1. **硬件初始化**：初始化各硬件模块
2. **系统自检**：检测Flash、SD卡、RTC等关键硬件
3. **配置加载**：从Flash加载用户配置参数
4. **状态显示**：OLED显示"system idle"表示系统就绪

### 3.2 基本操作

#### 3.2.1 系统自检
**操作方式**：串口命令
**命令格式**：`test` 或 `test simple`
**功能说明**：执行完整的硬件自检，验证系统各模块功能
**输出示例**：
```
======system selftest======
flash.........ok
TF card..........ok
flash ID: 0xEF4015
TF card memory: 7580672 KB
RTC: 2025-01-15 10:30:25
======system selftest======
```

#### 3.2.2 时间设置
**操作方式**：串口命令
**命令格式**：`RTC Config YYYY-MM-DD HH:MM:SS`
**功能说明**：设置系统时间
**使用示例**：
```
RTC Config 2025-01-15 10:30:00
```
**输出示例**：
```
rtc config success to 2025-01-15 10:30:00
```

#### 3.2.3 查看当前时间
**操作方式**：串口命令
**命令格式**：`RTC now`
**功能说明**：显示当前系统时间
**输出示例**：
```
rtc now - current time: 2025-01-15 10:30:25
```

### 3.3 配置管理

#### 3.3.1 参数配置
**变比设置**：
- **命令格式**：`ratio <值>`（范围：0-100）
- **使用示例**：`ratio 2.5`
- **功能说明**：设置电压变比，用于电压值的比例转换

**阈值设置**：
- **命令格式**：`limit <值>`（范围：0-200）
- **使用示例**：`limit 5.0`
- **功能说明**：设置电压超限阈值，超过此值将触发报警

#### 3.3.2 配置保存与读取
**保存配置**：
- **命令格式**：`config save`
- **功能说明**：将当前配置参数保存到Flash和SD卡
- **输出示例**：
```
ratio:2.5
limit:5.00
config save success
```

**读取配置**：
- **命令格式**：`config read`
- **功能说明**：从Flash读取配置参数
- **输出示例**：
```
read parameters from flash
ratio:2.5
limit:5.00
```

**从SD卡读取配置**：
- **命令格式**：`conf`
- **功能说明**：从SD卡config.ini文件读取配置
- **输出示例**：
```
Ratio = 2.5, Limit = 5.00, config read success
```

### 3.4 数据采集

#### 3.4.1 开始采样
**操作方式1**：串口命令
- **命令格式**：`start`
- **功能说明**：开始周期性电压采样

**操作方式2**：按键操作
- **按键**：按下KEY1
- **功能说明**：启动/停止采样切换

**输出示例**：
```
Periodic Sampling
sample cycle: 5s
```

#### 3.4.2 停止采样
**操作方式1**：串口命令
- **命令格式**：`stop`

**操作方式2**：按键操作
- **按键**：再次按下KEY1

**输出示例**：
```
Periodic Sampling STOP
```

#### 3.4.3 采样数据输出
**正常模式输出格式**：
```
2025-01-15 10:30:25 ch0=2.45V
2025-01-15 10:30:30 ch0=2.48V
2025-01-15 10:30:35 ch0=2.52V OverLimit(2.50)!
```

**加密模式输出格式**：
```
67890ABC12345678
67890DEF12345679*
```
*注：末尾的*号表示该数据超限*

### 3.5 数据加密

#### 3.5.1 启用加密
**命令格式**：`hide`
**功能说明**：启用数据加密模式，输出HEX格式数据
**输出示例**：
```
data encryption enabled
```

#### 3.5.2 禁用加密
**命令格式**：`unhide`
**功能说明**：禁用数据加密模式，恢复正常输出
**输出示例**：
```
data encryption disabled
```

## 4. 界面显示

### 4.1 OLED显示屏
**空闲状态显示**：
```
    system idle
```

**采样状态显示**：
```
   10:30:25
   
    2.45V
```
*第一行显示当前时间，第二行显示实时电压值*

### 4.2 LED指示灯
**LED1（采样指示）**：
- **熄灭**：系统空闲状态
- **闪烁**：正在进行周期性采样（1秒闪烁一次）

**LED2（超限报警）**：
- **熄灭**：电压正常
- **常亮**：电压超过设定阈值

## 5. 数据存储

### 5.1 存储结构
系统在SD卡中创建以下目录结构：
```
SD卡根目录/
├── sample/          # 正常采样数据
│   ├── sample0.txt
│   ├── sample1.txt
│   └── ...
├── overLimit/       # 超限数据
│   ├── overLimit0.txt
│   ├── overLimit1.txt
│   └── ...
├── log/            # 系统日志
│   ├── log0.txt
│   ├── log1.txt
│   └── ...
├── hideData/       # 加密数据
│   ├── hideData0.txt
│   ├── hideData1.txt
│   └── ...
└── config.ini      # 配置文件
```

### 5.2 文件内容格式
**采样数据文件（sample/*.txt）**：
```
2025-01-15 10:30:25 ch0=2.45V
2025-01-15 10:30:30 ch0=2.48V
2025-01-15 10:30:35 ch0=2.50V
...
```

**超限数据文件（overLimit/*.txt）**：
```
2025-01-15 10:30:40 ch0=2.55V OverLimit(2.50)!
2025-01-15 10:30:45 ch0=2.58V OverLimit(2.50)!
...
```

**系统日志文件（log/*.txt）**：
```
system init
rtc config success to 2025-01-15 10:30:00
system hardware test
test ok
sample start - cycle 5s (command)
...
```

**配置文件（config.ini）**：
```
[Ratio]
Ch0 = 2.5

[Limit]
Ch0 = 5.00
```

### 5.3 文件管理
- **自动切换**：每个文件最多存储10条记录，满后自动创建新文件
- **断电保护**：系统重启后自动恢复到未满的文件继续写入
- **双重备份**：重要数据同时保存在SD卡和Flash中

## 6. 系统维护

### 6.1 状态监控
**系统状态查询**：
- **命令格式**：`system status`
- **功能说明**：显示系统运行状态、硬件状态等信息

**详细状态诊断**：
- **命令格式**：`debug state`
- **功能说明**：输出详细的系统状态信息，用于故障诊断

### 6.2 故障处理
**SD卡故障恢复**：
- **命令格式**：`recover sd`
- **功能说明**：自动检测并修复SD卡故障

**状态一致性检查**：
- **命令格式**：`check state`
- **功能说明**：检查系统状态一致性并自动修复

**文件系统同步**：
- **命令格式**：`sd sync`
- **功能说明**：强制同步文件系统，确保数据完整性

### 6.3 系统重置
**重置启动计数**：
- **命令格式**：`reset boot`
- **功能说明**：重置系统启动次数计数器

**重置测试阶段**：
- **命令格式**：`reset stage`
- **功能说明**：重置测试阶段状态

## 7. 常见问题

### 7.1 系统无法启动
**可能原因**：电源供电不足、硬件连接错误
**解决方法**：
1. 检查电源适配器是否正常工作
2. 确认所有硬件连接正确
3. 执行系统自检：`test`

### 7.2 SD卡无法识别
**可能原因**：SD卡格式不正确、接触不良
**解决方法**：
1. 确保SD卡格式为FAT32
2. 重新插拔SD卡
3. 执行SD卡检测：`sd detect`
4. 必要时格式化SD卡：`sd format`

### 7.3 时间显示错误
**可能原因**：RTC时间未设置或电池电量不足
**解决方法**：
1. 重新设置时间：`RTC Config 2025-01-15 10:30:00`
2. 检查RTC电池电量
3. 执行RTC检测：`test`

### 7.4 数据丢失
**可能原因**：SD卡故障、系统异常断电
**解决方法**：
1. 检查Flash缓存：系统会自动从Flash恢复数据
2. 执行SD卡恢复：`recover sd`
3. 检查文件完整性：`log validate`

## 8. 技术支持

### 8.1 联系方式
- **技术支持邮箱**：<EMAIL>
- **用户手册版本**：v1.0
- **最后更新时间**：2025-01-15

### 8.2 更新说明
本手册基于系统固件版本v1.0编写，如有功能更新，请及时获取最新版本的使用手册。

---
**注意事项**：
1. 请在操作前仔细阅读本手册
2. 系统运行时请勿随意断电
3. 定期备份重要数据
4. 如遇问题请及时联系技术支持
