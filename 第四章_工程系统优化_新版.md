# 第四章 工程系统优化

## 4.1 系统可靠性优化

### 4.1.1 分层检测策略

系统采用分层检测策略确保运行可靠性，包括物理通信层和功能逻辑层双重验证：

**物理通信层检测**
- Flash硬件ID验证：通过读取Flash芯片ID确认硬件连接正常
- SD卡状态检测：采用多重重试机制，包括硬件初始化、卡状态检查、文件系统挂载
- RTC时钟验证：检查时间数据有效性，确保时间戳准确性

**功能逻辑层检测**
- 数据完整性验证：通过校验和机制确保Flash存储数据完整性
- 文件系统一致性：检查文件记录数量与管理器状态一致性
- 配置参数有效性：验证变比和阈值参数范围，防止无效配置

### 4.1.2 自动重新初始化机制

针对SD卡等外设的不稳定性，系统实现智能重新初始化：

```c
// 智能SD卡重新初始化流程
for (retry_count = 0; retry_count < MAX_RETRY_COUNT; retry_count++) {
    // 硬件重新初始化
    HAL_SD_Init(&hsd);
    // 状态检查
    card_state = HAL_SD_GetCardState(&hsd);
    // 文件系统挂载
    res = f_mount(&g_fs, "0:", 1);
    if (res == FR_OK) break;
}
```

### 4.1.3 容错处理机制

**Flash缓存备份**
- 第一次上电无SD卡时，将日志缓存到Flash中
- SD卡插入后自动恢复缓存数据，确保数据不丢失
- 采用魔数标识和校验和验证缓存数据完整性

**文件管理容错**
- 系统重启后自动恢复文件管理器状态
- 查找现有未满文件继续使用，避免数据碎片化
- 文件损坏时自动创建新文件，保证数据存储连续性

## 4.2 系统一致性优化

### 4.2.1 状态机设计

**采样控制状态机**
系统采用明确的状态机管理采样过程：
- SAMPLING_IDLE：空闲状态，LED关闭，OLED显示"system idle"
- SAMPLING_ACTIVE：采样状态，LED1闪烁，OLED显示时间和电压
- SAMPLING_PAUSED：暂停状态，保持配置但停止数据输出

**测试阶段状态机**
实现四阶段测试流程管理：
- Stage 0 (log0)：RTC配置和首次测试阶段
- Stage 1 (log1)：数据采集操作阶段  
- Stage 2 (log2)：超限操作阶段
- Stage 3 (log3)：数据加密操作阶段

### 4.2.2 数据流一致性

**统一数据结构**
采用结构化数据管理确保一致性：

```c
typedef struct {
    sampling_state_t state;         // 采样状态
    sampling_cycle_t cycle;         // 采样周期
    float processed_voltage;        // 处理后电压值
    uint8_t over_limit;            // 超限标志
} adc_control_t;
```

**配置参数同步**
- Flash和SD卡双重存储配置参数
- 参数修改时同步更新所有存储位置
- 启动时优先从Flash加载，确保配置一致性

### 4.2.3 时间同步机制

**RTC时间管理**
- 采用LSE 32.768KHz晶振提供1Hz精度
- 实现三层初始化检查：备份域标记、时间有效性、智能决策
- 支持时区校正，确保时间戳准确性

**文件命名一致性**
- 采用统一的时间戳格式：YYYYMMDDHHMMSS
- log文件基于启动次数命名，其他文件基于创建时间命名
- 确保文件名唯一性和可追溯性

## 4.3 性能优化策略

### 4.3.1 数据处理优化

**多级滤波算法**
实现双重滤波提高数据质量：

```c
// 中值滤波去除异常值
float median_filtered = voltage_median_filter(raw_voltage);
// 滑动平均滤波平滑显示
voltage = voltage_sliding_average_filter(median_filtered);
```

**DMA数据采集**
- 采用DMA方式进行ADC数据采集，减少CPU占用
- 32点采样缓冲区提供足够的数据平均精度
- 异步数据处理避免阻塞主循环

### 4.3.2 存储性能优化

**文件管理优化**
- 预分配文件管理器，避免运行时内存分配
- 批量写入减少文件系统操作次数
- 智能文件切换，仅在必要时创建新文件

**Flash操作优化**
- 采用扇区对齐的直接Flash操作
- 减少擦除次数，延长Flash使用寿命
- 关键数据立即写入，非关键数据批量写入

### 4.3.3 任务调度优化

**分层任务调度**
采用基于优先级的任务调度：

```c
static task_t scheduler_task[] = {
    {led_task,           1,    0},    // 高频LED控制
    {btn_task,           5,    0},    // 按键检测
    {uart_task,          5,    0},    // 串口处理
    {adc_task,           100,  0},    // ADC数据处理
    {adc_led1_blink_task, 1000, 0},  // LED闪烁控制
    {oled_task,          100, 0}     // OLED显示更新
};
```

**非阻塞设计**
- 所有任务采用非阻塞设计，避免系统卡死
- 使用状态机替代延时等待
- 异步错误处理，不影响主要功能

## 4.4 模块化架构优化

### 4.4.1 接口标准化

**统一错误处理**
定义标准化错误码和处理流程：

```c
typedef enum {
    FLASH_OK = 0,
    FLASH_ERROR_INIT,
    FLASH_ERROR_MOUNT,
    FLASH_ERROR_READ,
    FLASH_ERROR_WRITE
} flash_result_t;
```

**标准化API接口**
- 所有模块提供init、process、check接口
- 统一的参数验证和错误返回机制
- 清晰的模块间依赖关系定义

### 4.4.2 配置集中管理

**全局配置结构**
采用统一的配置管理：

```c
typedef struct {
    float ratio;                    // 变比参数
    float limit;                    // 阈值参数  
    sampling_cycle_t sample_cycle;  // 采样周期
} config_params_t;
```

**配置持久化**
- Flash主存储，SD卡备份存储
- 配置修改时同步更新多个存储位置
- 启动时智能加载最新有效配置

### 4.4.3 资源管理优化

**内存管理**
- 静态内存分配，避免内存碎片
- 环形缓冲区管理串口数据
- 预分配数据结构，提高实时性

**外设资源管理**
- 统一的外设初始化序列
- 资源冲突检测和仲裁机制
- 外设故障时的降级处理策略

## 4.5 系统监控与诊断

### 4.5.1 实时状态监控

**系统健康检查**
定期检查各模块运行状态：
- Flash健康状态检查
- SD卡连接状态监控
- RTC时钟精度验证
- 配置参数一致性检查

**性能指标监控**
- 任务执行时间统计
- 内存使用情况监控
- 文件系统空间管理
- 数据处理延迟测量

### 4.5.2 故障诊断机制

**分层诊断策略**
- 硬件层：外设连接和功能检测
- 驱动层：驱动程序状态和错误统计
- 应用层：业务逻辑一致性和数据完整性
- 系统层：整体性能和资源使用情况

**自动恢复机制**
- 检测到故障时自动尝试恢复
- 多级恢复策略：软重启、硬重启、降级运行
- 故障记录和统计，便于问题分析

通过以上系统优化策略，确保了工业嵌入式系统在复杂环境下的稳定运行，提高了系统的可靠性、一致性和性能表现，为竞赛要求的各项功能提供了坚实的技术保障。
