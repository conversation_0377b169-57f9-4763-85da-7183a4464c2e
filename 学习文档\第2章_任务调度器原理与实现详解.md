# 第2章：任务调度器原理与实现详解

## 🎯 学习目标
- 深入理解时间片轮转调度器的工作原理
- 掌握task_t结构体的设计思想和使用方法
- 理解HAL_GetTick()时间基准机制
- 学会任务周期配置和优先级设计
- 能够独立实现和优化任务调度器

## 📋 目录
1. [调度器基本概念](#1-调度器基本概念)
2. [task_t结构体深度解析](#2-task_t结构体深度解析)
3. [调度器数据结构设计](#3-调度器数据结构设计)
4. [scheduler_run()执行逻辑](#4-scheduler_run执行逻辑)
5. [时间基准HAL_GetTick()](#5-时间基准hal_gettick)
6. [任务周期设计原理](#6-任务周期设计原理)
7. [调度器优化技巧](#7-调度器优化技巧)
8. [实践练习](#8-实践练习)

---

## 1. 调度器基本概念

### 1.1 什么是任务调度器？
任务调度器是嵌入式系统的"大脑"，负责：
- **任务管理**: 决定何时执行哪个任务
- **时间控制**: 确保任务按指定周期运行
- **资源分配**: 合理分配CPU时间给各个任务

### 1.2 时间片轮转调度原理
```
时间轴: 0ms -----> 1ms -----> 5ms -----> 100ms -----> 1000ms
任务:   LED任务    LED任务    按键任务    ADC任务      LED闪烁任务
       (1ms)     (1ms)     (5ms)      (100ms)     (1000ms)
```

**核心思想：**
- 每个任务都有固定的执行周期
- 调度器不断检查是否到了任务的执行时间
- 到时间就执行，没到时间就跳过

### 1.3 本项目调度器特点
- ✅ **简单高效**: 无需复杂的RTOS
- ✅ **实时性好**: 1ms级别的时间精度
- ✅ **易于理解**: 代码简洁，逻辑清晰
- ✅ **资源占用少**: 适合资源受限的嵌入式系统

---

## 2. task_t结构体深度解析

### 2.1 结构体定义分析
```c
typedef struct {
    void (*task_func)(void);    // 任务函数指针 ⭐
    uint32_t rate_ms;           // 任务执行周期（毫秒）⭐
    uint32_t last_run;          // 上次执行时间戳（毫秒）⭐
} task_t;
```

### 2.2 成员详细解析

#### 2.2.1 任务函数指针 `task_func`
```c
void (*task_func)(void);  // 这是什么意思？
```

**解释：**
- `void (*)()` 是函数指针类型
- `task_func` 是变量名
- 指向一个无参数、无返回值的函数

**实际使用：**
```c
// 定义任务函数
void led_task(void) {
    // LED控制逻辑
}

// 将函数赋值给函数指针
task_t my_task;
my_task.task_func = led_task;  // 函数名就是函数地址

// 调用函数
my_task.task_func();  // 等价于 led_task();
```

#### 2.2.2 执行周期 `rate_ms`
```c
uint32_t rate_ms;  // 单位：毫秒
```

**作用：** 定义任务多久执行一次
- `1` = 每1毫秒执行一次（高频任务）
- `5` = 每5毫秒执行一次（中频任务）
- `100` = 每100毫秒执行一次（低频任务）
- `1000` = 每1秒执行一次（超低频任务）

#### 2.2.3 上次执行时间 `last_run`
```c
uint32_t last_run;  // 记录上次执行的时间戳
```

**作用：** 配合当前时间计算是否该执行任务
```c
uint32_t now_time = HAL_GetTick();  // 获取当前时间
if (now_time >= rate_ms + last_run) {
    // 时间到了，执行任务
    last_run = now_time;  // 更新执行时间
    task_func();          // 执行任务函数
}
```

---

## 3. 调度器数据结构设计

### 3.1 任务数组定义
```c
static task_t scheduler_task[] = {
    {led_task,           1,    0},    // LED任务：1ms周期
    {btn_task,           5,    0},    // 按键任务：5ms周期
    {uart_task,          5,    0},    // 串口任务：5ms周期
    {adc_task,           100,  0},    // ADC任务：100ms周期
    {adc_led1_blink_task, 1000, 0},   // LED闪烁：1000ms周期
    {oled_task,          100, 0}     // OLED任务：100ms周期
};
```

### 3.2 数组初始化详解
```c
{led_task, 1, 0}  // 这行代码的含义：
// led_task     - 函数指针，指向led_task函数
// 1            - 执行周期1毫秒
// 0            - 初始时间戳为0（系统启动时）
```

### 3.3 任务数量自动计算
```c
uint8_t task_num;  // 全局变量，存储任务总数

void scheduler_init(void) {
    // 自动计算任务数量，避免手动维护
    task_num = sizeof(scheduler_task) / sizeof(task_t);
    // sizeof(scheduler_task) = 整个数组的字节数
    // sizeof(task_t) = 单个结构体的字节数
    // 相除得到数组元素个数
}
```

**优势：**
- ✅ 添加新任务时无需修改任务数量
- ✅ 避免数组越界错误
- ✅ 代码维护更简单

---

## 4. scheduler_run()执行逻辑

### 4.1 核心调度算法
```c
void scheduler_run(void)
{
    for (uint8_t i = 0; i < task_num; i++)  // 遍历所有任务
    {
        uint32_t now_time = HAL_GetTick();  // 获取当前时间戳
        
        // 检查是否到了执行时间
        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
        {
            scheduler_task[i].last_run = now_time;    // 更新执行时间
            scheduler_task[i].task_func();            // 执行任务函数
        }
    }
}
```

### 4.2 时间判断逻辑详解
```c
// 关键判断条件
if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)

// 等价于
if (now_time - scheduler_task[i].last_run >= scheduler_task[i].rate_ms)

// 含义：当前时间 - 上次执行时间 >= 执行周期
```

**举例说明：**
```c
// 假设LED任务配置：{led_task, 1, 0}
// 系统启动时：now_time=0, last_run=0, rate_ms=1

// 第1次调用scheduler_run()：now_time=0
// 0 >= 1 + 0 ? 否，不执行

// 第2次调用scheduler_run()：now_time=1
// 1 >= 1 + 0 ? 是，执行led_task()，last_run更新为1

// 第3次调用scheduler_run()：now_time=1
// 1 >= 1 + 1 ? 否，不执行

// 第4次调用scheduler_run()：now_time=2
// 2 >= 1 + 1 ? 是，执行led_task()，last_run更新为2
```

### 4.3 调度器执行流程图
```
开始
  ↓
获取当前时间 now_time = HAL_GetTick()
  ↓
i = 0 (第一个任务)
  ↓
now_time >= rate_ms + last_run ?
  ↓                    ↓
 是                   否
  ↓                    ↓
更新 last_run         跳过任务
  ↓                    ↓
执行 task_func()      ↓
  ↓                    ↓
i++ (下一个任务)  ←——————
  ↓
i < task_num ?
  ↓        ↓
 是       否
  ↓        ↓
 循环     结束
```

---

## 5. 时间基准HAL_GetTick()

### 5.1 HAL_GetTick()工作原理
```c
uint32_t HAL_GetTick(void);  // 返回系统启动后的毫秒数
```

**内部实现：**
- 基于SysTick定时器
- 每1毫秒产生一次中断
- 中断中递增全局计数器
- HAL_GetTick()返回计数器值

### 5.2 SysTick定时器配置
```c
// 在HAL_Init()中自动配置
HAL_SYSTICK_Config(SystemCoreClock / 1000);  // 1ms中断
```

**计算过程：**
```
系统时钟 = 144MHz = 144,000,000 Hz
SysTick重载值 = 144,000,000 / 1000 = 144,000
中断周期 = 144,000 / 144,000,000 = 1ms
```

### 5.3 时间戳溢出处理
```c
// uint32_t最大值：4,294,967,295 毫秒
// 约等于：49.7天

// 溢出后自动回到0，但调度器算法仍然正确
// 因为使用的是时间差值比较，而不是绝对时间
```

---

## 6. 任务周期设计原理

### 6.1 周期分配策略
| 任务类型 | 周期 | 原因 |
|---------|------|------|
| LED控制 | 1ms | 需要快速响应，保证LED显示流畅 |
| 按键检测 | 5ms | 人手按键速度，5ms足够检测 |
| 串口通信 | 5ms | 数据处理及时，避免缓冲区溢出 |
| ADC采样 | 100ms | 数据采集不需要太频繁 |
| OLED显示 | 100ms | 人眼感知，100ms刷新足够 |
| LED闪烁 | 1000ms | 状态指示，1秒闪烁合适 |

### 6.2 周期设计原则
1. **响应性要求**: 越需要快速响应，周期越短
2. **CPU负载**: 周期太短会增加CPU负载
3. **功耗考虑**: 高频任务增加功耗
4. **人机交互**: 考虑人的感知能力

### 6.3 周期冲突处理
```c
// 如果多个任务同时到期，按数组顺序执行
// 优先级 = 数组索引（越小优先级越高）

scheduler_task[0] = {led_task, 1, 0};      // 最高优先级
scheduler_task[1] = {btn_task, 5, 0};      // 第二优先级
// ...
```

---

## 7. 调度器优化技巧

### 7.1 任务执行时间监控
```c
void scheduler_run_with_monitor(void)
{
    for (uint8_t i = 0; i < task_num; i++)
    {
        uint32_t now_time = HAL_GetTick();
        
        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
        {
            uint32_t start_time = HAL_GetTick();  // 记录开始时间
            
            scheduler_task[i].last_run = now_time;
            scheduler_task[i].task_func();
            
            uint32_t end_time = HAL_GetTick();    // 记录结束时间
            uint32_t exec_time = end_time - start_time;  // 计算执行时间
            
            // 检查是否超时
            if (exec_time > scheduler_task[i].rate_ms / 2) {
                printf("警告：任务%d执行时间过长：%dms\r\n", i, exec_time);
            }
        }
    }
}
```

### 7.2 动态任务管理
```c
// 添加任务状态控制
typedef struct {
    void (*task_func)(void);
    uint32_t rate_ms;
    uint32_t last_run;
    uint8_t enabled;        // 任务使能标志
} enhanced_task_t;

// 使能/禁用任务
void task_enable(uint8_t task_id, uint8_t enable) {
    if (task_id < task_num) {
        enhanced_scheduler_task[task_id].enabled = enable;
    }
}
```

---

## 8. 实践练习

### 练习1：理解函数指针
创建自己的任务函数并添加到调度器中。

### 练习2：计算任务执行频率
分析各任务在1秒内的执行次数。

### 练习3：优化任务周期
根据实际需求调整任务周期配置。

---

## 📝 本章小结

通过本章学习，您应该掌握：

✅ **时间片轮转调度原理** - 基于时间的任务切换机制
✅ **task_t结构体设计** - 函数指针、周期、时间戳三要素
✅ **HAL_GetTick()时间基准** - SysTick定时器实现的毫秒计数
✅ **调度算法实现** - 时间比较和任务执行逻辑
✅ **任务周期配置** - 根据需求设计合理的执行频率

**下一章预告：** 我们将学习GPIO控制与LED按键模块的具体实现。

---

## 🔗 相关文件
- `sysFunction/scheduler.h` - 调度器头文件定义
- `sysFunction/scheduler.c` - 调度器实现代码
- `sysFunction/led_app.c` - LED任务示例
