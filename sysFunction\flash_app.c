#include "flash_app.h"
#include "gd25qxx.h"        // GD25QXX SPI Flash驱动
#include "usart_app.h"      // 串口输出支持
#include "string.h"         // 字符串处理
#include "stdio.h"          // 标准输入输出


flash_info_t g_flash_info = {0};


flash_result_t flash_app_init(void)
{

    // 初始化Flash
    spi_flash_init();

    //初始化Flash信息结构体
    g_flash_info.mounted = 1;  // 标记为可用状态
    g_flash_info.total_size = 16 * 1024 * 1024;  // 16MB Flash容量
    g_flash_info.used_size = 0;  // 直接Flash操作模式下不统计使用量
    g_flash_info.free_size = g_flash_info.total_size;
    g_flash_info.block_count = g_flash_info.total_size / 4096;  // 4KB块
    g_flash_info.block_size = 4096;

    return FLASH_OK;
}


flash_result_t flash_mount(void)
{
    // 直接Flash操作模式下无需挂载操作
    g_flash_info.mounted = 1;
    return FLASH_OK;
}


flash_result_t flash_format(void)
{
    // 直接Flash操作模式下无需格式化操作
    return FLASH_OK;
}


flash_result_t flash_get_info(flash_info_t *info)
{
    if (info == NULL) return FLASH_ERROR_READ;

    // 复制全局Flash信息
    *info = g_flash_info;

    return FLASH_OK;
}


void initialize_filesystem(void)
{
    flash_app_init();
}


flash_result_t flash_check_health(void)
{
    return g_flash_info.mounted ? FLASH_OK : FLASH_ERROR_MOUNT;
}


flash_result_t flash_direct_write(uint32_t addr, const void* data, size_t size)
{
    flash_direct_erase_sector(addr);
    spi_flash_buffer_write((uint8_t*)data, addr, (uint16_t)size);
    return FLASH_OK;
}

flash_result_t flash_direct_read(uint32_t addr, void* data, size_t size)
{
    spi_flash_buffer_read((uint8_t*)data, addr, (uint16_t)size);
    return FLASH_OK;
}

flash_result_t flash_direct_erase_sector(uint32_t addr)
{
    // 计算扇区起始地址（4KB对齐）
    uint32_t sector_addr = addr & 0xFFFFF000;  // 4KB扇区对齐

    // 使用GD25QXX驱动擦除扇区
    spi_flash_sector_erase(sector_addr);

    return FLASH_OK;
}
