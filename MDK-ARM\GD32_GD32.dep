Dependencies for Project 'GD32', Target 'GD32': (DO NOT MODIFY !)
CompilerVersion: 6160000::V6.16::ARMCLANG
F (startup_stm32f429xx.s)(0x6899EA46)(--cpu Cortex-M4.fp.sp -g --pd "__MICROLIB SETA 1"

-I.\RTE\_GD32

-IE:\keilc51\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-IE:\keilc51\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

--pd "__UVISION_VERSION SETA 534"

--pd "_RTE_ SETA 1"

--pd "GD32F470 SETA 1"

--pd "_RTE_ SETA 1"

--list startup_stm32f429xx.lst

--xref -o .\startup_stm32f429xx.o

--depend .\startup_stm32f429xx.d)
F (../Core/Src/main.c)(0x6899EA46)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./main.o -MD)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\dma.h)(0x67D552D2)
I (..\FATFS\App\fatfs.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x6800CECF)
I (..\FATFS\Target\ffconf.h)(0x684ED838)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6845932D)
I (E:\keilc51\ARM\ARMCLANG\include\stdlib.h)(0x6035A4A8)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x6800CECF)
I (..\FATFS\Target\sd_diskio.h)(0x6845932D)
I (..\Core\Inc\i2c.h)(0x6829D009)
I (..\Core\Inc\rtc.h)(0x684D5B09)
I (..\Core\Inc\sdio.h)(0x6845932E)
I (..\Core\Inc\spi.h)(0x6896CDEB)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\usart.h)(0x6899EA45)
I (..\Core\Inc\gpio.h)(0x67D552D2)
I (E:\keilc51\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (..\sysFunction\mydefine.h)(0x6899EFFE)
I (E:\keilc51\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdarg.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (..\Components\ringbuffer\ringbuffer.h)(0x6896FAF9)
I (E:\keilc51\ARM\ARMCLANG\include\assert.h)(0x6035A4A6)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x68299A9D)
I (..\Components\GD25QXX\gd25qxx.h)(0x684E4CA3)
I (..\sysFunction\scheduler.h)(0x684EA7D4)
I (..\sysFunction\adc_app.h)(0x6899FFC5)
I (..\sysFunction\sampling_types.h)(0x684F845E)
I (..\sysFunction\gd30ad3344.h)(0x689712DE)
I (..\sysFunction\led_app.h)(0x680CCC02)
I (..\sysFunction\btn_app.h)(0x68977A3D)
I (..\sysFunction\usart_app.h)(0x6899EC00)
I (..\sysFunction\oled_app.h)(0x684D8F64)
I (..\Components\oled\oled.h)(0x60BF6F21)
I (..\sysFunction\flash_app.h)(0x6851752A)
I (..\sysFunction\sd_app.h)(0x68529981)
I (..\Components\GD25QXX\lfs.h)(0x681CBC39)
I (E:\keilc51\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (..\sysFunction\rtc_app.h)(0x689779AD)
I (..\sysFunction\config_app.h)(0x68997A4F)
I (..\sysFunction\sampling_board_app.h)(0x689758E7)
F (../Core/Src/gpio.c)(0x6899EA43)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./gpio.o -MD)
I (..\Core\Inc\gpio.h)(0x67D552D2)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Core/Src/adc.c)(0x6896F8D3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./adc.o -MD)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Core/Src/dma.c)(0x6899EA44)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./dma.o -MD)
I (..\Core\Inc\dma.h)(0x67D552D2)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Core/Src/i2c.c)(0x6829D008)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./i2c.o -MD)
I (..\Core\Inc\i2c.h)(0x6829D009)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Core/Src/rtc.c)(0x6899EA45)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./rtc.o -MD)
I (..\Core\Inc\rtc.h)(0x684D5B09)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Core/Src/sdio.c)(0x6896CDEB)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./sdio.o -MD)
I (..\Core\Inc\sdio.h)(0x6845932E)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Core/Src/spi.c)(0x6896F369)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./spi.o -MD)
I (..\Core\Inc\spi.h)(0x6896CDEB)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Core/Src/tim.c)(0x6815F962)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./tim.o -MD)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Core/Src/usart.c)(0x6899F041)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./usart.o -MD)
I (..\Core\Inc\usart.h)(0x6899EA45)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (..\sysFunction\mydefine.h)(0x6899EFFE)
I (E:\keilc51\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdarg.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdlib.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\rtc.h)(0x684D5B09)
I (..\Core\Inc\i2c.h)(0x6829D009)
I (..\Core\Inc\sdio.h)(0x6845932E)
I (..\Components\ringbuffer\ringbuffer.h)(0x6896FAF9)
I (E:\keilc51\ARM\ARMCLANG\include\assert.h)(0x6035A4A6)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x68299A9D)
I (..\Components\GD25QXX\gd25qxx.h)(0x684E4CA3)
I (..\sysFunction\scheduler.h)(0x684EA7D4)
I (..\sysFunction\adc_app.h)(0x6899FFC5)
I (..\sysFunction\sampling_types.h)(0x684F845E)
I (..\sysFunction\gd30ad3344.h)(0x689712DE)
I (..\sysFunction\led_app.h)(0x680CCC02)
I (..\sysFunction\btn_app.h)(0x68977A3D)
I (..\sysFunction\usart_app.h)(0x6899EC00)
I (..\sysFunction\oled_app.h)(0x684D8F64)
I (..\Components\oled\oled.h)(0x60BF6F21)
I (..\sysFunction\flash_app.h)(0x6851752A)
I (..\sysFunction\sd_app.h)(0x68529981)
I (..\FATFS\App\fatfs.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x6800CECF)
I (..\FATFS\Target\ffconf.h)(0x684ED838)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x6800CECF)
I (..\FATFS\Target\sd_diskio.h)(0x6845932D)
I (..\Components\GD25QXX\lfs.h)(0x681CBC39)
I (E:\keilc51\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (..\sysFunction\rtc_app.h)(0x689779AD)
I (..\sysFunction\config_app.h)(0x68997A4F)
I (..\sysFunction\sampling_board_app.h)(0x689758E7)
F (../Core/Src/stm32f4xx_it.c)(0x6899EA45)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_it.o -MD)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_it.h)(0x6899EA45)
F (../Core/Src/stm32f4xx_hal_msp.c)(0x67D552D4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_hal_msp.o -MD)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.c)(0x6800CEEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_hal_adc.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.c)(0x6800CEEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_hal_adc_ex.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.c)(0x6800CEEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_ll_adc.o -MD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c)(0x6800CEEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_hal_rcc.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c)(0x6800CEEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_hal_rcc_ex.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c)(0x6800CEEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_hal_flash.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c)(0x6800CEEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_hal_flash_ex.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c)(0x6800CEEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_hal_flash_ramfunc.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c)(0x6800CEEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_hal_gpio.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c)(0x6800CEEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_hal_dma_ex.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c)(0x6800CEEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_hal_dma.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c)(0x6800CEEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_hal_pwr.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c)(0x6800CEEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_hal_pwr_ex.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c)(0x6800CEEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_hal_cortex.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c)(0x6800CEEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_hal.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c)(0x6800CEEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_hal_exti.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c)(0x6800CEEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_hal_i2c.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c)(0x6800CEEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_hal_i2c_ex.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rtc.c)(0x6800CEEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_hal_rtc.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rtc_ex.c)(0x6800CEEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_hal_rtc_ex.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_sdmmc.c)(0x6800CEEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_ll_sdmmc.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_sd.c)(0x6800CEEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_hal_sd.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_mmc.c)(0x6800CEEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_hal_mmc.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c)(0x6800CEEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_hal_spi.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c)(0x6800CEEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_hal_tim.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c)(0x6800CEEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_hal_tim_ex.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c)(0x6800CEEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./stm32f4xx_hal_uart.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../Core/Src/system_stm32f4xx.c)(0x67D552C4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./system_stm32f4xx.o -MD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (..\Components\ebtn\ebtn.c)(0x65F9CDEC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./ebtn.o -MD)
I (E:\keilc51\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
I (..\Components\ebtn\ebtn.h)(0x65F9CDEC)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Components\ebtn\bit_array.h)(0x65F9CDEC)
F (..\Components\ringbuffer\ringbuffer.c)(0x680DD84B)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./ringbuffer.o -MD)
I (..\Components\ringbuffer\ringbuffer.h)(0x6896FAF9)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\assert.h)(0x6035A4A6)
I (E:\keilc51\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
F (..\Components\oled\oled.c)(0x6829D30F)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./oled.o -MD)
I (..\Components\oled\oled.h)(0x60BF6F21)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
I (..\Components\oled\oledfont.h)(0x60BF6F21)
I (..\Core\Inc\i2c.h)(0x6829D009)
F (..\Components\GD25QXX\gd25qxx.c)(0x684E6143)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./gd25qxx.o -MD)
I (..\Components\GD25QXX\gd25qxx.h)(0x684E4CA3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
F (..\sysFunction\adc_app.c)(0x689A11F6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./adc_app.o -MD)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (..\sysFunction\adc_app.h)(0x6899FFC5)
I (..\sysFunction\mydefine.h)(0x6899EFFE)
I (E:\keilc51\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdarg.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdlib.h)(0x6035A4A8)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
I (..\Core\Inc\usart.h)(0x6899EA45)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\rtc.h)(0x684D5B09)
I (..\Core\Inc\i2c.h)(0x6829D009)
I (..\Core\Inc\sdio.h)(0x6845932E)
I (..\Components\ringbuffer\ringbuffer.h)(0x6896FAF9)
I (E:\keilc51\ARM\ARMCLANG\include\assert.h)(0x6035A4A6)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x68299A9D)
I (..\Components\GD25QXX\gd25qxx.h)(0x684E4CA3)
I (..\sysFunction\scheduler.h)(0x684EA7D4)
I (..\sysFunction\led_app.h)(0x680CCC02)
I (..\sysFunction\btn_app.h)(0x68977A3D)
I (..\sysFunction\usart_app.h)(0x6899EC00)
I (..\sysFunction\oled_app.h)(0x684D8F64)
I (..\Components\oled\oled.h)(0x60BF6F21)
I (..\sysFunction\flash_app.h)(0x6851752A)
I (..\sysFunction\sd_app.h)(0x68529981)
I (..\FATFS\App\fatfs.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x6800CECF)
I (..\FATFS\Target\ffconf.h)(0x684ED838)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x6800CECF)
I (..\FATFS\Target\sd_diskio.h)(0x6845932D)
I (..\Components\GD25QXX\lfs.h)(0x681CBC39)
I (E:\keilc51\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (..\sysFunction\rtc_app.h)(0x689779AD)
I (..\sysFunction\config_app.h)(0x68997A4F)
I (..\sysFunction\sampling_types.h)(0x684F845E)
I (..\sysFunction\gd30ad3344.h)(0x689712DE)
I (..\sysFunction\sampling_board_app.h)(0x689758E7)
F (..\sysFunction\btn_app.c)(0x689A1B5C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./btn_app.o -MD)
I (..\sysFunction\btn_app.h)(0x68977A3D)
I (..\sysFunction\mydefine.h)(0x6899EFFE)
I (E:\keilc51\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdarg.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdlib.h)(0x6035A4A8)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
I (..\Core\Inc\usart.h)(0x6899EA45)
I (E:\keilc51\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\rtc.h)(0x684D5B09)
I (..\Core\Inc\i2c.h)(0x6829D009)
I (..\Core\Inc\sdio.h)(0x6845932E)
I (..\Components\ringbuffer\ringbuffer.h)(0x6896FAF9)
I (E:\keilc51\ARM\ARMCLANG\include\assert.h)(0x6035A4A6)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x68299A9D)
I (..\Components\GD25QXX\gd25qxx.h)(0x684E4CA3)
I (..\sysFunction\scheduler.h)(0x684EA7D4)
I (..\sysFunction\adc_app.h)(0x6899FFC5)
I (..\sysFunction\sampling_types.h)(0x684F845E)
I (..\sysFunction\gd30ad3344.h)(0x689712DE)
I (..\sysFunction\led_app.h)(0x680CCC02)
I (..\sysFunction\usart_app.h)(0x6899EC00)
I (..\sysFunction\oled_app.h)(0x684D8F64)
I (..\Components\oled\oled.h)(0x60BF6F21)
I (..\sysFunction\flash_app.h)(0x6851752A)
I (..\sysFunction\sd_app.h)(0x68529981)
I (..\FATFS\App\fatfs.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x6800CECF)
I (..\FATFS\Target\ffconf.h)(0x684ED838)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x6800CECF)
I (..\FATFS\Target\sd_diskio.h)(0x6845932D)
I (..\Components\GD25QXX\lfs.h)(0x681CBC39)
I (E:\keilc51\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (..\sysFunction\rtc_app.h)(0x689779AD)
I (..\sysFunction\config_app.h)(0x68997A4F)
I (..\sysFunction\sampling_board_app.h)(0x689758E7)
I (..\Components\ebtn\ebtn.h)(0x65F9CDEC)
I (..\Components\ebtn\bit_array.h)(0x65F9CDEC)
I (..\Core\Inc\gpio.h)(0x67D552D2)
F (..\sysFunction\flash_app.c)(0x685158A2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./flash_app.o -MD)
I (..\sysFunction\flash_app.h)(0x6851752A)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Components\GD25QXX\gd25qxx.h)(0x684E4CA3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
I (..\sysFunction\usart_app.h)(0x6899EC00)
I (..\sysFunction\mydefine.h)(0x6899EFFE)
I (E:\keilc51\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdarg.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdlib.h)(0x6035A4A8)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Core\Inc\usart.h)(0x6899EA45)
I (E:\keilc51\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\rtc.h)(0x684D5B09)
I (..\Core\Inc\i2c.h)(0x6829D009)
I (..\Core\Inc\sdio.h)(0x6845932E)
I (..\Components\ringbuffer\ringbuffer.h)(0x6896FAF9)
I (E:\keilc51\ARM\ARMCLANG\include\assert.h)(0x6035A4A6)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x68299A9D)
I (..\sysFunction\scheduler.h)(0x684EA7D4)
I (..\sysFunction\adc_app.h)(0x6899FFC5)
I (..\sysFunction\sampling_types.h)(0x684F845E)
I (..\sysFunction\gd30ad3344.h)(0x689712DE)
I (..\sysFunction\led_app.h)(0x680CCC02)
I (..\sysFunction\btn_app.h)(0x68977A3D)
I (..\sysFunction\oled_app.h)(0x684D8F64)
I (..\Components\oled\oled.h)(0x60BF6F21)
I (..\sysFunction\sd_app.h)(0x68529981)
I (..\FATFS\App\fatfs.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x6800CECF)
I (..\FATFS\Target\ffconf.h)(0x684ED838)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x6800CECF)
I (..\FATFS\Target\sd_diskio.h)(0x6845932D)
I (..\Components\GD25QXX\lfs.h)(0x681CBC39)
I (E:\keilc51\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (..\sysFunction\rtc_app.h)(0x689779AD)
I (..\sysFunction\config_app.h)(0x68997A4F)
I (..\sysFunction\sampling_board_app.h)(0x689758E7)
F (..\sysFunction\led_app.c)(0x684EB912)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./led_app.o -MD)
I (..\sysFunction\led_app.h)(0x680CCC02)
I (..\sysFunction\mydefine.h)(0x6899EFFE)
I (E:\keilc51\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdarg.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdlib.h)(0x6035A4A8)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
I (..\Core\Inc\usart.h)(0x6899EA45)
I (E:\keilc51\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\rtc.h)(0x684D5B09)
I (..\Core\Inc\i2c.h)(0x6829D009)
I (..\Core\Inc\sdio.h)(0x6845932E)
I (..\Components\ringbuffer\ringbuffer.h)(0x6896FAF9)
I (E:\keilc51\ARM\ARMCLANG\include\assert.h)(0x6035A4A6)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x68299A9D)
I (..\Components\GD25QXX\gd25qxx.h)(0x684E4CA3)
I (..\sysFunction\scheduler.h)(0x684EA7D4)
I (..\sysFunction\adc_app.h)(0x6899FFC5)
I (..\sysFunction\sampling_types.h)(0x684F845E)
I (..\sysFunction\gd30ad3344.h)(0x689712DE)
I (..\sysFunction\btn_app.h)(0x68977A3D)
I (..\sysFunction\usart_app.h)(0x6899EC00)
I (..\sysFunction\oled_app.h)(0x684D8F64)
I (..\Components\oled\oled.h)(0x60BF6F21)
I (..\sysFunction\flash_app.h)(0x6851752A)
I (..\sysFunction\sd_app.h)(0x68529981)
I (..\FATFS\App\fatfs.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x6800CECF)
I (..\FATFS\Target\ffconf.h)(0x684ED838)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x6800CECF)
I (..\FATFS\Target\sd_diskio.h)(0x6845932D)
I (..\Components\GD25QXX\lfs.h)(0x681CBC39)
I (E:\keilc51\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (..\sysFunction\rtc_app.h)(0x689779AD)
I (..\sysFunction\config_app.h)(0x68997A4F)
I (..\sysFunction\sampling_board_app.h)(0x689758E7)
I (..\Core\Inc\gpio.h)(0x67D552D2)
F (..\sysFunction\oled_app.c)(0x68977AF2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./oled_app.o -MD)
I (..\sysFunction\oled_app.h)(0x684D8F64)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stdarg.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (..\Components\oled\oled.h)(0x60BF6F21)
I (..\sysFunction\adc_app.h)(0x6899FFC5)
I (..\sysFunction\mydefine.h)(0x6899EFFE)
I (E:\keilc51\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdlib.h)(0x6035A4A8)
I (..\Core\Inc\usart.h)(0x6899EA45)
I (E:\keilc51\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\rtc.h)(0x684D5B09)
I (..\Core\Inc\i2c.h)(0x6829D009)
I (..\Core\Inc\sdio.h)(0x6845932E)
I (..\Components\ringbuffer\ringbuffer.h)(0x6896FAF9)
I (E:\keilc51\ARM\ARMCLANG\include\assert.h)(0x6035A4A6)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x68299A9D)
I (..\Components\GD25QXX\gd25qxx.h)(0x684E4CA3)
I (..\sysFunction\scheduler.h)(0x684EA7D4)
I (..\sysFunction\led_app.h)(0x680CCC02)
I (..\sysFunction\btn_app.h)(0x68977A3D)
I (..\sysFunction\usart_app.h)(0x6899EC00)
I (..\sysFunction\flash_app.h)(0x6851752A)
I (..\sysFunction\sd_app.h)(0x68529981)
I (..\FATFS\App\fatfs.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x6800CECF)
I (..\FATFS\Target\ffconf.h)(0x684ED838)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x6800CECF)
I (..\FATFS\Target\sd_diskio.h)(0x6845932D)
I (..\Components\GD25QXX\lfs.h)(0x681CBC39)
I (E:\keilc51\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (..\sysFunction\rtc_app.h)(0x689779AD)
I (..\sysFunction\config_app.h)(0x68997A4F)
I (..\sysFunction\sampling_types.h)(0x684F845E)
I (..\sysFunction\gd30ad3344.h)(0x689712DE)
I (..\sysFunction\sampling_board_app.h)(0x689758E7)
F (..\sysFunction\scheduler.c)(0x6899FFEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./scheduler.o -MD)
I (..\sysFunction\scheduler.h)(0x684EA7D4)
I (..\sysFunction\mydefine.h)(0x6899EFFE)
I (E:\keilc51\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdarg.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdlib.h)(0x6035A4A8)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
I (..\Core\Inc\usart.h)(0x6899EA45)
I (E:\keilc51\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\rtc.h)(0x684D5B09)
I (..\Core\Inc\i2c.h)(0x6829D009)
I (..\Core\Inc\sdio.h)(0x6845932E)
I (..\Components\ringbuffer\ringbuffer.h)(0x6896FAF9)
I (E:\keilc51\ARM\ARMCLANG\include\assert.h)(0x6035A4A6)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x68299A9D)
I (..\Components\GD25QXX\gd25qxx.h)(0x684E4CA3)
I (..\sysFunction\adc_app.h)(0x6899FFC5)
I (..\sysFunction\sampling_types.h)(0x684F845E)
I (..\sysFunction\gd30ad3344.h)(0x689712DE)
I (..\sysFunction\led_app.h)(0x680CCC02)
I (..\sysFunction\btn_app.h)(0x68977A3D)
I (..\sysFunction\usart_app.h)(0x6899EC00)
I (..\sysFunction\oled_app.h)(0x684D8F64)
I (..\Components\oled\oled.h)(0x60BF6F21)
I (..\sysFunction\flash_app.h)(0x6851752A)
I (..\sysFunction\sd_app.h)(0x68529981)
I (..\FATFS\App\fatfs.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x6800CECF)
I (..\FATFS\Target\ffconf.h)(0x684ED838)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x6800CECF)
I (..\FATFS\Target\sd_diskio.h)(0x6845932D)
I (..\Components\GD25QXX\lfs.h)(0x681CBC39)
I (E:\keilc51\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (..\sysFunction\rtc_app.h)(0x689779AD)
I (..\sysFunction\config_app.h)(0x68997A4F)
I (..\sysFunction\sampling_board_app.h)(0x689758E7)
I (..\sysFunction\selftest_app.h)(0x68977EBF)
F (..\sysFunction\usart_app.c)(0x689A036D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./usart_app.o -MD)
I (..\sysFunction\usart_app.h)(0x6899EC00)
I (..\sysFunction\mydefine.h)(0x6899EFFE)
I (E:\keilc51\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdarg.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdlib.h)(0x6035A4A8)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
I (..\Core\Inc\usart.h)(0x6899EA45)
I (E:\keilc51\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\rtc.h)(0x684D5B09)
I (..\Core\Inc\i2c.h)(0x6829D009)
I (..\Core\Inc\sdio.h)(0x6845932E)
I (..\Components\ringbuffer\ringbuffer.h)(0x6896FAF9)
I (E:\keilc51\ARM\ARMCLANG\include\assert.h)(0x6035A4A6)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x68299A9D)
I (..\Components\GD25QXX\gd25qxx.h)(0x684E4CA3)
I (..\sysFunction\scheduler.h)(0x684EA7D4)
I (..\sysFunction\adc_app.h)(0x6899FFC5)
I (..\sysFunction\sampling_types.h)(0x684F845E)
I (..\sysFunction\gd30ad3344.h)(0x689712DE)
I (..\sysFunction\led_app.h)(0x680CCC02)
I (..\sysFunction\btn_app.h)(0x68977A3D)
I (..\sysFunction\oled_app.h)(0x684D8F64)
I (..\Components\oled\oled.h)(0x60BF6F21)
I (..\sysFunction\flash_app.h)(0x6851752A)
I (..\sysFunction\sd_app.h)(0x68529981)
I (..\FATFS\App\fatfs.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x6800CECF)
I (..\FATFS\Target\ffconf.h)(0x684ED838)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x6800CECF)
I (..\FATFS\Target\sd_diskio.h)(0x6845932D)
I (..\Components\GD25QXX\lfs.h)(0x681CBC39)
I (E:\keilc51\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (..\sysFunction\rtc_app.h)(0x689779AD)
I (..\sysFunction\config_app.h)(0x68997A4F)
I (..\sysFunction\sampling_board_app.h)(0x689758E7)
I (..\sysFunction\selftest_app.h)(0x68977EBF)
I (..\sysFunction\binary_protocol.h)(0x6898AA91)
I (..\Core\Inc\gpio.h)(0x67D552D2)
I (..\sysFunction\binary_protocol.c)(0x6898B466)
F (..\sysFunction\rtc_app.c)(0x684FB655)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./rtc_app.o -MD)
I (..\sysFunction\rtc_app.h)(0x689779AD)
I (..\sysFunction\mydefine.h)(0x6899EFFE)
I (E:\keilc51\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdarg.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdlib.h)(0x6035A4A8)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
I (..\Core\Inc\usart.h)(0x6899EA45)
I (E:\keilc51\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\rtc.h)(0x684D5B09)
I (..\Core\Inc\i2c.h)(0x6829D009)
I (..\Core\Inc\sdio.h)(0x6845932E)
I (..\Components\ringbuffer\ringbuffer.h)(0x6896FAF9)
I (E:\keilc51\ARM\ARMCLANG\include\assert.h)(0x6035A4A6)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x68299A9D)
I (..\Components\GD25QXX\gd25qxx.h)(0x684E4CA3)
I (..\sysFunction\scheduler.h)(0x684EA7D4)
I (..\sysFunction\adc_app.h)(0x6899FFC5)
I (..\sysFunction\sampling_types.h)(0x684F845E)
I (..\sysFunction\gd30ad3344.h)(0x689712DE)
I (..\sysFunction\led_app.h)(0x680CCC02)
I (..\sysFunction\btn_app.h)(0x68977A3D)
I (..\sysFunction\usart_app.h)(0x6899EC00)
I (..\sysFunction\oled_app.h)(0x684D8F64)
I (..\Components\oled\oled.h)(0x60BF6F21)
I (..\sysFunction\flash_app.h)(0x6851752A)
I (..\sysFunction\sd_app.h)(0x68529981)
I (..\FATFS\App\fatfs.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x6800CECF)
I (..\FATFS\Target\ffconf.h)(0x684ED838)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x6800CECF)
I (..\FATFS\Target\sd_diskio.h)(0x6845932D)
I (..\Components\GD25QXX\lfs.h)(0x681CBC39)
I (E:\keilc51\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (..\sysFunction\config_app.h)(0x68997A4F)
I (..\sysFunction\sampling_board_app.h)(0x689758E7)
F (..\sysFunction\mydefine.h)(0x6899EFFE)()
F (..\sysFunction\config_app.c)(0x6899823B)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./config_app.o -MD)
I (..\sysFunction\config_app.h)(0x68997A4F)
I (..\sysFunction\mydefine.h)(0x6899EFFE)
I (E:\keilc51\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdarg.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdlib.h)(0x6035A4A8)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
I (..\Core\Inc\usart.h)(0x6899EA45)
I (E:\keilc51\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\rtc.h)(0x684D5B09)
I (..\Core\Inc\i2c.h)(0x6829D009)
I (..\Core\Inc\sdio.h)(0x6845932E)
I (..\Components\ringbuffer\ringbuffer.h)(0x6896FAF9)
I (E:\keilc51\ARM\ARMCLANG\include\assert.h)(0x6035A4A6)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x68299A9D)
I (..\Components\GD25QXX\gd25qxx.h)(0x684E4CA3)
I (..\sysFunction\scheduler.h)(0x684EA7D4)
I (..\sysFunction\adc_app.h)(0x6899FFC5)
I (..\sysFunction\sampling_types.h)(0x684F845E)
I (..\sysFunction\gd30ad3344.h)(0x689712DE)
I (..\sysFunction\led_app.h)(0x680CCC02)
I (..\sysFunction\btn_app.h)(0x68977A3D)
I (..\sysFunction\usart_app.h)(0x6899EC00)
I (..\sysFunction\oled_app.h)(0x684D8F64)
I (..\Components\oled\oled.h)(0x60BF6F21)
I (..\sysFunction\flash_app.h)(0x6851752A)
I (..\sysFunction\sd_app.h)(0x68529981)
I (..\FATFS\App\fatfs.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x6800CECF)
I (..\FATFS\Target\ffconf.h)(0x684ED838)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x6800CECF)
I (..\FATFS\Target\sd_diskio.h)(0x6845932D)
I (..\Components\GD25QXX\lfs.h)(0x681CBC39)
I (E:\keilc51\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (..\sysFunction\rtc_app.h)(0x689779AD)
I (..\sysFunction\sampling_board_app.h)(0x689758E7)
F (..\sysFunction\selftest_app.c)(0x689774C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./selftest_app.o -MD)
I (..\sysFunction\selftest_app.h)(0x68977EBF)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
I (..\sysFunction\mydefine.h)(0x6899EFFE)
I (E:\keilc51\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdarg.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdlib.h)(0x6035A4A8)
I (..\Core\Inc\usart.h)(0x6899EA45)
I (E:\keilc51\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\rtc.h)(0x684D5B09)
I (..\Core\Inc\i2c.h)(0x6829D009)
I (..\Core\Inc\sdio.h)(0x6845932E)
I (..\Components\ringbuffer\ringbuffer.h)(0x6896FAF9)
I (E:\keilc51\ARM\ARMCLANG\include\assert.h)(0x6035A4A6)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x68299A9D)
I (..\Components\GD25QXX\gd25qxx.h)(0x684E4CA3)
I (..\sysFunction\scheduler.h)(0x684EA7D4)
I (..\sysFunction\adc_app.h)(0x6899FFC5)
I (..\sysFunction\sampling_types.h)(0x684F845E)
I (..\sysFunction\gd30ad3344.h)(0x689712DE)
I (..\sysFunction\led_app.h)(0x680CCC02)
I (..\sysFunction\btn_app.h)(0x68977A3D)
I (..\sysFunction\usart_app.h)(0x6899EC00)
I (..\sysFunction\oled_app.h)(0x684D8F64)
I (..\Components\oled\oled.h)(0x60BF6F21)
I (..\sysFunction\flash_app.h)(0x6851752A)
I (..\sysFunction\sd_app.h)(0x68529981)
I (..\FATFS\App\fatfs.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x6800CECF)
I (..\FATFS\Target\ffconf.h)(0x684ED838)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x6800CECF)
I (..\FATFS\Target\sd_diskio.h)(0x6845932D)
I (..\Components\GD25QXX\lfs.h)(0x681CBC39)
I (E:\keilc51\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (..\sysFunction\rtc_app.h)(0x689779AD)
I (..\sysFunction\config_app.h)(0x68997A4F)
I (..\sysFunction\sampling_board_app.h)(0x689758E7)
F (..\sysFunction\sd_app.c)(0x68981891)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./sd_app.o -MD)
I (..\sysFunction\sd_app.h)(0x68529981)
I (..\sysFunction\mydefine.h)(0x6899EFFE)
I (E:\keilc51\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdarg.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdlib.h)(0x6035A4A8)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
I (..\Core\Inc\usart.h)(0x6899EA45)
I (E:\keilc51\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\rtc.h)(0x684D5B09)
I (..\Core\Inc\i2c.h)(0x6829D009)
I (..\Core\Inc\sdio.h)(0x6845932E)
I (..\Components\ringbuffer\ringbuffer.h)(0x6896FAF9)
I (E:\keilc51\ARM\ARMCLANG\include\assert.h)(0x6035A4A6)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x68299A9D)
I (..\Components\GD25QXX\gd25qxx.h)(0x684E4CA3)
I (..\sysFunction\scheduler.h)(0x684EA7D4)
I (..\sysFunction\adc_app.h)(0x6899FFC5)
I (..\sysFunction\sampling_types.h)(0x684F845E)
I (..\sysFunction\gd30ad3344.h)(0x689712DE)
I (..\sysFunction\led_app.h)(0x680CCC02)
I (..\sysFunction\btn_app.h)(0x68977A3D)
I (..\sysFunction\usart_app.h)(0x6899EC00)
I (..\sysFunction\oled_app.h)(0x684D8F64)
I (..\Components\oled\oled.h)(0x60BF6F21)
I (..\sysFunction\flash_app.h)(0x6851752A)
I (..\sysFunction\rtc_app.h)(0x689779AD)
I (..\sysFunction\config_app.h)(0x68997A4F)
I (..\sysFunction\sampling_board_app.h)(0x689758E7)
I (..\FATFS\App\fatfs.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x6800CECF)
I (..\FATFS\Target\ffconf.h)(0x684ED838)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x6800CECF)
I (..\FATFS\Target\sd_diskio.h)(0x6845932D)
I (..\Components\GD25QXX\lfs.h)(0x681CBC39)
I (E:\keilc51\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
F (..\sysFunction\gd30ad3344.c)(0x689A03DE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./gd30ad3344.o -MD)
I (..\sysFunction\gd30ad3344.h)(0x689712DE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\sysFunction\mydefine.h)(0x6899EFFE)
I (E:\keilc51\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdarg.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdlib.h)(0x6035A4A8)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
I (..\Core\Inc\usart.h)(0x6899EA45)
I (E:\keilc51\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\rtc.h)(0x684D5B09)
I (..\Core\Inc\i2c.h)(0x6829D009)
I (..\Core\Inc\sdio.h)(0x6845932E)
I (..\Components\ringbuffer\ringbuffer.h)(0x6896FAF9)
I (E:\keilc51\ARM\ARMCLANG\include\assert.h)(0x6035A4A6)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x68299A9D)
I (..\Components\GD25QXX\gd25qxx.h)(0x684E4CA3)
I (..\sysFunction\scheduler.h)(0x684EA7D4)
I (..\sysFunction\adc_app.h)(0x6899FFC5)
I (..\sysFunction\sampling_types.h)(0x684F845E)
I (..\sysFunction\led_app.h)(0x680CCC02)
I (..\sysFunction\btn_app.h)(0x68977A3D)
I (..\sysFunction\usart_app.h)(0x6899EC00)
I (..\sysFunction\oled_app.h)(0x684D8F64)
I (..\Components\oled\oled.h)(0x60BF6F21)
I (..\sysFunction\flash_app.h)(0x6851752A)
I (..\sysFunction\sd_app.h)(0x68529981)
I (..\FATFS\App\fatfs.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x6800CECF)
I (..\FATFS\Target\ffconf.h)(0x684ED838)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x6800CECF)
I (..\FATFS\Target\sd_diskio.h)(0x6845932D)
I (..\Components\GD25QXX\lfs.h)(0x681CBC39)
I (E:\keilc51\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (..\sysFunction\rtc_app.h)(0x689779AD)
I (..\sysFunction\config_app.h)(0x68997A4F)
I (..\sysFunction\sampling_board_app.h)(0x689758E7)
F (..\sysFunction\sampling_board_app.c)(0x689A1422)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./sampling_board_app.o -MD)
I (..\sysFunction\sampling_board_app.h)(0x689758E7)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\sysFunction\mydefine.h)(0x6899EFFE)
I (E:\keilc51\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdarg.h)(0x6035A4A8)
I (E:\keilc51\ARM\ARMCLANG\include\stdlib.h)(0x6035A4A8)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
I (..\Core\Inc\usart.h)(0x6899EA45)
I (E:\keilc51\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (..\Core\Inc\adc.h)(0x680DAC6E)
I (..\Core\Inc\tim.h)(0x6815F962)
I (..\Core\Inc\rtc.h)(0x684D5B09)
I (..\Core\Inc\i2c.h)(0x6829D009)
I (..\Core\Inc\sdio.h)(0x6845932E)
I (..\Components\ringbuffer\ringbuffer.h)(0x6896FAF9)
I (E:\keilc51\ARM\ARMCLANG\include\assert.h)(0x6035A4A6)
I (..\Middlewares\ST\ARM\DSP\Inc\arm_math.h)(0x68299A9D)
I (..\Components\GD25QXX\gd25qxx.h)(0x684E4CA3)
I (..\sysFunction\scheduler.h)(0x684EA7D4)
I (..\sysFunction\adc_app.h)(0x6899FFC5)
I (..\sysFunction\sampling_types.h)(0x684F845E)
I (..\sysFunction\gd30ad3344.h)(0x689712DE)
I (..\sysFunction\led_app.h)(0x680CCC02)
I (..\sysFunction\btn_app.h)(0x68977A3D)
I (..\sysFunction\usart_app.h)(0x6899EC00)
I (..\sysFunction\oled_app.h)(0x684D8F64)
I (..\Components\oled\oled.h)(0x60BF6F21)
I (..\sysFunction\flash_app.h)(0x6851752A)
I (..\sysFunction\sd_app.h)(0x68529981)
I (..\FATFS\App\fatfs.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x6800CECF)
I (..\FATFS\Target\ffconf.h)(0x684ED838)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x6800CECF)
I (..\FATFS\Target\sd_diskio.h)(0x6845932D)
I (..\Components\GD25QXX\lfs.h)(0x681CBC39)
I (E:\keilc51\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (..\sysFunction\rtc_app.h)(0x689779AD)
I (..\sysFunction\config_app.h)(0x68997A4F)
F (../FATFS/Target/bsp_driver_sd.c)(0x6896CDEA)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./bsp_driver_sd.o -MD)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6845932D)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
F (../FATFS/Target/sd_diskio.c)(0x684ED835)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./sd_diskio.o -MD)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x6800CECF)
I (..\FATFS\Target\ffconf.h)(0x684ED838)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6845932D)
I (E:\keilc51\ARM\ARMCLANG\include\stdlib.h)(0x6035A4A8)
I (..\FATFS\Target\sd_diskio.h)(0x6845932D)
F (../FATFS/App/fatfs.c)(0x6896F8F8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./fatfs.o -MD)
I (..\FATFS\App\fatfs.h)(0x6845932D)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x6800CECF)
I (..\FATFS\Target\ffconf.h)(0x684ED838)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6845932D)
I (E:\keilc51\ARM\ARMCLANG\include\stdlib.h)(0x6035A4A8)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x6800CECF)
I (..\FATFS\Target\sd_diskio.h)(0x6845932D)
F (../Middlewares/Third_Party/FatFs/src/diskio.c)(0x6800CECF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./diskio.o -MD)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x6800CECF)
I (..\FATFS\Target\ffconf.h)(0x684ED838)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6845932D)
I (E:\keilc51\ARM\ARMCLANG\include\stdlib.h)(0x6035A4A8)
F (../Middlewares/Third_Party/FatFs/src/ff.c)(0x6800CECF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./ff.o -MD)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x6800CECF)
I (..\FATFS\Target\ffconf.h)(0x684ED838)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6845932D)
I (E:\keilc51\ARM\ARMCLANG\include\stdlib.h)(0x6035A4A8)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x6800CECF)
I (E:\keilc51\ARM\ARMCLANG\include\stdarg.h)(0x6035A4A8)
F (../Middlewares/Third_Party/FatFs/src/ff_gen_drv.c)(0x6800CECF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./ff_gen_drv.o -MD)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x6800CECF)
I (..\FATFS\Target\ffconf.h)(0x684ED838)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6845932D)
I (E:\keilc51\ARM\ARMCLANG\include\stdlib.h)(0x6035A4A8)
F (../Middlewares/Third_Party/FatFs/src/option/syscall.c)(0x6800CECF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./syscall.o -MD)
I (..\Middlewares\Third_Party\FatFs\src\option\..\ff.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x6800CECF)
I (..\FATFS\Target\ffconf.h)(0x684ED838)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6845932D)
I (E:\keilc51\ARM\ARMCLANG\include\stdlib.h)(0x6035A4A8)
F (../Middlewares/Third_Party/FatFs/src/option/cc936.c)(0x6800CECF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../Components/ebtn -I ../Components/ringbuffer -I ../Middlewares/ST/ARM/DSP/Inc -I ../Components/oled -I ../Components/GD25QXX -I ../FATFS/Target -I ../FATFS/App -I ../Middlewares/Third_Party/FatFs/src -I ../sysFunction

-I./RTE/_GD32

-IE:/keilc51/ARM/CMSIS/5.7.0/CMSIS/Core/Include

-IE:/keilc51/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="534" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o ./cc936.o -MD)
I (..\Middlewares\Third_Party\FatFs\src\option\..\ff.h)(0x6800CECF)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x6800CECF)
I (..\FATFS\Target\ffconf.h)(0x684ED838)
I (..\Core\Inc\main.h)(0x67D552D4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x6800CEEE)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6896CDEB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x6800CEEE)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x6800CECE)
I (E:\keilc51\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x6800CECE)
I (..\Drivers\CMSIS\Include\mpu_armv7.h)(0x6800CECE)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x6800CEEE)
I (E:\keilc51\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rtc_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sd.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_sdmmc.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x6800CEEE)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x6800CEEE)
I (..\FATFS\Target\bsp_driver_sd.h)(0x6845932D)
I (E:\keilc51\ARM\ARMCLANG\include\stdlib.h)(0x6035A4A8)
F (../Middlewares/ST/ARM/DSP/Lib/arm_cortexM4l_math.lib)(0x68299A9D)()
