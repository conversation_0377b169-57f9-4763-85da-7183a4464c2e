# 电压测量专用版本使用说明

## 🎯 功能概述

本版本专门针对电压测量进行了优化，专注于AIN0~GND通道的高精度电压采集，并提供了灵活的校准系数调整功能。

## 📊 主要特性

### 1. 专用电压测量
- **专注通道：** 仅使用CH0（AIN0~GND）进行电压测量
- **高精度：** 16位ADC分辨率，±2.048V测量范围
- **校准功能：** 支持动态校准系数调整

### 2. 校准系数功能
- **默认系数：** 10/3.115 ≈ 3.208（根据您的例程）
- **动态调整：** 可通过串口命令实时调整
- **范围限制：** 0.1 - 100.0（防止异常值）

### 3. 数据处理
- **滤波：** 8点滑动平均滤波
- **有效性检查：** 自动识别无效数据（32767）
- **稳定时间：** 通道切换后2ms稳定时间

## 🔧 串口命令

### 基本命令

#### 1. 开始电压测量
```
sb start
```
开始连续电压测量并输出数据。

**输出示例：**
```
2025-01-15 10:30:25 Voltage=12.3456V (Calibrated)
2025-01-15 10:30:30 Voltage=12.3478V (Calibrated)
```

#### 2. 停止测量
```
sb stop
```
停止连续测量。

#### 3. 读取当前电压
```
sb read
```
立即读取并显示当前电压值。

**输出示例：**
```
=== Voltage Measurement ===
Time: 2025-01-15 10:30:25
Voltage: 12.3456V (Calibrated)
Calibration Factor: 3.2080
===========================
```

### 校准命令

#### 4. 设置校准系数
```
sb cal <系数>
```

**示例：**
```
sb cal 3.208          # 设置系数为3.208
sb cal 3.115          # 设置系数为3.115
sb cal                # 查看当前系数
```

**输出示例：**
```
Voltage calibration factor set to: 3.2080
Formula: Calibrated_Voltage = Raw_Voltage × 3.2080
```

## 📋 校准方法

### 方法1：基于已知电压校准

1. **连接标准电压源**（如精密电源或万用表校准的电压）
2. **读取原始值：**
   ```
   sb read
   ```
3. **计算校准系数：**
   ```
   校准系数 = 实际电压 / 显示电压
   ```
4. **设置新系数：**
   ```
   sb cal <计算出的系数>
   ```

### 方法2：基于分压比校准

如果您使用分压电路：
```
校准系数 = (R1 + R2) / R2
```
其中R1是上拉电阻，R2是下拉电阻。

### 方法3：基于您的例程

根据您提供的例程公式：
```
result * 10/3.115 = result * 3.208
```
所以默认校准系数设置为3.208。

## 🔍 调试信息

### 详细调试输出
系统会输出详细的调试信息：

```
Voltage: Raw=1234, 0.0771V -> 0.2473V (Cal)
```

**含义：**
- **Raw=1234：** ADC原始数值
- **0.0771V：** 原始电压值（未校准）
- **0.2473V：** 校准后电压值

### 错误信息
```
CH0: Invalid data (0x7FFF)
```
表示检测到无效数据，可能原因：
- 输入电压超出±2.048V范围
- 硬件连接问题
- SPI通信错误

## ⚙️ 技术参数

### 测量规格
- **输入范围：** ±2.048V（ADC原生范围）
- **分辨率：** 16位（65536级）
- **精度：** 约0.00006V/LSB（未校准）
- **采样率：** 约20Hz（50ms任务周期）

### 校准范围
- **系数范围：** 0.1 - 100.0
- **默认系数：** 3.208（10/3.115）
- **精度：** 4位小数

### 滤波特性
- **类型：** 8点滑动平均
- **稳定时间：** 约400ms（8个采样周期）
- **噪声抑制：** 有效降低随机噪声

## 🛠️ 硬件连接

### 必需连接
- **AIN0：** 待测电压输入
- **GND：** 公共地线
- **VCC：** 3.3V供电
- **SPI接口：** PA5(SCK), PA6(MISO), PA7(MOSI), PA4(CS)

### 输入保护
建议在AIN0输入端添加：
- **限流电阻：** 1kΩ
- **钳位二极管：** 保护ADC输入
- **滤波电容：** 100nF（可选）

## 📊 使用示例

### 示例1：测量电池电压
```
sb start                    # 开始测量
# 观察输出：Voltage=3.7234V (Calibrated)
sb stop                     # 停止测量
```

### 示例2：校准到万用表读数
```
sb read                     # 当前显示：12.34V
# 万用表读数：12.50V
# 计算：12.50/12.34 = 1.013
sb cal 1.013               # 设置校准系数
sb read                     # 验证：应该接近12.50V
```

### 示例3：分压电路测量
```
# 假设使用10kΩ+3.3kΩ分压测量12V电池
# 分压比：(10k+3.3k)/3.3k = 4.03
sb cal 4.03                # 设置分压系数
sb start                   # 开始测量实际电池电压
```

## ⚠️ 注意事项

1. **电压范围：** 确保输入电压不超过±2.048V
2. **接地：** 确保GND连接良好
3. **稳定性：** 等待滤波稳定（约400ms）
4. **校准：** 定期使用标准电压源校准
5. **保护：** 建议添加输入保护电路

## 🎯 优化建议

1. **提高精度：** 使用精密基准电压源校准
2. **降低噪声：** 添加硬件滤波电路
3. **扩展范围：** 使用精密分压电路
4. **温度补偿：** 考虑温度对精度的影响

**现在可以烧录程序开始精确的电压测量了！**
