/**
 * @file    第6章_实践练习与代码示例.c
 * @brief   RTC时间管理与时间戳处理 - 实践练习代码
 * @details 通过实际代码示例帮助理解RTC配置、时间获取设置、Unix时间戳转换的实现原理
 * <AUTHOR>
 * @date    2025-01-07
 */

#include "main.h"
#include "stdio.h"
#include "string.h"
#include "rtc.h"

// ============================================================================
// 练习1：RTC配置详解
// ============================================================================

/**
 * @brief 演示RTC_HandleTypeDef结构体配置
 */
void practice_rtc_config_demo(void)
{
    printf("=== RTC配置演示 ===\r\n");
    
    // 分析项目中的RTC配置
    extern RTC_HandleTypeDef hrtc;
    
    printf("RTC_HandleTypeDef结构体大小: %d 字节\r\n", sizeof(RTC_HandleTypeDef));
    printf("当前RTC配置:\r\n");
    printf("  外设实例: RTC\r\n");
    printf("  时间格式: %s\r\n", 
           (hrtc.Init.HourFormat == RTC_HOURFORMAT_24) ? "24小时制" : "12小时制");
    printf("  异步预分频: %d\r\n", hrtc.Init.AsynchPrediv);
    printf("  同步预分频: %d\r\n", hrtc.Init.SynchPrediv);
    printf("  输出功能: %s\r\n", 
           (hrtc.Init.OutPut == RTC_OUTPUT_DISABLE) ? "禁用" : "使能");
    printf("  输出极性: %s\r\n", 
           (hrtc.Init.OutPutPolarity == RTC_OUTPUT_POLARITY_HIGH) ? "高电平" : "低电平");
    printf("  输出类型: %s\r\n", 
           (hrtc.Init.OutPutType == RTC_OUTPUT_TYPE_OPENDRAIN) ? "开漏" : "推挽");
    
    // 计算RTC时钟频率
    uint32_t rtc_clock = 32768;  // LSE频率
    uint32_t async_div = hrtc.Init.AsynchPrediv + 1;
    uint32_t sync_div = hrtc.Init.SynchPrediv + 1;
    uint32_t rtc_freq = rtc_clock / (async_div * sync_div);
    
    printf("\nRTC时钟计算:\r\n");
    printf("  LSE时钟源: %d Hz\r\n", rtc_clock);
    printf("  异步分频: %d\r\n", async_div);
    printf("  同步分频: %d\r\n", sync_div);
    printf("  RTC频率: %d Hz\r\n", rtc_freq);
    printf("  时钟周期: %d ms\r\n", 1000 / rtc_freq);
}

/**
 * @brief 演示时钟源选择分析
 */
void practice_clock_source_demo(void)
{
    printf("=== 时钟源选择演示 ===\r\n");
    
    printf("STM32F429 RTC时钟源对比:\r\n");
    printf("时钟源\t频率\t\t精度\t\t功耗\t应用场景\r\n");
    printf("------------------------------------------------------------\r\n");
    printf("LSE\t32.768kHz\t±20ppm\t\t极低\t高精度应用(推荐)\r\n");
    printf("LSI\t~32kHz\t\t±5%%\t\t低\t一般应用\r\n");
    printf("HSE/32\t变化\t\t高\t\t高\t特殊需求\r\n");
    
    printf("\nLSE时钟源优势:\r\n");
    printf("1. 标准32.768kHz晶振，精度高\r\n");
    printf("2. 功耗极低，适合电池供电\r\n");
    printf("3. 温度稳定性好\r\n");
    printf("4. 长期运行可靠\r\n");
    
    printf("\n精度计算示例:\r\n");
    printf("LSE精度±20ppm:\r\n");
    printf("  每天误差: ±20 × 86400 / 1000000 = ±1.73秒\r\n");
    printf("  每月误差: ±1.73 × 30 = ±51.8秒\r\n");
    printf("  每年误差: ±1.73 × 365 = ±10.5分钟\r\n");
    
    printf("LSI精度±5%%:\r\n");
    printf("  每天误差: ±5%% × 86400 = ±4320秒 = ±1.2小时\r\n");
    printf("  精度差异巨大，不适合精确计时\r\n");
}

// ============================================================================
// 练习2：时间获取与设置演示
// ============================================================================

/**
 * @brief 演示RTC时间数据结构
 */
void practice_time_structure_demo(void)
{
    printf("=== RTC时间数据结构演示 ===\r\n");
    
    printf("RTC_TimeTypeDef结构体:\r\n");
    printf("  Hours: 小时 (0-23)\r\n");
    printf("  Minutes: 分钟 (0-59)\r\n");
    printf("  Seconds: 秒 (0-59)\r\n");
    printf("  DayLightSaving: 夏令时设置\r\n");
    printf("  StoreOperation: 存储操作控制\r\n");
    printf("  结构体大小: %d 字节\r\n", sizeof(RTC_TimeTypeDef));
    
    printf("\nRTC_DateTypeDef结构体:\r\n");
    printf("  WeekDay: 星期 (1-7, 1=周一)\r\n");
    printf("  Month: 月份 (1-12)\r\n");
    printf("  Date: 日期 (1-31)\r\n");
    printf("  Year: 年份 (0-99, 表示2000-2099)\r\n");
    printf("  结构体大小: %d 字节\r\n", sizeof(RTC_DateTypeDef));
    
    // 演示时间获取
    RTC_TimeTypeDef current_time = {0};
    RTC_DateTypeDef current_date = {0};
    
    printf("\n当前时间获取演示:\r\n");
    HAL_RTC_GetTime(&hrtc, &current_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_date, RTC_FORMAT_BIN);
    
    printf("当前时间: %02d:%02d:%02d\r\n", 
           current_time.Hours, current_time.Minutes, current_time.Seconds);
    printf("当前日期: %04d-%02d-%02d (星期%d)\r\n", 
           current_date.Year + 2000, current_date.Month, current_date.Date, current_date.WeekDay);
    
    printf("\n重要提示:\r\n");
    printf("⚠️ 必须先调用HAL_RTC_GetTime()再调用HAL_RTC_GetDate()\r\n");
    printf("⚠️ 这是STM32 HAL库的要求，顺序不能颠倒\r\n");
}

/**
 * @brief 演示时间字符串解析算法
 */
void practice_time_parsing_demo(void)
{
    printf("=== 时间字符串解析演示 ===\r\n");
    
    // 测试不同格式的时间字符串
    const char *test_strings[] = {
        "2025-01-15 14:30:25",  // 标准格式
        "2025-12-31 23:59:59",  // 年末时间
        "2025-02-29 12:00:00",  // 无效日期(2025不是闰年)
        "2024-02-29 12:00:00",  // 有效日期(2024是闰年)
        "2025-13-01 12:00:00",  // 无效月份
        "2025-01-01 25:00:00",  // 无效小时
    };
    
    uint8_t test_count = sizeof(test_strings) / sizeof(char *);
    
    printf("时间字符串解析测试:\r\n");
    for (uint8_t i = 0; i < test_count; i++) {
        printf("输入: \"%s\"\r\n", test_strings[i]);
        
        int year, month, day, hour, minute, second;
        int parsed = sscanf(test_strings[i], "%d-%d-%d %d:%d:%d", 
                           &year, &month, &day, &hour, &minute, &second);
        
        if (parsed == 6) {
            // 参数验证
            if (year >= 2000 && year <= 2099 && month >= 1 && month <= 12 &&
                day >= 1 && day <= 31 && hour <= 23 && minute <= 59 && second <= 59) {
                
                // 闰年检查
                if (month == 2 && day == 29) {
                    bool is_leap = (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
                    if (!is_leap) {
                        printf("  结果: 解析失败 - %d年不是闰年，2月没有29日\r\n", year);
                        continue;
                    }
                }
                
                printf("  结果: 解析成功\r\n");
                printf("    年: %d, 月: %d, 日: %d\r\n", year, month, day);
                printf("    时: %d, 分: %d, 秒: %d\r\n", hour, minute, second);
            } else {
                printf("  结果: 解析失败 - 参数超出有效范围\r\n");
            }
        } else {
            printf("  结果: 解析失败 - 格式不正确\r\n");
        }
        printf("\r\n");
    }
}

// ============================================================================
// 练习3：Unix时间戳转换演示
// ============================================================================

/**
 * @brief 演示Unix时间戳概念
 */
void practice_unix_timestamp_concept_demo(void)
{
    printf("=== Unix时间戳概念演示 ===\r\n");
    
    printf("Unix时间戳定义:\r\n");
    printf("  起始时间: 1970年1月1日 00:00:00 UTC\r\n");
    printf("  计算方式: 从起始时间到指定时间的秒数\r\n");
    printf("  数据类型: 32位无符号整数 (uint32_t)\r\n");
    printf("  最大值: 4294967295 (2106年2月7日)\r\n");
    
    printf("\n重要时间点的时间戳:\r\n");
    printf("  1970-01-01 00:00:00 → 0\r\n");
    printf("  2000-01-01 00:00:00 → 946684800\r\n");
    printf("  2025-01-01 00:00:00 → 1735689600\r\n");
    printf("  2038-01-19 03:14:07 → 2147483647 (32位有符号整数最大值)\r\n");
    
    printf("\n应用场景:\r\n");
    printf("1. 数据记录的时间标记\r\n");
    printf("2. 文件命名和分类\r\n");
    printf("3. 时间比较和计算\r\n");
    printf("4. 跨平台时间交换\r\n");
    
    // 演示时间戳计算
    printf("\n时间戳计算示例:\r\n");
    printf("计算2025-01-15 14:30:25的时间戳:\r\n");
    printf("1. 从1970年到2025年的天数\r\n");
    printf("2. 加上1月1日到1月15日的天数\r\n");
    printf("3. 转换为秒数并加上当天的时分秒\r\n");
}

/**
 * @brief 演示闰年判断算法
 */
void practice_leap_year_demo(void)
{
    printf("=== 闰年判断算法演示 ===\r\n");
    
    printf("闰年判断规则:\r\n");
    printf("1. 能被4整除且不能被100整除的年份是闰年\r\n");
    printf("2. 能被400整除的年份是闰年\r\n");
    
    // 测试年份
    int test_years[] = {1900, 2000, 2004, 2024, 2025, 2100, 2400};
    uint8_t year_count = sizeof(test_years) / sizeof(int);
    
    printf("\n闰年判断测试:\r\n");
    for (uint8_t i = 0; i < year_count; i++) {
        int year = test_years[i];
        bool is_leap = (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
        
        printf("  %d年: %s", year, is_leap ? "闰年" : "平年");
        
        // 解释原因
        if (year % 400 == 0) {
            printf(" (能被400整除)\r\n");
        } else if (year % 100 == 0) {
            printf(" (能被100整除但不能被400整除)\r\n");
        } else if (year % 4 == 0) {
            printf(" (能被4整除但不能被100整除)\r\n");
        } else {
            printf(" (不能被4整除)\r\n");
        }
    }
    
    printf("\n每月天数表:\r\n");
    const char *month_names[] = {"", "一月", "二月", "三月", "四月", "五月", "六月",
                                "七月", "八月", "九月", "十月", "十一月", "十二月"};
    const int days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    
    for (int m = 1; m <= 12; m++) {
        printf("  %s: %d天", month_names[m], days_in_month[m - 1]);
        if (m == 2) {
            printf(" (闰年29天)");
        }
        printf("\r\n");
    }
}

/**
 * @brief 演示时间戳转换算法
 */
void practice_timestamp_conversion_demo(void)
{
    printf("=== 时间戳转换算法演示 ===\r\n");
    
    // 测试时间：2025-01-15 14:30:25
    RTC_TimeTypeDef test_time = {14, 30, 25, 0, 0};
    RTC_DateTypeDef test_date = {1, 1, 15, 25};  // 2025-01-15 周一
    
    printf("测试时间: %04d-%02d-%02d %02d:%02d:%02d\r\n",
           test_date.Year + 2000, test_date.Month, test_date.Date,
           test_time.Hours, test_time.Minutes, test_time.Seconds);
    
    // 手动计算过程演示
    int year = test_date.Year + 2000;
    int month = test_date.Month;
    int day = test_date.Date;
    int hour = test_time.Hours;
    int minute = test_time.Minutes;
    int second = test_time.Seconds;
    
    printf("\n计算过程:\r\n");
    
    // 1. 计算年份贡献的天数
    uint32_t days = 0;
    printf("1. 计算从1970年到%d年的天数:\r\n", year);
    
    for (int y = 1970; y < year; y++) {
        bool is_leap = (y % 4 == 0 && y % 100 != 0) || (y % 400 == 0);
        days += is_leap ? 366 : 365;
        
        if (y < 1975 || y >= 2020) {  // 只显示部分年份
            printf("   %d年: %d天 (累计: %d天)\r\n", y, is_leap ? 366 : 365, days);
        } else if (y == 1975) {
            printf("   ... (省略中间年份) ...\r\n");
        }
    }
    
    // 2. 计算月份贡献的天数
    const int days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    uint32_t month_days = 0;
    
    printf("2. 计算%d年1月到%d月的天数:\r\n", year, month - 1);
    for (int m = 1; m < month; m++) {
        int month_day_count = days_in_month[m - 1];
        if (m == 2 && ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0))) {
            month_day_count = 29;  // 闰年2月
        }
        month_days += month_day_count;
        printf("   %d月: %d天 (累计: %d天)\r\n", m, month_day_count, month_days);
    }
    
    // 3. 加上当月天数
    uint32_t total_days = days + month_days + (day - 1);
    printf("3. 加上当月天数: %d - 1 = %d天\r\n", day, day - 1);
    printf("   总天数: %d + %d + %d = %d天\r\n", days, month_days, day - 1, total_days);
    
    // 4. 转换为秒
    uint32_t total_seconds = total_days * 24 * 3600 + hour * 3600 + minute * 60 + second;
    printf("4. 转换为秒:\r\n");
    printf("   天数秒数: %d × 24 × 3600 = %d秒\r\n", total_days, total_days * 24 * 3600);
    printf("   时分秒: %d×3600 + %d×60 + %d = %d秒\r\n", 
           hour, minute, second, hour * 3600 + minute * 60 + second);
    printf("   总秒数: %d秒\r\n", total_seconds);
    
    // 5. 时区修正
    uint32_t final_timestamp = total_seconds - 28800;  // 减去8小时
    printf("5. 时区修正 (减去8小时):\r\n");
    printf("   最终时间戳: %d - 28800 = %d\r\n", total_seconds, final_timestamp);
    printf("   十六进制: 0x%08X\r\n", final_timestamp);
}

// ============================================================================
// 练习4：时间格式化演示
// ============================================================================

/**
 * @brief 演示多种时间格式
 */
void practice_time_format_demo(void)
{
    printf("=== 时间格式化演示 ===\r\n");
    
    // 示例时间
    RTC_TimeTypeDef time = {14, 30, 25, 0, 0};  // 14:30:25
    RTC_DateTypeDef date = {1, 1, 15, 25};      // 2025-01-15 周一
    
    char buffer[64];
    
    printf("示例时间: 2025年1月15日 14:30:25 星期一\r\n\n");
    
    printf("不同格式化输出:\r\n");
    
    // 格式1：标准格式
    snprintf(buffer, sizeof(buffer), "%04d-%02d-%02d %02d:%02d:%02d",
             date.Year + 2000, date.Month, date.Date,
             time.Hours, time.Minutes, time.Seconds);
    printf("1. 标准格式: %s\r\n", buffer);
    
    // 格式2：紧凑格式
    snprintf(buffer, sizeof(buffer), "%04d%02d%02d_%02d%02d%02d",
             date.Year + 2000, date.Month, date.Date,
             time.Hours, time.Minutes, time.Seconds);
    printf("2. 紧凑格式: %s\r\n", buffer);
    
    // 格式3：文件名格式
    snprintf(buffer, sizeof(buffer), "%04d-%02d-%02d_%02dh%02dm",
             date.Year + 2000, date.Month, date.Date,
             time.Hours, time.Minutes);
    printf("3. 文件名格式: %s\r\n", buffer);
    
    // 格式4：中文格式
    snprintf(buffer, sizeof(buffer), "%04d年%02d月%02d日 %02d时%02d分%02d秒",
             date.Year + 2000, date.Month, date.Date,
             time.Hours, time.Minutes, time.Seconds);
    printf("4. 中文格式: %s\r\n", buffer);
    
    // 格式5：12小时制
    int hour_12 = time.Hours;
    const char *am_pm = "AM";
    if (hour_12 >= 12) {
        am_pm = "PM";
        if (hour_12 > 12) hour_12 -= 12;
    }
    if (hour_12 == 0) hour_12 = 12;
    
    snprintf(buffer, sizeof(buffer), "%04d-%02d-%02d %02d:%02d:%02d %s",
             date.Year + 2000, date.Month, date.Date,
             hour_12, time.Minutes, time.Seconds, am_pm);
    printf("5. 12小时制: %s\r\n", buffer);
    
    // 格式6：ISO 8601格式
    snprintf(buffer, sizeof(buffer), "%04d-%02d-%02dT%02d:%02d:%02d+08:00",
             date.Year + 2000, date.Month, date.Date,
             time.Hours, time.Minutes, time.Seconds);
    printf("6. ISO 8601: %s\r\n", buffer);
}

// ============================================================================
// 主练习函数
// ============================================================================

/**
 * @brief 第6章所有练习的入口函数
 */
void chapter6_practice_all(void)
{
    printf("\r\n");
    printf("========================================\r\n");
    printf("    第6章：RTC时间管理与时间戳处理\r\n");
    printf("           实践练习开始\r\n");
    printf("========================================\r\n");
    
    practice_rtc_config_demo();
    printf("\r\n");
    
    practice_clock_source_demo();
    printf("\r\n");
    
    practice_time_structure_demo();
    printf("\r\n");
    
    practice_time_parsing_demo();
    printf("\r\n");
    
    practice_unix_timestamp_concept_demo();
    printf("\r\n");
    
    practice_leap_year_demo();
    printf("\r\n");
    
    practice_timestamp_conversion_demo();
    printf("\r\n");
    
    practice_time_format_demo();
    printf("\r\n");
    
    printf("========================================\r\n");
    printf("           实践练习结束\r\n");
    printf("========================================\r\n");
    printf("\r\n");
}
