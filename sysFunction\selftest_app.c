#include "selftest_app.h"
#include "mydefine.h"       // 系统公共头文件
#include "gd25qxx.h"        // Flash驱动
#include "bsp_driver_sd.h"  // SD卡驱动
#include "rtc_app.h"        // RTC应用层
#include "usart_app.h"      // 串口应用层
#include "sd_app.h"         // SD卡应用层（用于日志记录）
#include "flash_app.h"      // Flash应用层（用于地址定义）
#include "string.h"         // 字符串处理
#include "stdio.h"          // 标准输入输出
#include "fatfs.h"          // FatFs文件系统

// --- 设备ID管理全局变量 ---

/**
 * @brief 全局设备信息结构体
 * @details 存储设备ID、队伍编号、上电次数等信息
 */
device_info_t g_device_info = {
    .team_number = DEFAULT_TEAM_NUMBER,
    .power_on_count = 0,
    .initialized = 0
};

/**
 * @brief 运行完整系统自检
 * @retval 自检结果
 */
selftest_result_t selftest_run_all(void)
{
    selftest_info_t info = {0};
    
    // 检测Flash（使用简化版本，专注于竞赛要求）
    selftest_check_flash_simple(&info);
    
    // 检测SD卡
    selftest_check_sd(&info);
    
    // 检测RTC
    selftest_check_rtc(&info);
    
    // 打印结果
    selftest_print_results(&info);
    
    // 判断整体结果
    if (!info.flash_ok) return SELFTEST_FLASH_ERROR;
    if (!info.sd_ok) return SELFTEST_SD_ERROR;
    if (!info.rtc_ok) return SELFTEST_RTC_ERROR;
    
    return SELFTEST_OK;
}

/**
 * @brief 检测Flash状态（带调试信息）
 * @details 按照竞赛要求检测Flash：1.Flash ID有效 2.能从Flash中读取到设备ID "2025-CIMC-2025478430"
 * @param info 自检信息结构体指针
 * @retval 自检结果
 */
selftest_result_t selftest_check_flash(selftest_info_t *info)
{
    if (info == NULL) return SELFTEST_FLASH_ERROR;

    info->flash_id = spi_flash_read_id();

    if (info->flash_id == 0x000000 || info->flash_id == 0xFFFFFF) {
        info->flash_id = spi_flash_read_id_alt();
    }

    if (info->flash_id == 0x000000 || info->flash_id == 0xFFFFFF) {
        info->flash_ok = 0;
        return SELFTEST_FLASH_ERROR;
    }

    flash_result_t health_result = flash_check_health();
    if (health_result != FLASH_OK) {
        info->flash_ok = 0;
        return SELFTEST_FLASH_ERROR;
    }

    if (g_device_info.initialized != 1) {
        info->flash_ok = 0;
        return SELFTEST_FLASH_ERROR;
    }

    // 竞赛要求：Flash判断ok的条件是能否读取到队伍ID
    // 如果队伍ID不是默认值，说明从Flash成功读取到了有效的队伍ID
    if (strcmp(g_device_info.team_number, DEFAULT_TEAM_NUMBER) == 0) {
        // 如果还是默认值，说明Flash读取失败
        info->flash_ok = 0;
        return SELFTEST_FLASH_ERROR;
    }

    info->flash_ok = 1;
    return SELFTEST_OK;
}

/**
 * @brief 简化的Flash检测（竞赛专用版本）
 * @details 按照竞赛要求检测Flash：1.Flash ID有效 2.能从Flash中读取到队伍ID
 * @param info 自检信息结构体指针
 * @retval 自检结果
 */
selftest_result_t selftest_check_flash_simple(selftest_info_t *info)
{
    if (info == NULL) return SELFTEST_FLASH_ERROR;

    // 步骤1：检查Flash ID是否有效
    info->flash_id = spi_flash_read_id();

    if (info->flash_id == 0x000000 || info->flash_id == 0xFFFFFF) {
        info->flash_id = spi_flash_read_id_alt();
    }

    if (info->flash_id == 0x000000 || info->flash_id == 0xFFFFFF) {
        info->flash_ok = 0;
        return SELFTEST_FLASH_ERROR;
    }

    // 步骤2：竞赛要求 - 检查能否从Flash读取到队伍ID
    // 调试信息：显示当前队伍ID和默认值（竞赛时注释掉）
    // extern UART_HandleTypeDef huart1;
    // extern int my_printf(UART_HandleTypeDef *huart, const char *format, ...);

    // my_printf(&huart1, "DEBUG: Current team_number: '%s'\r\n", g_device_info.team_number);
    // my_printf(&huart1, "DEBUG: Default team_number: '%s'\r\n", DEFAULT_TEAM_NUMBER);
    // my_printf(&huart1, "DEBUG: g_device_info.initialized: %d\r\n", g_device_info.initialized);

    // 如果队伍ID等于默认值，说明Flash读取失败
    if (strcmp(g_device_info.team_number, DEFAULT_TEAM_NUMBER) == 0) {
        // my_printf(&huart1, "DEBUG: Team ID matches default, Flash read failed\r\n");
        info->flash_ok = 0;
        return SELFTEST_FLASH_ERROR;
    }

    // Flash ID有效且能读取到队伍ID，Flash检测通过
    info->flash_ok = 1;
    return SELFTEST_OK;
}

/**
 * @brief 检测SD卡状态
 * @details 使用综合检测方法检测SD卡是否可用
 * @param info 自检信息结构体指针
 * @retval 自检结果
 */
selftest_result_t selftest_check_sd(selftest_info_t *info)
{
    if (info == NULL) return SELFTEST_SD_ERROR;

    FRESULT fr;
    DWORD free_clusters, total_sectors;
    FATFS *pfs;

    // 步骤1：使用综合检测方法
    fr = sd_comprehensive_check();
    if (fr == FR_OK) {
        // 获取SD卡容量信息
        fr = f_getfree("0:", &free_clusters, &pfs);
        if (fr == FR_OK) {
            total_sectors = (pfs->n_fatent - 2) * pfs->csize;
            info->sd_capacity = total_sectors / 2;  // 每扇区512字节，转换为KB
            info->sd_ok = 1;
            return SELFTEST_OK;
        }
    }

    // 步骤2：第一次检测失败，尝试智能重新初始化
    if (sd_check_and_reinit_if_needed()) {
        // 重新初始化成功，再次尝试获取容量信息
        fr = f_getfree("0:", &free_clusters, &pfs);
        if (fr == FR_OK) {
            total_sectors = (pfs->n_fatent - 2) * pfs->csize;
            info->sd_capacity = total_sectors / 2;  // 每扇区512字节，转换为KB
            info->sd_ok = 1;
            return SELFTEST_OK;
        }
    }

    // 步骤3：所有尝试都失败，SD卡检测失败
    info->sd_ok = 0;
    info->sd_capacity = 0;

    return SELFTEST_SD_ERROR;
}

/**
 * @brief 检测RTC状态
 * @param info 自检信息结构体指针
 * @retval 自检结果
 */
selftest_result_t selftest_check_rtc(selftest_info_t *info)
{
    if (info == NULL) return SELFTEST_RTC_ERROR;
    
    // 获取当前RTC时间
    rtc_format_current_time_string(info->rtc_time, sizeof(info->rtc_time));
    
    // 简单验证：检查时间字符串是否为空
    if (strlen(info->rtc_time) > 0) {
        info->rtc_ok = 1;
        return SELFTEST_OK;
    } else {
        info->rtc_ok = 0;
        return SELFTEST_RTC_ERROR;
    }
}

/**
 * @brief 打印自检结果（竞赛格式）
 * @param info 自检信息结构体指针
 * @retval None
 */
void selftest_print_results(const selftest_info_t *info)
{
    if (info == NULL) return;
    
    // 打印分隔线和标题
    my_printf(&huart1, "======system selftest======\r\n");
    
    // 打印Flash检测结果
    if (info->flash_ok) {
        my_printf(&huart1, "flash.........ok\r\n");
    } else {
        my_printf(&huart1, "flash.........error\r\n");
    }
    
    // 打印SD卡检测结果
    if (info->sd_ok) {
        my_printf(&huart1, "TF card..........ok\r\n");
    } else {
        my_printf(&huart1, "TF card..........error\r\n");
    }
    
    // 打印Flash硬件ID（按照竞赛要求格式）
    my_printf(&huart1, "flash ID: 0x%06lX\r\n", info->flash_id);
    
    // 打印SD卡容量或错误信息
    if (info->sd_ok) {
        my_printf(&huart1, "TF card memory: %lu KB\r\n", info->sd_capacity);
    } else {
        my_printf(&huart1, "can not find TF card\r\n");
    }
    
    // 打印RTC时间
    my_printf(&huart1, "RTC: %s\r\n", info->rtc_time);
    
    // 打印结束分隔线
    my_printf(&huart1, "======system selftest======\r\n");
}

/**
 * @brief 格式化自检结果到缓冲区
 * @param info 自检信息结构体指针
 * @param buffer 输出缓冲区
 * @param buffer_size 缓冲区大小
 * @retval None
 */
void selftest_format_output(const selftest_info_t *info, char *buffer, size_t buffer_size)
{
    if (info == NULL || buffer == NULL || buffer_size == 0) return;
    
    int offset = 0;
    
    // 添加分隔线和标题
    offset += snprintf(buffer + offset, buffer_size - offset, 
                      "======system selftest======\r\n");
    
    // 添加Flash检测结果
    if (info->flash_ok) {
        offset += snprintf(buffer + offset, buffer_size - offset, 
                          "flash.........ok\r\n");
    } else {
        offset += snprintf(buffer + offset, buffer_size - offset, 
                          "flash.........error\r\n");
    }
    
    // 添加SD卡检测结果
    if (info->sd_ok) {
        offset += snprintf(buffer + offset, buffer_size - offset, 
                          "TF card..........ok\r\n");
    } else {
        offset += snprintf(buffer + offset, buffer_size - offset, 
                          "TF card..........error\r\n");
    }
    
    // 添加Flash硬件ID（按照竞赛要求格式）
    offset += snprintf(buffer + offset, buffer_size - offset,
                      "flash ID: 0x%06X\r\n", (unsigned int)info->flash_id);

    // 添加SD卡容量或错误信息
    if (info->sd_ok) {
        offset += snprintf(buffer + offset, buffer_size - offset,
                          "TF card memory: %u KB\r\n", (unsigned int)info->sd_capacity);
    } else {
        offset += snprintf(buffer + offset, buffer_size - offset, 
                          "can not find TF card\r\n");
    }
    
    // 添加RTC时间
    offset += snprintf(buffer + offset, buffer_size - offset, 
                      "RTC: %s\r\n", info->rtc_time);
    
    // 添加结束分隔线
    snprintf(buffer + offset, buffer_size - offset,
             "======system selftest======\r\n");
}

// --- 设备ID管理函数实现区域 ---

/**
 * @brief 设备ID初始化 - 扩展支持竞赛要求
 * @details 初始化设备信息，包括设备ID、队伍编号等，支持CRC校验
 * @param  None
 * @retval None
 */
void device_id_init(void)
{
    // 设置实际的队伍ID（竞赛时的真实队伍编号）
    strcpy(g_device_info.team_number, "2025478430");
    g_device_info.device_id = DEFAULT_DEVICE_ID;  // 默认设备ID
    g_device_info.power_on_count = 1;
    g_device_info.initialized = 1;
    g_device_info.magic_number = DEVICE_ID_MAGIC; // 设置魔数
    g_device_info.crc_check = device_id_calculate_crc(&g_device_info); // 计算CRC
}

/**
 * @brief 从Flash加载设备ID
 * @details 使用直接Flash操作从指定地址读取设备信息
 * @param  None
 * @retval None
 */
void device_id_load_from_flash(void)
{
    extern UART_HandleTypeDef huart1;
    extern int my_printf(UART_HandleTypeDef *huart, const char *format, ...);

    flash_result_t result;
    device_info_t temp_info;

    memset(&temp_info, 0, sizeof(temp_info));

    result = flash_direct_read(DEVICE_ID_FLASH_ADDR, &temp_info, sizeof(temp_info));

    // 调试信息：Flash读取结果（竞赛时注释掉）
    // my_printf(&huart1, "DEBUG: Flash read result: %d\r\n", result);
    // my_printf(&huart1, "DEBUG: Flash data - initialized: %d\r\n", temp_info.initialized);
    // my_printf(&huart1, "DEBUG: Flash data - team_number: '%s'\r\n", temp_info.team_number);

    if (result != FLASH_OK) {
        // my_printf(&huart1, "DEBUG: Flash read failed, using default\r\n");
        g_device_info.initialized = 0;
        return;
    }

    if (temp_info.initialized == 1 && strlen(temp_info.team_number) > 0) {
        // my_printf(&huart1, "DEBUG: Using Flash data\r\n");
        g_device_info = temp_info;
    } else {
        // my_printf(&huart1, "DEBUG: Flash data invalid, saving current data to Flash\r\n");
        // Flash中没有有效数据，保存当前的队伍ID到Flash
        g_device_info.initialized = 1;
        device_id_save_to_flash();
        // my_printf(&huart1, "DEBUG: Team ID saved to Flash\r\n");
    }
}

/**
 * @brief 保存设备ID到Flash
 * @details 使用直接Flash操作将设备信息保存到指定地址
 * @param  None
 * @retval None
 */
void device_id_save_to_flash(void)
{
    flash_direct_write(DEVICE_ID_FLASH_ADDR, &g_device_info, sizeof(g_device_info));
}

/**
 * @brief 打印设备ID信息
 * @details 按照竞赛要求的格式输出设备ID
 * @param  None
 * @retval None
 */
void device_id_print(void)
{
    my_printf(&huart1, "Device_ID:2025-CIMC-%s\r\n", g_device_info.team_number);
}

/**
 * @brief 系统启动序列
 * @details 按照竞赛要求执行完整的系统启动序列
 * @param  None
 * @retval None
 * @note   包含：系统初始化信息、设备ID、系统就绪信息、OLED显示
 *         添加了防重复记录机制，避免程序烧录后手动复位产生重复的system init日志
 */
void system_startup_sequence(void)
{
    extern UART_HandleTypeDef huart1;

    // 1.1 系统上电初始化信息
    my_printf(&huart1, "====system init====\r\n");

    // 检查当前启动是否已经记录过system init（防止重复记录）
    uint8_t already_logged = sd_check_system_init_logged();
    my_printf(&huart1, "[SYSTEM_INIT] Already logged: %d\r\n", already_logged);

    if (!already_logged) {
        // 未记录过，记录系统初始化日志（按照官方示例格式）
        my_printf(&huart1, "[SYSTEM_INIT] Recording system init log\r\n");
        sd_write_log_data("system init");

        // 标记当前启动已记录system init
        sd_mark_system_init_logged();
        my_printf(&huart1, "[SYSTEM_INIT] Marked as logged\r\n");
    } else {
        my_printf(&huart1, "[SYSTEM_INIT] Skipped - already logged for this boot\r\n");
    }

    // 1.2 从Flash中读取设备ID号
    device_id_print();

    // 1.3 系统就绪信息
    my_printf(&huart1, "====system ready====\r\n");

    // 1.4 OLED显示"system idle"（在oled_task中实现）
    // 注：OLED显示在oled_task中根据系统状态自动更新
}

// --- 新增设备ID管理函数实现区域 ---

/**
 * @brief 获取当前设备ID
 * @retval 当前设备ID
 */
uint16_t device_id_get(void)
{
    return g_device_info.device_id;
}

/**
 * @brief 设置新设备ID
 * @param new_id 新的设备ID
 * @retval HAL_OK: 设置成功, HAL_ERROR: 设置失败
 */
HAL_StatusTypeDef device_id_set(uint16_t new_id)
{
    // 验证设备ID有效性
    if (!device_id_validate(new_id)) {
        return HAL_ERROR;
    }

    // 设置新的设备ID
    g_device_info.device_id = new_id;

    // 重新计算CRC
    g_device_info.crc_check = device_id_calculate_crc(&g_device_info);

    return HAL_OK;
}

/**
 * @brief 验证设备ID有效性
 * @param id 要验证的设备ID
 * @retval 1: 有效, 0: 无效
 */
uint8_t device_id_validate(uint16_t id)
{
    // 设备ID范围检查：0x0001-0xFFFF (不允许0x0000)
    if (id == 0x0000) {
        return 0;
    }
    return 1;
}

/**
 * @brief 计算设备信息的CRC校验
 * @param info 设备信息结构体指针
 * @retval CRC校验值
 */
uint16_t device_id_calculate_crc(const device_info_t *info)
{
    if (info == NULL) return 0;

    // 简单的CRC计算（可以后续优化为标准CRC-16）
    uint16_t crc = 0xFFFF;
    const uint8_t *data = (const uint8_t *)info;
    size_t len = sizeof(device_info_t) - sizeof(info->crc_check); // 不包含CRC字段本身

    for (size_t i = 0; i < len; i++) {
        crc ^= data[i];
        for (uint8_t j = 0; j < 8; j++) {
            if (crc & 0x0001) {
                crc = (crc >> 1) ^ 0xA001; // CRC-16-IBM多项式
            } else {
                crc >>= 1;
            }
        }
    }

    return crc;
}
