# 真实硬件模式测试验证清单

## 🎯 测试目标

验证所有串口命令和二进制协议在真实硬件模式下的功能，确保：
- 数据采集显示真实硬件数据（而非模拟数据）
- 变比计算正确
- 超限检测正常
- 输出格式完全符合测评要求

## 🔧 硬件配置

### 当前配置状态
- ✅ USE_REAL_HARDWARE_ADC 已启用
- ✅ 通道映射：CH0->AIN0-GND, CH1->AIN1-GND, CH2->AIN2-GND
- ✅ 串口2 RS485模式（PA2/PA3/PA1）
- ✅ GD30AD3344 SPI接口（PA5/PA6/PA7/PA4）

### 测试连接
- **串口连接**：RS485接口，9600波特率
- **硬件连接**：根据测评要求，只接入一路数据进行采集
- **其他通道**：未接入，应显示0.0值

## 📋 测试项目清单

### 1. 系统启动验证
**测试目的**：确认真实硬件模式已启用

**预期输出**：
```
Multi-channel hardware initialized (GD30AD3344)
GD30AD3344 Config: 0x[配置值], Response: 0x[响应值]
GD30AD3344 appears to be responding
```

**验证标准**：
- ✅ 显示GD30AD3344初始化信息
- ✅ 无"Warning: GD30AD3344 may not be connected properly!"警告
- ✅ 所有信息在串口2正确显示

### 2. 文本命令测试

#### 2.1 设备ID命令
```
输入：get_device_id
预期：report:device_id=0x0002
```

#### 2.2 RTC时间命令
```
输入：get_RTC
预期：report:currentTime=YYYY-MM-DD HH:MM:SS
```

#### 2.3 数据采集命令（核心测试）
```
输入：get_data
预期：report:ch0=X.XX,ch1=Y.YY,ch2=Z.ZZ
```

**重要验证点**：
- 数据应为真实硬件采集值，不是固定的模拟数据
- 已接入通道显示实际电压/电流/电阻值
- 未接入通道显示0.00值
- 包含正确的变比计算结果

#### 2.4 连续采样命令
```
输入：start_sample
预期：report:ch0=X.XX,ch1=Y.YY,ch2=Z.ZZ（立即输出一次）

输入：stop_sample
预期：report:ok
```

#### 2.5 变比配置命令
```
输入：get_ratio
预期：report:ch0ratio=1.00,ch1ratio=1.00,ch2ratio=1.00

输入：set_ratio:ch0=2.00,ch1=3.00,ch2=4.00
预期：report:ok

输入：get_data
预期：数据应反映新的变比计算结果
```

#### 2.6 阈值配置命令
```
输入：get_limit
预期：report:ch0limit=10.00,ch1limit=10.00,ch2limit=10.00

输入：set_limit:ch0=5.00,ch1=6.00,ch2=7.00
预期：report:ok
```

### 3. 二进制协议测试

#### 3.1 获取设备ID（广播）
```
输入：command:FFFF0200080163FA
预期：report:000202000A010002947A
```

#### 3.2 单次读取
```
输入：command:000221000801E7B5
预期：report:0200011800017F9C4D68CB94333F3E531340FC8D0B4468C6
```

**重要验证点**：
- 响应数据应包含真实硬件采集的数值
- 数据格式符合二进制协议规范
- 包含正确的report:前缀

#### 3.3 连续读取
```
输入：command:000222000801E6B4
预期：report:000202000A0100029479
```

#### 3.4 停止读取
```
输入：command:000223000801E5B7
预期：report:000202000A0100029478
```

### 4. 数据一致性验证

**测试目的**：确认文本命令和二进制协议返回的数据一致

**测试步骤**：
1. 执行`get_data`获取文本格式数据
2. 执行`command:000221000801E7B5`获取二进制格式数据
3. 解析二进制数据，对比数值是否一致

### 5. 错误处理验证

#### 5.1 未接入通道处理
**验证标准**：
- 未接入通道显示0.00值
- 不产生错误信息
- 不影响已接入通道的正常工作

#### 5.2 硬件异常处理
**验证标准**：
- 如果GD30AD3344未连接，应显示相应警告
- 系统不会卡死或崩溃
- 错误信息在串口2正确显示

## ✅ 验证成功标准

### 必须满足的条件：
1. **真实硬件数据**：采集到的数据为真实硬件值，不是模拟数据
2. **输出格式正确**：所有响应都包含正确的`report:`前缀
3. **串口2兼容**：所有信息都能在串口2正确显示
4. **变比计算正确**：设置变比后数据计算结果正确
5. **超限检测正常**：设置阈值后超限标记正确
6. **二进制协议正常**：二进制命令返回正确格式的数据
7. **未接入通道处理**：未接入通道显示0值，不产生错误

### 性能要求：
- 命令响应时间 < 1秒
- 连续采样稳定，无数据丢失
- 系统运行稳定，无异常重启

## 📝 测试记录

**测试时间**：_____________________
**测试人员**：_____________________
**固件版本**：GD32.axf (1,106,544字节)
**硬件版本**：_____________________

**测试结果**：
- [ ] 系统启动验证
- [ ] 文本命令测试
- [ ] 二进制协议测试
- [ ] 数据一致性验证
- [ ] 错误处理验证

**备注**：
_________________________________________________
_________________________________________________
_________________________________________________

## 🚀 测试完成确认

当所有测试项目都通过后，可以确认：
**✅ 真实硬件模式的3通道数据采集功能完全正常，所有测评流程命令都能正确工作！**
