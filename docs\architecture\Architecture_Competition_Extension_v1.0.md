# 2025 CIMC西门子杯竞赛功能扩展技术架构设计

## 文档信息
- **版本**: v1.0
- **创建日期**: 2025-01-09
- **负责人**: Bob (系统架构师)
- **项目**: STM32F4数据采集系统竞赛功能扩展

## 1. 架构概述

### 1.1 设计原则
- **向后兼容**: 保持现有功能完全不变
- **模块化扩展**: 新功能独立模块，最小化耦合
- **性能优先**: 重用现有高效机制，避免性能损失
- **可靠性保障**: 多重验证和错误恢复机制

### 1.2 架构分层
```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application Layer)                │
├─────────────────────────────────────────────────────────────┤
│  竞赛命令处理  │  按键显示控制  │  二进制协议处理  │  数据格式化  │
├─────────────────────────────────────────────────────────────┤
│                    业务逻辑层 (Business Layer)               │
├─────────────────────────────────────────────────────────────┤
│  设备ID管理   │  3通道配置   │  多通道采集   │  CRC校验   │  IEEE754  │
├─────────────────────────────────────────────────────────────┤
│                    服务层 (Service Layer)                   │
├─────────────────────────────────────────────────────────────┤
│  任务调度器   │  串口通信   │  配置管理   │  数据存储   │  时间管理   │
├─────────────────────────────────────────────────────────────┤
│                    硬件抽象层 (HAL Layer)                    │
├─────────────────────────────────────────────────────────────┤
│   SPI/DMA    │    ADC     │   Flash    │   SD卡    │    RTC     │
└─────────────────────────────────────────────────────────────┘
```

## 2. 核心模块设计

### 2.1 设备ID管理模块 (device_id_app)

#### 2.1.1 数据结构设计
```c
// 扩展现有device_info_t结构
typedef struct {
    char team_number[16];       // 现有：队伍编号
    uint16_t device_id;         // 新增：16位设备ID (0x0001-0xFFFF)
    uint32_t power_on_count;    // 现有：上电次数
    uint8_t initialized;        // 现有：初始化标志
    uint16_t crc_check;         // 新增：CRC校验
    uint32_t magic_number;      // 新增：魔数验证 (0x44455649 = "DEVI")
} device_info_t;
```

#### 2.1.2 Flash存储布局
```
Flash地址分配:
├── 0x08080000 - 0x08080FFF: 配置参数区 (现有)
├── 0x08081000 - 0x08081FFF: 设备ID区 (新增)
└── 0x08082000 - 0x08082FFF: 备份区 (新增)
```

#### 2.1.3 核心接口
```c
// 设备ID管理接口
HAL_StatusTypeDef device_id_init(void);
uint16_t device_id_get(void);
HAL_StatusTypeDef device_id_set(uint16_t new_id);
HAL_StatusTypeDef device_id_save_to_flash(void);
HAL_StatusTypeDef device_id_load_from_flash(void);
uint8_t device_id_validate(uint16_t id);
```

### 2.2 多通道配置管理模块 (config_app扩展)

#### 2.2.1 配置结构扩展
```c
// 扩展现有config_params_t结构
typedef struct {
    // 3通道变比配置
    float ch0_ratio;
    float ch1_ratio; 
    float ch2_ratio;
    
    // 3通道阈值配置
    float ch0_limit;
    float ch1_limit;
    float ch2_limit;
    
    // 现有字段保持不变
    uint32_t sample_cycle;
    
    // 新增字段
    uint16_t device_id_ref;     // 设备ID引用
    uint32_t config_version;    // 配置版本号
    uint32_t checksum;          // 配置校验和
} config_params_t;
```

#### 2.2.2 配置文件格式
```ini
[DEVICE]
device_id=0x0001
config_version=1

[CHANNEL0]
ratio=1.00
limit=3.30

[CHANNEL1]
ratio=1.00
limit=20.00

[CHANNEL2]
ratio=1.00
limit=10000.00

[SYSTEM]
sample_cycle=5000
```

### 2.3 多通道数据采集模块 (adc_app扩展)

#### 2.3.1 数据结构设计
```c
// 多通道数据结构
typedef struct {
    // 原始数据
    float ch0_raw;
    float ch1_raw;
    float ch2_raw;
    
    // 变比后数据
    float ch0_processed;
    float ch1_processed;
    float ch2_processed;
    
    // 超限标志
    uint8_t ch0_over_limit;
    uint8_t ch1_over_limit;
    uint8_t ch2_over_limit;
    
    // 时间戳
    uint32_t timestamp;
    
    // 数据有效性
    uint8_t data_valid;
} multi_channel_data_t;
```

#### 2.3.2 采集流程设计
```c
// 多通道采集流程
void multi_channel_task(void) {
    // 1. 轮询读取3通道原始数据
    g_multi_data.ch0_raw = GD30AD3344_AD_Read(GD30AD3344_CH_AIN0_GND, GD30AD3344_PGA_6V144);
    g_multi_data.ch1_raw = GD30AD3344_AD_Read(GD30AD3344_CH_AIN1_GND, GD30AD3344_PGA_6V144);
    g_multi_data.ch2_raw = GD30AD3344_AD_Read(GD30AD3344_CH_AIN2_GND, GD30AD3344_PGA_6V144);
    
    // 2. 应用滤波处理
    multi_channel_apply_filters();
    
    // 3. 应用变比计算
    multi_channel_apply_ratios();
    
    // 4. 检查超限状态
    multi_channel_check_limits();
    
    // 5. 更新时间戳
    g_multi_data.timestamp = rtc_get_unix_timestamp();
}
```

### 2.4 二进制协议处理模块 (binary_protocol)

#### 2.4.1 协议结构定义
```c
// 二进制协议结构
typedef struct {
    uint16_t device_id;         // 设备ID (2字节)
    uint8_t msg_type;           // 消息类型 (1字节)
    uint16_t msg_length;        // 消息长度 (2字节)
    uint8_t protocol_ver;       // 协议版本 (1字节)
    uint8_t payload[16];        // 负载数据 (最大16字节)
    uint16_t crc;              // CRC校验 (2字节)
} binary_protocol_t;

// 消息类型定义
typedef enum {
    MSG_TYPE_SET_DEVICE_ID = 0x01,      // 设置设备ID
    MSG_TYPE_GET_DEVICE_ID = 0x02,      // 获取设备ID
    MSG_TYPE_RESPONSE = 0x02,           // 应答消息
    MSG_TYPE_SINGLE_READ = 0x21,        // 单次读取
    MSG_TYPE_CONTINUOUS_READ = 0x22,    // 连续读取
    MSG_TYPE_STOP_READ = 0x2F           // 停止读取
} msg_type_t;
```

#### 2.4.2 CRC-16算法实现
```c
// CRC-16-CCITT算法 (多项式: 0x1021)
static const uint16_t crc16_table[256] = {
    0x0000, 0x1021, 0x2042, 0x3063, 0x4084, 0x50A5, 0x60C6, 0x70E7,
    // ... 完整的256项查找表
};

uint16_t crc16_calculate(const uint8_t *data, size_t length) {
    uint16_t crc = 0xFFFF;
    for (size_t i = 0; i < length; i++) {
        crc = (crc << 8) ^ crc16_table[((crc >> 8) ^ data[i]) & 0xFF];
    }
    return crc;
}
```

#### 2.4.3 IEEE 754处理
```c
// IEEE 754单精度浮点数处理
typedef union {
    float f;
    uint32_t i;
    struct {
        uint32_t mantissa : 23;
        uint32_t exponent : 8;
        uint32_t sign : 1;
    } parts;
} ieee754_float_t;

uint32_t ieee754_encode(float value) {
    ieee754_float_t converter;
    converter.f = value;
    return converter.i;
}

float ieee754_decode(uint32_t encoded) {
    ieee754_float_t converter;
    converter.i = encoded;
    return converter.f;
}
```

## 3. 系统集成设计

### 3.1 任务调度器扩展

#### 3.1.1 新增任务配置
```c
// 扩展现有scheduler_task数组
static task_t scheduler_task[] = {
    // 现有任务保持不变
    {led_task,              1,    0},
    {btn_task,              5,    0},
    {uart_task,             5,    0},
    {adc_task,              100,  0},
    {adc_led1_blink_task,   1000, 0},
    {sampling_board_task,   100,  0},
    {oled_task,             100,  0},
    
    // 新增任务
    {multi_channel_task,    50,   0},    // 多通道采集任务
    {binary_protocol_task,  10,   0},    // 二进制协议处理任务
    {device_id_monitor_task, 1000, 0}   // 设备ID监控任务
};
```

#### 3.1.2 任务优先级设计
```
优先级分配 (执行周期):
├── 1ms:  LED控制 (最高优先级)
├── 5ms:  按键扫描、串口通信
├── 10ms: 二进制协议处理
├── 50ms: 多通道数据采集
├── 100ms: ADC处理、OLED显示、采样板
└── 1000ms: LED闪烁、设备ID监控 (最低优先级)
```

### 3.2 内存管理优化

#### 3.2.1 内存使用评估
```c
// 内存使用统计
typedef struct {
    uint32_t total_ram;         // 总RAM: 256KB
    uint32_t used_ram;          // 已使用RAM
    uint32_t free_ram;          // 可用RAM
    uint32_t stack_usage;       // 栈使用量
    uint32_t heap_usage;        // 堆使用量
} memory_usage_t;

// 新增模块内存估算
// - device_id_app: ~1KB
// - binary_protocol: ~2KB  
// - multi_channel扩展: ~1KB
// - 总计新增: ~4KB (占总RAM的1.6%)
```

#### 3.2.2 缓冲区复用策略
```c
// 复用现有缓冲区
extern uint8_t uart_rx_buffer[128];        // 串口接收缓冲区
extern uint8_t uart_tx_buffer[512];        // 串口发送缓冲区 (新增)
extern uint8_t binary_parse_buffer[64];    // 二进制解析缓冲区 (新增)

// 缓冲区复用规则
// 1. 二进制协议解析复用uart_rx_buffer的后64字节
// 2. 数据格式化复用uart_tx_buffer
// 3. 临时计算使用栈分配，避免堆碎片
```

### 3.3 中断优先级配置

#### 3.3.1 中断优先级分配
```c
// 中断优先级配置 (数值越小优先级越高)
#define SYSTICK_IRQ_PRIORITY        0    // 系统时钟 (最高)
#define DMA_IRQ_PRIORITY           1    // DMA传输
#define USART_IRQ_PRIORITY         2    // 串口通信
#define SPI_IRQ_PRIORITY           3    // SPI通信
#define TIM_IRQ_PRIORITY           4    // 定时器
#define EXTI_IRQ_PRIORITY          5    // 外部中断 (最低)
```

#### 3.3.2 中断处理优化
```c
// 中断服务程序优化原则
// 1. 最小化中断处理时间
// 2. 使用标志位延迟处理
// 3. 避免在中断中调用复杂函数
// 4. 使用DMA减少CPU中断负担
```

## 4. 性能优化策略

### 4.1 数据处理优化

#### 4.1.1 滤波算法优化
```c
// 优化的多通道滤波器
typedef struct {
    float buffer[8];            // 8点滑动平均
    uint8_t index;
    uint8_t filled;
    float sum;                  // 累加和，避免重复计算
} optimized_filter_t;

// 每通道独立滤波器
static optimized_filter_t channel_filters[3];
```

#### 4.1.2 计算优化
```c
// 使用查找表优化CRC计算
// 使用位操作优化IEEE 754转换
// 使用定点运算优化浮点计算 (可选)
```

### 4.2 通信优化

#### 4.2.1 DMA传输优化
```c
// SPI DMA配置优化
// 1. 使用双缓冲DMA，提高传输效率
// 2. 优化DMA传输长度，减少中断次数
// 3. 使用DMA链表模式 (如果需要)
```

#### 4.2.2 串口通信优化
```c
// 串口通信优化策略
// 1. 使用DMA发送，减少CPU占用
// 2. 实现发送队列，避免数据丢失
// 3. 优化波特率配置，提高传输速度
```

## 5. 错误处理与恢复

### 5.1 错误分类与处理

#### 5.1.1 错误类型定义
```c
typedef enum {
    ERROR_NONE = 0,
    ERROR_DEVICE_ID_INVALID,        // 设备ID无效
    ERROR_CONFIG_CORRUPTED,         // 配置损坏
    ERROR_CRC_MISMATCH,            // CRC校验失败
    ERROR_PROTOCOL_INVALID,         // 协议格式错误
    ERROR_CHANNEL_READ_FAILED,      // 通道读取失败
    ERROR_FLASH_WRITE_FAILED,       // Flash写入失败
    ERROR_MEMORY_INSUFFICIENT       // 内存不足
} error_code_t;
```

#### 5.1.2 错误恢复策略
```c
// 分级错误恢复机制
typedef struct {
    error_code_t error;
    uint8_t retry_count;
    uint32_t last_error_time;
    void (*recovery_func)(void);
} error_recovery_t;

// 错误恢复函数
void error_recovery_device_id(void);      // 设备ID恢复
void error_recovery_config(void);         // 配置恢复
void error_recovery_communication(void);  // 通信恢复
```

### 5.2 系统监控

#### 5.2.1 健康监控
```c
// 系统健康状态
typedef struct {
    uint8_t device_id_ok;
    uint8_t config_ok;
    uint8_t communication_ok;
    uint8_t data_acquisition_ok;
    uint32_t error_count;
    uint32_t uptime;
} system_health_t;
```

## 6. 测试与验证策略

### 6.1 单元测试

#### 6.1.1 测试模块
```c
// 单元测试框架
typedef struct {
    const char* test_name;
    uint8_t (*test_func)(void);
    uint8_t result;
} unit_test_t;

// 测试用例
uint8_t test_device_id_management(void);
uint8_t test_multi_channel_acquisition(void);
uint8_t test_binary_protocol_parsing(void);
uint8_t test_crc_calculation(void);
uint8_t test_ieee754_conversion(void);
```

### 6.2 集成测试

#### 6.2.1 测试场景
```c
// 集成测试场景
typedef enum {
    TEST_SCENARIO_BASIC_COMMANDS,       // 基本命令测试
    TEST_SCENARIO_BINARY_PROTOCOL,      // 二进制协议测试
    TEST_SCENARIO_MULTI_CHANNEL,        // 多通道测试
    TEST_SCENARIO_STRESS_TEST,          // 压力测试
    TEST_SCENARIO_COMPATIBILITY        // 兼容性测试
} test_scenario_t;
```

## 7. 部署与维护

### 7.1 版本管理

#### 7.1.1 版本号定义
```c
#define FIRMWARE_VERSION_MAJOR  2
#define FIRMWARE_VERSION_MINOR  0
#define FIRMWARE_VERSION_PATCH  0
#define FIRMWARE_BUILD_NUMBER   1

// 版本字符串: "v2.0.0-build1-competition"
```

### 7.2 调试支持

#### 7.2.1 调试接口
```c
// 调试命令扩展
{"debug info", CMD_DEBUG_INFO, handle_debug_info_cmd},
{"debug memory", CMD_DEBUG_MEMORY, handle_debug_memory_cmd},
{"debug tasks", CMD_DEBUG_TASKS, handle_debug_tasks_cmd},
{"debug protocol", CMD_DEBUG_PROTOCOL, handle_debug_protocol_cmd}
```

## 8. 风险评估与缓解

### 8.1 技术风险

| 风险项 | 影响程度 | 发生概率 | 缓解措施 |
|--------|----------|----------|----------|
| 二进制协议复杂度 | 高 | 中 | 分步实现，充分测试 |
| 内存使用增加 | 中 | 低 | 优化数据结构，复用缓冲区 |
| 实时性能影响 | 中 | 低 | 优化算法，使用DMA |
| 兼容性问题 | 高 | 低 | 保持接口不变，独立模块 |

### 8.2 缓解策略

#### 8.2.1 开发策略
- 渐进式开发，分阶段验证
- 保持现有功能完全不变
- 充分的单元测试和集成测试
- 详细的错误处理和恢复机制

#### 8.2.2 质量保证
- 代码审查机制
- 自动化测试
- 性能基准测试
- 长期稳定性测试

## 9. 总结

本技术架构设计基于现有STM32F4工程的深度分析，采用模块化扩展策略，确保在实现竞赛要求的同时保持系统的稳定性和性能。通过重用现有的优秀组件和机制，最小化开发风险，最大化实现效率。

### 9.1 关键优势
- **兼容性**: 100%保持现有功能
- **性能**: 重用高效的DMA和滤波机制
- **可靠性**: 多重验证和错误恢复
- **可维护性**: 模块化设计，清晰的接口

### 9.2 实现路径
1. 基础模块扩展 (设备ID、配置管理)
2. 数据采集扩展 (多通道支持)
3. 协议处理实现 (文本和二进制)
4. 用户交互完善 (按键显示)
5. 系统集成测试 (全面验证)

通过这个架构设计，可以确保竞赛功能的完整实现，同时保持系统的高质量和可靠性。