/**
 * @file sd_app.h
 * @brief SD卡应用程序头文件 - 2025 CIMC西门子杯智能制造挑战赛
 * @details 定义SD卡数据存储相关的数据结构、常量和函数接口
 *          支持四种数据类型：sample、overLimit、log、hideData
 * <AUTHOR> CIMC西门子杯智能制造挑战赛开发团队
 * @date 2025-01-15
 * @version v1.0
 * @note 所有注释使用GB2312编码，兼容Keil IDE
 */

#ifndef __SD_APP_H_
#define __SD_APP_H_

#include "mydefine.h"
#include "fatfs.h"
#include "lfs.h"

// 文件夹名称定义 - 符合竞赛要求的目录结构
#define FOLDER_SAMPLE    "sample"      // 采集数据存储目录
#define FOLDER_OVERLIMIT "overLimit"   // 超限数据存储目录
#define FOLDER_LOG       "log"         // 日志数据存储目录
#define FOLDER_HIDEDATA  "hideData"    // 加密数据存储目录

// 文件名格式定义 - 竞赛要求的文件管理参数
#define MAX_FILENAME_LEN 64            // 最大文件名长度
#define MAX_FILEPATH_LEN 128           // 最大文件路径长度
#define MAX_RECORDS_PER_FILE 10        // 每文件最大记录数（竞赛要求）

// Flash日志缓存配置 - 解决第一次上电无SD卡时的日志存储问题
#define MAX_CACHED_LOGS 30             // 最大缓存30条日志（足够第一次上电的操作）
#define LOG_ENTRY_SIZE 128             // 每条日志最大128字节
#define FLASH_LOG_CACHE_MAGIC 0x4C4F4731  // 魔数标识："LOG1"
#define FLASH_LOG_CACHE_ADDR 0x6000    // Flash中日志缓存存储地址（修复地址冲突：避免与TEST_STAGE_FLASH_ADDR(0x4000)重叠）

// 数据存储类型枚举 - 四种竞赛要求的数据类型
typedef enum {
    DATA_TYPE_SAMPLE = 0,       // 采集数据：正常采样数据存储
    DATA_TYPE_OVERLIMIT,        // 超限数据：超过阈值的数据存储
    DATA_TYPE_LOG,              // 日志数据：系统操作记录存储
    DATA_TYPE_HIDEDATA          // 加密数据：HEX格式加密数据存储
} data_type_t;

// 文件管理结构体 - 管理每种数据类型的文件状态
typedef struct {
    char current_filename[MAX_FILENAME_LEN];    // 当前活动文件名
    uint8_t record_count;                       // 当前文件中的记录数量
    uint32_t file_creation_time;                // 文件创建时间戳（HAL_GetTick()）
} file_manager_t;

// 日志ID管理结构体 - 管理上电次数和日志文件ID
typedef struct {
    uint32_t log_id;                            // 当前日志文件ID（基于上电次数）
    uint8_t initialized;                        // 管理器初始化状态标志
} log_id_manager_t;

// 测试阶段管理结构体 - 管理测试流程状态和日志文件切换
typedef struct {
    uint8_t current_stage;                      // 当前测试阶段(0-3): 0=log0, 1=log1, 2=log2, 3=log3
    uint8_t first_test_done;                    // 第一次test完成标志
    uint8_t first_limit_done;                   // 第一次limit完成标志
    uint32_t stage_change_time;                 // 阶段切换时间戳
    uint32_t operation_count;                   // 操作计数器
} test_stage_manager_t;

// 日志切换触发类型枚举 - 定义不同操作触发的日志文件切换
typedef enum {
    LOG_TRIGGER_RTC_CONFIG,                     // RTC配置触发(log0阶段)
    LOG_TRIGGER_FIRST_TEST,                     // 第一次test触发(log0→log1)
    LOG_TRIGGER_FIRST_LIMIT,                    // 第一次limit触发(log1→log2)
    LOG_TRIGGER_HIDE_MODE                       // hide模式触发(log2→log3)
} log_switch_trigger_t;

// Flash日志缓存结构体 - 第一次上电无SD卡时的日志临时存储
typedef struct {
    uint32_t magic;                             // 魔数标识：0x4C4F4731 ("LOG1")
    uint32_t count;                             // 已缓存的日志条目数量
    uint32_t next_index;                        // 下一个写入位置索引
    char logs[MAX_CACHED_LOGS][LOG_ENTRY_SIZE]; // 日志条目数组
    uint32_t checksum;                          // 数据校验和
} flash_log_cache_t;

// 全局变量声明
extern FATFS g_fs;                              // FatFs文件系统对象（全局访问）
extern file_manager_t g_file_managers[4];       // 四种数据类型的文件管理器数组
extern log_id_manager_t g_log_id_manager;       // 日志ID管理器实例
extern uint8_t g_filesystem_mounted;            // 文件系统挂载状态标志（全局访问）

// === 核心初始化和管理函数 ===
void sd_app_init(void);                         // SD卡应用完整初始化（主要接口）
FRESULT sd_quick_init(void);                    // 快速SD卡初始化（兼容接口）
FRESULT sd_mount_filesystem(void);              // 挂载文件系统（兼容接口）
FRESULT sd_check_card_status(void);             // 检查SD卡工作状态
FRESULT sd_comprehensive_check(void);           // 综合检测SD卡状态（兼容接口）
FRESULT sd_reinitialize(void);                  // 重新初始化SD卡（错误恢复）
uint8_t sd_check_and_reinit_if_needed(void);    // 智能检查并重新初始化SD卡
uint8_t sd_check_and_reinit_if_needed(void);    // 智能检查并重新初始化SD卡
FRESULT sd_create_directories(void);            // 创建四个存储目录
void sd_monitor_task(void);                     // SD卡状态监控任务（非阻塞）
void sd_restore_file_managers(void);            // 恢复文件管理器状态（查找现有未满文件）

// === 数据写入函数 - 竞赛要求的四种数据类型 ===
FRESULT sd_write_data(data_type_t type, const char *data);                  // 通用数据写入接口
FRESULT sd_write_sample_data(const char *time_str, float voltage);          // 写入采样数据到sample文件夹
FRESULT sd_write_overlimit_data(const char *time_str, float voltage, float limit); // 写入超限数据到overLimit文件夹
FRESULT sd_write_log_data(const char *log_content);                         // 写入日志数据到log文件夹
FRESULT sd_write_hidedata(const char *hex_data);                            // 写入HEX数据到hideData文件夹（兼容接口）
FRESULT sd_write_hidedata_with_voltage(float voltage, uint8_t is_overlimit); // 写入加密数据（推荐接口）

// === 文件管理辅助函数 ===
void sd_generate_filename(data_type_t type, char *filename, size_t filename_size); // 生成符合竞赛要求的文件名
FRESULT sd_check_and_create_new_file(data_type_t type);                     // 检查文件记录数并创建新文件
void sd_get_datetime_string(char *datetime_str, size_t str_size);           // 获取YYYYMMDDHHMMSS格式时间字符串

// === 日志ID管理函数（上电次数管理）===
void sd_log_id_init(void);                      // 日志ID管理器初始化
uint32_t sd_get_next_log_id(void);              // 获取下一个日志文件ID
FRESULT sd_save_log_id_to_flash(void);          // 保存日志ID到Flash存储
FRESULT sd_load_log_id_from_flash(void);        // 从Flash加载日志ID
void sd_check_and_increment_boot_count(void);   // SD卡插入后检查并递增上电次数（兼容接口）
void sd_handle_log_file_switch(void);               // 处理日志文件切换逻辑（竞赛核心功能）
void check_program_version_and_reset_boot_count(void); // 检查程序版本并重置启动次数

// === Flash日志缓存函数（解决第一次上电无SD卡问题）===
FRESULT sd_cache_log_to_flash(const char *log_data);    // 缓存日志到Flash
FRESULT sd_restore_logs_from_flash(void);               // 从Flash恢复日志到SD卡
FRESULT sd_clear_flash_log_cache(void);                 // 清空Flash日志缓存
uint32_t sd_get_cached_log_count(void);                 // 获取Flash中缓存的日志数量
FRESULT sd_reset_boot_count(void);                      // 重置启动次数到0（调试用）
uint32_t sd_get_boot_count(void);                       // 获取当前启动次数（用于显示）
void sd_switch_to_next_log_file(void);                  // 手动切换到下一个log文件
void schedule_flash_cache_clear(void);                  // 安排延迟清空Flash缓存
void reset_flash_cache_sync_flag(void);                 // 重置Flash缓存同步标志（用于reset boot）

// === 系统初始化标志管理 - 防止重复记录system init ===
uint8_t sd_check_system_init_logged(void);              // 检查当前启动是否已记录system init
void sd_mark_system_init_logged(void);                  // 标记当前启动已记录system init
void sd_clear_system_init_flag(void);                   // 清除系统初始化标志（新启动时调用）

// === 测试阶段管理函数 - 管理测试流程状态和日志文件切换 ===
void test_stage_init(void);                             // 测试阶段管理器初始化
flash_result_t test_stage_save_to_flash(void);          // 保存测试阶段状态到Flash
flash_result_t test_stage_load_from_flash(void);        // 从Flash加载测试阶段状态
uint8_t test_stage_get_current_stage(void);             // 获取当前测试阶段
void test_stage_reset(void);                            // 重置测试阶段状态

// === 智能日志切换管理接口 - 统一的日志文件切换控制 ===
flash_result_t smart_log_switch(log_switch_trigger_t trigger);  // 智能日志切换主接口
uint8_t should_switch_to_stage(uint8_t target_stage);           // 检查是否需要切换到目标阶段
uint8_t get_target_stage_by_trigger(log_switch_trigger_t trigger); // 根据触发类型获取目标阶段

// === 日志格式验证和符号转换接口 - 确保日志内容标准化 ===
void log_convert_chinese_symbols(char *text);                   // 中文符号转换为英文符号
uint8_t log_validate_format(const char *log_content);           // 验证日志内容格式
void log_audit_files(void);                                     // 日志文件审计功能

// === 标准日志内容定义 - 确保与测试流程文档完全一致 ===
#define LOG_SYSTEM_INIT         "system init"                   // 系统初始化
#define LOG_SYSTEM_READY        "system ready"                  // 系统就绪
#define LOG_RTC_CONFIG          "RTC Config"                    // RTC配置
#define LOG_TEST_START          "system hardware test"          // 系统自检开始
#define LOG_TEST_OK             "test ok"                       // 测试通过
#define LOG_TEST_ERROR_TF       "test error: tf card not found" // TF卡错误
#define LOG_TEST_ERROR_FLASH    "test error: flash error"       // Flash错误
#define LOG_TEST_ERROR_RTC      "test error: rtc error"         // RTC错误
#define LOG_RATIO_CONFIG        "ratio config"                  // 变比配置
#define LOG_START_SAMPLING      "start sampling"                // 开始采样
#define LOG_STOP_SAMPLING       "stop sampling"                 // 停止采样
#define LOG_LIMIT_CONFIG        "limit config"                  // 阈值配置
#define LOG_CONFIG_SAVE         "config save"                   // 配置保存
#define LOG_HIDE_DATA           "hide data"                     // 数据加密
#define LOG_UNHIDE_DATA         "unhide data"                   // 取消加密

// === 错误恢复和异常处理接口 - 提高系统健壮性 ===
uint8_t check_test_stage_consistency(void);                     // 检查测试阶段状态一致性
flash_result_t flash_write_with_retry(uint32_t addr, const void *data, size_t size); // Flash写入重试机制
void recover_from_sd_card_error(void);                          // SD卡异常恢复
void reset_test_stage_to_initial(void);                         // 重置测试阶段到初始状态
uint8_t auto_repair_inconsistent_state(void);                   // 自动修复不一致状态

// === 系统测试和验证接口 - 确保日志管理系统质量和可靠性 ===
void log_validate_all_files(void);                              // 验证所有log文件内容
uint8_t log_count_records_in_file(const char *filename);        // 统计log文件记录数量
void test_simulate_full_sequence(void);                         // 模拟完整测试序列
void test_verify_log_switching(void);                           // 验证日志切换逻辑
void debug_print_system_state(void);                            // 打印详细系统状态
void test_generate_report(void);                                // 生成测试报告
void sd_force_sync_filesystem(void);                            // 强制同步文件系统到SD卡

// === 全局变量声明 ===
extern FATFS g_fs;                          // FatFs文件系统对象
extern uint8_t g_filesystem_mounted;        // 文件系统挂载状态
// 注意：g_boot_count不再作为全局变量导出，请使用sd_get_boot_count()函数获取

#endif
