# 配置管理模块程序设计

## 2.3.1 模块开发需求分析

### 核心配置参数管理

```c
// 文件：sysFunction/config_app.h (第7-12行)
// 配置参数结构体
typedef struct {
    float ratio;                    // 变比参数 (0-100)
    float limit;                    // 阈值参数 (0-200)
    sampling_cycle_t sample_cycle;  // 采样周期 (5s/10s/15s)
} config_params_t;

// 文件：sysFunction/config_app.c (第14-17行)
// 全局配置参数结构体 - 安全默认值初始化
config_params_t g_config_params = {
    .ratio = 1.0f,  // 默认变比为1.0（无缩放）
    .limit = 1.0f   // 默认阈值为1.0V
};
```

**关键参数说明：**
- **变比(ratio)**：直接影响ADC原始数据到实际物理电压的转换结果
- **阈值(limit)**：判断电压是否超限的唯一依据，关系到报警、记录等关键功能
- **采样周期(sample_cycle)**：决定系统的数据采集密度和响应速度

### 参数范围验证机制

```c
// 文件：sysFunction/config_app.h (第30-34行)
// 参数范围定义
#define RATIO_MIN 0.0f
#define RATIO_MAX 100.0f
#define LIMIT_MIN 0.0f
#define LIMIT_MAX 200.0f

// 文件：sysFunction/config_app.c (第623-632行)
config_status_t config_set_ratio(float ratio)
{
    // 竞赛要求：ratio有效范围为0-100
    if (ratio < 0.0f || ratio > 100.0f) {
        return CONFIG_INVALID_PARAM;
    }
    
    g_config_params.ratio = ratio;
    return CONFIG_OK;
}

// 文件：sysFunction/config_app.c (第639-648行)
config_status_t config_set_limit(float limit)
{
    // 竞赛要求：limit有效范围为0-200
    if (limit < 0.0f || limit > 200.0f) {
        return CONFIG_INVALID_PARAM;
    }
    
    g_config_params.limit = limit;
    return CONFIG_OK;
}
```

## 2.3.2 模块核心设计理念与实现方案

### 2.3.2.1 双层存储策略

#### Flash作为主存储

```c
// 文件：sysFunction/config_app.c (第31-48行)
void config_app_init(void)
{
    // 尝试从Flash中加载之前保存的配置参数
    // Flash存储具有掉电保持特性，可以保存用户的配置
    if (config_load_from_flash() != CONFIG_OK) {
        // 如果加载失败，使用安全的默认配置值
        // 这种情况通常发生在：
        // 1. 系统首次启动，Flash中没有配置数据
        // 2. Flash存储区域损坏或数据不完整
        // 3. 配置数据格式不匹配
        g_config_params.ratio = 1.0f;          // 默认变比1.0，不进行缩放
        g_config_params.limit = 1.0f;          // 默认阈值1.0V，较低的安全值
        g_config_params.sample_cycle = CYCLE_5S; // 默认5秒采样周期
    }
}

// 文件：sysFunction/config_app.c (第606-616行)
config_status_t config_load_from_flash(void)
{
    config_params_t temp_params;
    
    if (flash_direct_read(CONFIG_FLASH_ADDR, &temp_params, sizeof(temp_params)) == FLASH_OK) {
        g_config_params = temp_params;
        return CONFIG_OK;
    }
    
    return CONFIG_FILE_NOT_FOUND;
}
```

**Flash存储优势：**
- **高可靠性**：内部Flash是芯片的一部分，不存在接触不良问题
- **快速启动**：读写速度远快于SD卡，确保系统快速启动
- **独立性**：即使没有插入SD卡，系统也能完全正常工作

#### SD卡作为备份与人机交互接口

```c
// 文件：sysFunction/config_app.c (第449-531行)
config_status_t config_save_to_sd(void)
{
    // 检查SD卡状态
    if (!g_filesystem_mounted) {
        my_printf(&huart1, "SD card not mounted, skip sync\r\n");
        return CONFIG_ERROR;
    }
    FIL file;
    FRESULT fr;
    UINT bytes_written;
    char config_content[256] = {0};
    // 格式化配置内容（按照竞赛要求的INI格式）
    snprintf(config_content, sizeof(config_content),
             "[Ratio]\r\n"
             "Ch0 = %.1f\r\n"
             "\r\n"
             "[Limit]\r\n"
             "Ch0 = %.2f\r\n",
             g_config_params.ratio,
             g_config_params.limit);
    // 尝试不同的文件打开模式
    fr = f_open(&file, CONFIG_FILE_NAME, FA_WRITE | FA_CREATE_NEW);
    if (fr == FR_EXIST) {
        // 文件已存在，尝试覆盖
        fr = f_open(&file, CONFIG_FILE_NAME, FA_WRITE | FA_CREATE_ALWAYS);
    }
    // 写入配置内容
    fr = f_write(&file, config_content, strlen(config_content), &bytes_written);
    // 强制同步文件到存储设备
    f_sync(&file);
    f_close(&file);
    if (fr == FR_OK) {
        // 额外的文件系统同步，确保文件完全写入
        f_sync(NULL);  // 同步整个文件系统
        return CONFIG_OK;
    }
    return CONFIG_ERROR;
}
```

### 2.3.2.2 数据完整性保障机制

#### Flash写入验证机制

```c
// 文件：sysFunction/config_app.c (第410-441行)
config_status_t config_save_to_flash(void)
{
    // 步骤1：保存前备份当前参数
    config_params_t backup = g_config_params;
    // 步骤2：执行Flash写入操作
    flash_result_t write_result = flash_direct_write(CONFIG_FLASH_ADDR, &g_config_params, sizeof(g_config_params));
    if (write_result != FLASH_OK) {
        // Flash写入失败，恢复备份数据
        g_config_params = backup;
        return CONFIG_ERROR;
    }
    // 步骤3：立即读取验证写入结果
    config_params_t verify_params;
    flash_result_t read_result = flash_direct_read(CONFIG_FLASH_ADDR, &verify_params, sizeof(verify_params));
    if (read_result != FLASH_OK) {
        // Flash读取失败，恢复备份数据
        g_config_params = backup;
        return CONFIG_ERROR;
    }
    // 步骤4：使用memcmp比较写入和读取的数据
    if (memcmp(&g_config_params, &verify_params, sizeof(config_params_t)) != 0) {
        // 数据不一致，恢复备份数据并返回错误
        g_config_params = backup;
        return CONFIG_ERROR;
    }
    // 步骤5：验证成功，返回OK
    return CONFIG_OK;
}
```

**数据完整性保障流程：**
1. **备份**：在执行任何Flash操作前，先将当前参数备份到内存
2. **写入**：调用底层flash_direct_write()函数将新参数写入Flash
3. **回读与比较**：立即从同一地址读回数据，使用memcmp()进行逐字节比较
4. **失败恢复**：任何环节失败都会立即恢复备份数据并返回错误

#### 参数范围验证

```c
// 文件：sysFunction/config_app.c (第698-717行)
config_status_t config_validate_ratio(float ratio)
{
    if (ratio >= RATIO_MIN && ratio <= RATIO_MAX) {
        return CONFIG_OK;
    }
    return CONFIG_INVALID_PARAM;
}

config_status_t config_validate_limit(float limit)
{
    if (limit >= LIMIT_MIN && limit <= LIMIT_MAX) {
        return CONFIG_OK;
    }
    return CONFIG_INVALID_PARAM;
}
```

### 2.3.2.3 模块化与接口解耦

#### 单一数据源管理

```c
// 文件：sysFunction/config_app.h (第40行)
// 全局配置参数 - 由配置模块统一管理
extern config_params_t g_config_params;

// 文件：sysFunction/config_app.c (第654-666行)
// 清晰的Getter接口 - 其他模块只能通过这些接口获取参数
float config_get_ratio(void)
{
    return g_config_params.ratio;
}

float config_get_limit(void)
{
    return g_config_params.limit;
}
```

#### 清晰的API接口设计

```c
// 文件：sysFunction/config_app.h (第42-59行)
// 函数声明 - 模块化接口设计
void config_app_init(void);                                    // 配置模块初始化
config_status_t config_load_from_sd(void);                     // 从SD卡加载配置文件
config_status_t config_save_to_flash(void);                    // 保存配置到Flash
config_status_t config_save_to_sd(void);                       // 保存配置到SD卡config.ini文件
config_status_t config_load_from_flash(void);                  // 从Flash加载配置
config_status_t config_ensure_ini_file(void);                  // 确保config.ini文件存在
config_status_t config_set_ratio(float ratio);                 // 设置变比参数
config_status_t config_set_limit(float limit);                 // 设置阈值参数
config_status_t config_set_sample_cycle(sampling_cycle_t cycle); // 设置采样周期参数
float config_get_ratio(void);                                  // 获取变比参数
float config_get_limit(void);                                  // 获取阈值参数
sampling_cycle_t config_get_sample_cycle(void);                // 获取采样周期参数
config_status_t config_validate_ratio(float ratio);            // 验证变比参数
config_status_t config_validate_limit(float limit);            // 验证阈值参数
```

## 2.3.3 INI文件解析机制

### INI文件格式定义

```c
// 文件：sysFunction/config_app.c (第588-594行)
// 标准INI文件格式
const char *default_config =
    "[Ratio]\r\n"
    "Ch0 = 1.0\r\n"
    "\r\n"
    "[Limit]\r\n"
    "Ch0 = 1.0\r\n";
```

### INI文件解析实现

```c
// 文件：sysFunction/config_app.c (第751-806行)
config_status_t parse_ini_file(const char *file_content, config_params_t *params)
{
    if (file_content == NULL || params == NULL) {
        return CONFIG_ERROR;
    }
    
    char line[CONFIG_MAX_LINE_LENGTH];
    char section[CONFIG_MAX_SECTION_NAME] = {0};
    char key[CONFIG_MAX_KEY_NAME];
    char value[CONFIG_MAX_VALUE_NAME];
    const char *ptr = file_content;
    int line_start = 0;
    int line_end = 0;
    
    // 初始化参数为默认值
    params->ratio = 1.0f;
    params->limit = 1.0f;
    params->sample_cycle = CYCLE_5S;
    
    // 逐行解析
    while (*ptr != '\0') {
        // 找到行结束
        line_end = line_start;
        while (ptr[line_end] != '\0' && ptr[line_end] != '\n' && ptr[line_end] != '\r') {
            line_end++;
        }
        
        // 复制行内容并解析
        int line_length = line_end - line_start;
        if (line_length >= CONFIG_MAX_LINE_LENGTH) {
            line_length = CONFIG_MAX_LINE_LENGTH - 1;
        }
        strncpy(line, ptr + line_start, line_length);
        line[line_length] = '\0';
        
        // 解析行
        config_status_t status = parse_ini_line(line, section, key, value);
        if (status == CONFIG_OK) {
            // 处理解析结果
            if (strcmp(section, "Ratio") == 0 && strcmp(key, "Ch0") == 0) {
                params->ratio = atof(value);
            } else if (strcmp(section, "Limit") == 0 && strcmp(key, "Ch0") == 0) {
                params->limit = atof(value);
            }
        }
        
        // 移动到下一行
        while (ptr[line_end] == '\n' || ptr[line_end] == '\r') {
            line_end++;
        }
        line_start = line_end;
        ptr = file_content + line_start;
    }
    
    return CONFIG_OK;
}
```

## 2.3.4 Flash存储地址布局

```c
// 文件：sysFunction/flash_app.h (第58-65行)
// Flash存储地址布局定义
#define DEVICE_ID_FLASH_ADDR    0x0000      // 设备ID存储地址
#define CONFIG_FLASH_ADDR       0x1000      // 配置参数存储地址
#define BOOT_COUNT_FLASH_ADDR   0x2000      // 上电次数存储地址
#define PROGRAM_VERSION_FLASH_ADDR 0x3000   // 程序版本标志存储地址
#define TEST_STAGE_FLASH_ADDR   0x4000      // 测试阶段状态存储地址
#define SYSTEM_INIT_FLAG_ADDR   0x5000      // 系统初始化标志存储地址
```

## 2.3.5 容错能力与可靠性设计

### 首次启动安全机制
- **安全默认值**：ratio=1.0f, limit=1.0f, sample_cycle=CYCLE_5S
- **降级策略**：Flash加载失败时自动使用硬编码默认值
- **系统不崩溃**：任何存储介质故障都不会导致系统无法启动

### 存储介质故障处理
- **SD卡故障**：系统完全依赖Flash运行，不受SD卡状态影响
- **Flash故障**：自动回退到安全默认值，确保基本功能可用
- **写入失败**：备份-写入-验证-恢复机制保证数据完整性

### 接口解耦设计
- **单一数据源**：g_config_params由配置模块统一管理
- **标准化接口**：其他模块通过getter/setter接口访问参数
- **透明存储**：上层模块无需关心参数存储在Flash还是SD卡
