# 数据处理模块程序设计

## 2.5.1 模块设计理念与实现方案

### 数据处理流程架构

```c
// 文件：sysFunction/adc_app.c (第80-103行)
void adc_task(void) //电压测量主函数，每0.1秒跑一次
{
    uint32_t adc_sum = 0; //累加测量结果
    // === 第一级：DMA硬件平均滤波 ===
    for (uint16_t i = 0; i < ADC_DMA_BUFFER_SIZE; i++) { //把所有测量值加起来
        adc_sum += adc_dma_buffer[i];
    }
    adc_val = adc_sum / ADC_DMA_BUFFER_SIZE; //算平均值
    // ADC数字量转换为电压值
    float raw_voltage = ((float)adc_val * 3.3f) / 4096.0f; //转成电压值
    // === 第二级：中值滤波 ===
    float median_filtered = voltage_median_filter(raw_voltage); //去异常值
    // === 第三级：滑动平均滤波 ===
    voltage = voltage_sliding_average_filter(median_filtered); //平滑处理
    // 应用用户配置的变比
    float ratio = config_get_ratio(); //用户设的比例
    g_adc_control.processed_voltage = voltage * ratio;
    adc_check_over_limit(); //检查是否超限
    if (g_adc_control.state == SAMPLING_ACTIVE) { //如果在定时测量
        adc_process_sample();
    }
}
```

## 2.5.2 三级级联滤波实现

### 2.5.2.1 第一级：DMA硬件平均滤波

#### DMA缓冲区配置

```c
// 文件：sysFunction/adc_app.h (第3行)
#define ADC_DMA_BUFFER_SIZE 32 // DMA缓冲区大小，可以根据需要调整

// 文件：sysFunction/adc_app.c (第8行)
uint32_t adc_dma_buffer[ADC_DMA_BUFFER_SIZE]; //原始测量数据存这里

// 文件：sysFunction/adc_app.c (第75-78行)
void adc_dma_init(void) //启动电压测量系统
{
    HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_dma_buffer, ADC_DMA_BUFFER_SIZE); //开始测量
}
```

#### 硬件平均算法

```c
// 文件：sysFunction/adc_app.c (第82-88行)
// 第一级滤波：DMA硬件平均
uint32_t adc_sum = 0; //累加测量结果

for (uint16_t i = 0; i < ADC_DMA_BUFFER_SIZE; i++) { //把所有测量值加起来
    adc_sum += adc_dma_buffer[i];
}

adc_val = adc_sum / ADC_DMA_BUFFER_SIZE; //算平均值
```

**设计特点：**
- **硬件加速**：利用DMA连续采集32个ADC样本，减少CPU负担
- **噪声抑制**：通过算术平均有效降低随机噪声
- **实时性保证**：DMA后台运行，不影响主程序执行

### 2.5.2.2 第二级：中值滤波

#### 中值滤波缓冲区

```c
// 文件：sysFunction/adc_app.c (第17-19行)
#define MEDIAN_FILTER_SIZE 5 //去掉奇怪的测量值
static float median_filter_buffer[MEDIAN_FILTER_SIZE] = {0};
static uint8_t median_filter_index = 0;
```

#### 中值滤波算法实现

```c
// 文件：sysFunction/adc_app.c (第33-54行)
static float voltage_median_filter(float new_value) //去掉奇怪的电压值
{
    median_filter_buffer[median_filter_index] = new_value; //存新值
    median_filter_index = (median_filter_index + 1) % MEDIAN_FILTER_SIZE;
    
    float temp_buffer[MEDIAN_FILTER_SIZE]; //复制数据来排序
    for (uint8_t i = 0; i < MEDIAN_FILTER_SIZE; i++) {
        temp_buffer[i] = median_filter_buffer[i];
    }
    
    // 冒泡排序算法
    for (uint8_t i = 0; i < MEDIAN_FILTER_SIZE - 1; i++) { //冒泡排序
        for (uint8_t j = 0; j < MEDIAN_FILTER_SIZE - 1 - i; j++) {
            if (temp_buffer[j] > temp_buffer[j + 1]) {
                float temp = temp_buffer[j];
                temp_buffer[j] = temp_buffer[j + 1];
                temp_buffer[j + 1] = temp;
            }
        }
    }
    
    return temp_buffer[MEDIAN_FILTER_SIZE / 2]; //返回中间值
}
```

**设计思想：**
- **非线性滤波**：中值滤波能很好地保护信号的边缘特性
- **脉冲噪声抑制**：有效去除突发的异常测量值
- **边缘保护**：不会像平均滤波那样使信号的快速跳变变得模糊

### 2.5.2.3 第三级：滑动平均滤波

#### 滑动平均缓冲区

```c
// 文件：sysFunction/adc_app.c (第12-15行)
#define VOLTAGE_FILTER_SIZE 16 //保存16次历史数据让显示稳定
static float voltage_filter_buffer[VOLTAGE_FILTER_SIZE] = {0};
static uint8_t voltage_filter_index = 0;
static uint8_t voltage_filter_filled = 0;
```

#### 滑动平均算法实现

```c
// 文件：sysFunction/adc_app.c (第56-73行)
static float voltage_sliding_average_filter(float new_voltage) //让显示稳定不跳
{
    voltage_filter_buffer[voltage_filter_index] = new_voltage; //存新值
    voltage_filter_index = (voltage_filter_index + 1) % VOLTAGE_FILTER_SIZE;
    
    if (!voltage_filter_filled && voltage_filter_index == 0) { //记录是否存满
        voltage_filter_filled = 1;
    }
    
    float sum = 0.0f; //计算平均值
    uint8_t count = voltage_filter_filled ? VOLTAGE_FILTER_SIZE : voltage_filter_index;
    
    for (uint8_t i = 0; i < count; i++) {
        sum += voltage_filter_buffer[i];
    }
    
    return sum / count; //返回平均值
}
```

**设计特点：**
- **平滑处理**：对经过中值滤波后的"干净"数据进行16点滑动平均
- **显示稳定**：有效减少显示数值的跳动，提供稳定的用户体验
- **自适应长度**：初始阶段使用实际数据点数，避免零值影响

## 2.5.3 HEX定点数编码实现

### 2.5.3.1 HEX数据结构定义

```c
// 文件：sysFunction/adc_app.h (第20-25行)
// HEX数据结构体
typedef struct {
    uint32_t timestamp;             // Unix时间戳
    uint16_t voltage_integer;       // 电压整数部分
    uint16_t voltage_decimal;       // 电压小数部分
} hex_data_t;
```

### 2.5.3.2 定点数编码算法

```c
// 文件：sysFunction/adc_app.c (第221-229行)
void adc_convert_to_hex(hex_data_t *hex_data) //转成加密格式
{
    if (hex_data == NULL) return;
    
    hex_data->timestamp = rtc_get_unix_timestamp_now(); //时间戳
    
    // === 整数部分：直接截取，无损保存 ===
    hex_data->voltage_integer = (uint16_t)g_adc_control.processed_voltage; //整数部分
    
    // === 小数部分：定点数编码 ===
    float decimal_part = g_adc_control.processed_voltage - hex_data->voltage_integer;
    hex_data->voltage_decimal = (uint16_t)(decimal_part * 65536.0f); //小数部分
}
```

**设计思想：**

#### 整数部分处理
- **直接转换**：通过`(uint16_t)`强制类型转换截取整数部分
- **无损保存**：整数部分完全保留，不存在精度损失

#### 小数部分处理
- **分离小数**：`decimal_part = processed_voltage - voltage_integer`
- **定点编码**：`(uint16_t)(decimal_part * 65536.0f)`将小数转换为16位定点数
- **可逆性保证**：接收方通过除以65536.0f即可完美还原小数部分

### 2.5.3.3 HEX字符串格式化

```c
// 文件：sysFunction/adc_app.c (第231-247行)
void adc_format_hex_string(const hex_data_t *hex_data, char *buffer, size_t buffer_size) //格式化16进制字符串
{
    if (hex_data == NULL || buffer == NULL || buffer_size == 0) return;
    // === 标准化格式：%08lX%04X%04X ===
    snprintf(buffer, buffer_size, "%08lX%04X%04X", //组合成字符串
             hex_data->timestamp,        //时间戳8位十六进制
             hex_data->voltage_integer,  //电压整数4位十六进制
             hex_data->voltage_decimal); //电压小数4位十六进制
    // === 超限标记处理 ===
    if (g_adc_control.over_limit) { //超限加*号
        size_t len = strlen(buffer);
        if (len < buffer_size - 1) {
            buffer[len] = '*';
            buffer[len + 1] = '\0';
        }
    }
}
```

**格式化特点：**
- **固定长度**：时间戳8位 + 电压整数4位 + 电压小数4位 = 16位十六进制
- **标准化输出**：使用`snprintf`确保格式一致性
- **超限标记**：超限时在末尾添加`*`号进行标识

## 2.5.4 数据处理流程集成

### 滤波器初始化

```c
// 文件：sysFunction/adc_app.c (第105-127行)
void adc_control_init(void) //初始化测量系统
{
    g_adc_control.state = SAMPLING_IDLE; //待机状态
    g_adc_control.last_sample_time = 0; //清空时间
    g_adc_control.processed_voltage = 0.0f; //清空电压
    g_adc_control.display_voltage = 0.0f; //清空显示值
    g_adc_control.over_limit = 0; //清除超限
    g_adc_control.led1_blink_state = 0; //清除LED状态
    
    // === 滑动平均滤波器初始化 ===
    for (uint8_t i = 0; i < VOLTAGE_FILTER_SIZE; i++) { //清空历史数据
        voltage_filter_buffer[i] = 0.0f;
    }
    voltage_filter_index = 0;
    voltage_filter_filled = 0;
    
    // === 中值滤波器初始化 ===
    for (uint8_t i = 0; i < MEDIAN_FILTER_SIZE; i++) { //清空过滤数据
        median_filter_buffer[i] = 0.0f;
    }
    median_filter_index = 0;
    
    sampling_cycle_t saved_cycle = config_get_sample_cycle(); //加载用户设置
    adc_set_cycle(saved_cycle);
}
```

### 数据处理调用流程

```c
// 文件：sysFunction/adc_app.c (第177-182行)
// 在采样处理中的HEX编码调用
if (g_hide_mode_enabled) { //加密模式
    hex_data_t hex_data;
    char hex_buffer[32] = {0};
    adc_convert_to_hex(&hex_data);           // 转换为HEX格式
    adc_format_hex_string(&hex_data, hex_buffer, sizeof(hex_buffer)); // 格式化字符串
    my_printf(&huart1, "%s\r\n", hex_buffer);
}
```

## 2.5.5 数据处理性能特点

### 滤波性能指标
- **DMA硬件平均**：32点平均，有效抑制高频噪声
- **中值滤波**：5点中值，去除脉冲干扰，保护信号边缘
- **滑动平均**：16点平均，提供稳定的显示效果

### HEX编码精度
- **时间戳精度**：Unix时间戳，秒级精度
- **电压精度**：16位定点数，理论精度约为1/65536 ≈ 0.000015V
- **可逆性**：完全可逆的编码过程，无精度损失

### 计算复杂度
- **DMA平均**：O(n)，n=32
- **中值滤波**：O(n²)，n=5，使用冒泡排序
- **滑动平均**：O(n)，n=16
- **HEX编码**：O(1)，常数时间复杂度

### 内存使用
- **DMA缓冲区**：32 × 4字节 = 128字节
- **中值滤波缓冲区**：5 × 4字节 = 20字节
- **滑动平均缓冲区**：16 × 4字节 = 64字节
- **总计**：约212字节静态内存
