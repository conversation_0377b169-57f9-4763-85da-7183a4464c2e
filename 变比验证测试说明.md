# 变比验证测试说明

## 🎯 测试目的

验证电流校准问题是否出在变比计算环节。

## 🔧 临时修改内容

### 取消变比计算
**文件**：`sysFunction/adc_app.c`

```c
// 修改前（应用变比）
g_multi_channel_data.ch1_processed = filtered_current * ch1_ratio;

// 修改后（临时取消变比）
g_multi_channel_data.ch1_processed = filtered_current; // * ch1_ratio;
```

### 添加校准过程调试信息
```c
// 每20次输出一次校准过程的详细信息
uart_printf("CALIB_DEBUG: raw=%.4f, calibrated=%.4f, filtered=%.4f\r\n", 
           g_multi_channel_data.ch1_raw, calibrated_current, filtered_current);
```

## 📊 预期测试结果

### 如果问题在变比计算
**取消变比后应该看到**：
```
DEBUG: Only CH1 active - CH1=1.0740 (raw)
CALIB_DEBUG: raw=1.0740, calibrated=10.41, filtered=10.41
report:ch0=0.00,ch1=10.41,ch2=0.00  ✅ 正确的10.41mA
```

### 如果问题在校准函数
**取消变比后仍然错误**：
```
DEBUG: Only CH1 active - CH1=1.0740 (raw)
CALIB_DEBUG: raw=1.0740, calibrated=51.95, filtered=51.95
report:ch0=0.00,ch1*=51.95,ch2=0.00  ❌ 仍然是错误的51.95mA
```

## 🔍 测试步骤

### 1. 使用新固件测试
**固件**：1,106,920字节

### 2. 执行测试命令
```
get_data
```

### 3. 观察输出信息
- **DEBUG信息**：确认原始电压值正确（约1.074V）
- **CALIB_DEBUG信息**：查看校准过程的每一步
- **report信息**：查看最终输出值

### 4. 分析结果

#### 情况A：变比计算有问题
```
CALIB_DEBUG: raw=1.0740, calibrated=10.41, filtered=10.41
report:ch0=0.00,ch1=10.41,ch2=0.00
```
**结论**：校准函数正常，问题在变比计算

#### 情况B：校准函数有问题
```
CALIB_DEBUG: raw=1.0740, calibrated=51.95, filtered=51.95
report:ch0=0.00,ch1*=51.95,ch2=0.00
```
**结论**：校准函数本身有问题

#### 情况C：滤波器有问题
```
CALIB_DEBUG: raw=1.0740, calibrated=10.41, filtered=51.95
report:ch0=0.00,ch1*=51.95,ch2=0.00
```
**结论**：校准正常，滤波器有问题

## 🚀 请立即测试

使用新固件测试并观察：

1. **CALIB_DEBUG输出**：查看校准过程的详细信息
2. **最终数值**：是否变为正确的10.4mA左右
3. **超限标记**：是否还有*号

### 关键验证点
- ✅ **原始值正确**：约1.074V
- ✅ **校准值正确**：约10.41mA
- ✅ **滤波值正确**：约10.41mA
- ✅ **最终输出正确**：约10.41mA（无*号）

## 📋 可能的问题定位

### 如果校准值就是错误的
需要检查：
1. 校准表是否正确加载
2. 线性插值算法是否有问题
3. 数据类型转换是否有问题

### 如果滤波值是错误的
需要检查：
1. 滤波器初始化是否正确
2. 滤波算法是否有问题
3. 滤波器缓冲区是否有问题

### 如果变比计算是错误的
需要检查：
1. 变比值的获取是否正确
2. 变比乘法运算是否有问题
3. 变比配置是否被意外修改

## 🎯 测试完成后的下一步

根据测试结果，我们将：

1. **如果是变比问题**：修复变比计算逻辑
2. **如果是校准问题**：检查校准表和算法
3. **如果是滤波问题**：检查滤波器实现

**🔍 现在请测试并告诉我CALIB_DEBUG的输出结果！**
