#ifndef __GD30AD3344_H_
#define __GD30AD3344_H_

#include "stdint.h"
#include "mydefine.h"

// SPI和DMA配置宏定义
#define SPI_GD30AD3344                  SPI1                    // 使用SPI1
#define SPI_GD30AD3344_CS_PORT          GPIOA                   // CS引脚端口
#define SPI_GD30AD3344_CS_PIN           GPIO_PIN_4              // CS引脚PA4

// DMA配置（根据实际SPI配置修正）
#define DMA_GD30AD3344                  DMA2                    // DMA控制器
#define DMA_GD30_CHANNEL_TX             DMA2_Stream3            // SPI1_TX对应DMA2_Stream3
#define DMA_GD30_CHANNEL_RX             DMA2_Stream0            // SPI1_RX对应DMA2_Stream0（修正）
#define DMA_GD30_REQUEST                DMA_CHANNEL_3           // SPI1的DMA通道3

// 缓冲区大小
#define ARRAYSIZE                       32

// CS控制宏
#define SPI_GD30AD3344_CS_LOW()         HAL_GPIO_WritePin(SPI_GD30AD3344_CS_PORT, SPI_GD30AD3344_CS_PIN, GPIO_PIN_RESET)
#define SPI_GD30AD3344_CS_HIGH()        HAL_GPIO_WritePin(SPI_GD30AD3344_CS_PORT, SPI_GD30AD3344_CS_PIN, GPIO_PIN_SET)

// GD30AD3344通道枚举
typedef enum {
    GD30AD3344_CH_AIN0_AIN1 = 0,    // AIN0~AIN1差分
    GD30AD3344_CH_AIN0_AIN3 = 1,    // AIN0~AIN3差分
    GD30AD3344_CH_AIN1_AIN3 = 2,    // AIN1~AIN3差分
    GD30AD3344_CH_AIN2_AIN3 = 3,    // AIN2~AIN3差分
    GD30AD3344_CH_AIN0_GND  = 4,    // AIN0~GND单端
    GD30AD3344_CH_AIN1_GND  = 5,    // AIN1~GND单端
    GD30AD3344_CH_AIN2_GND  = 6,    // AIN2~GND单端
    GD30AD3344_CH_AIN3_GND  = 7     // AIN3~GND单端
} GD30AD3344_Channel_TypeDef;

// GD30AD3344增益枚举
typedef enum {
    GD30AD3344_PGA_6V144 = 0,       // ±6.144V
    GD30AD3344_PGA_4V096 = 1,       // ±4.096V
    GD30AD3344_PGA_2V048 = 2,       // ±2.048V (默认)
    GD30AD3344_PGA_1V024 = 3,       // ±1.024V
    GD30AD3344_PGA_0V512 = 4,       // ±0.512V
    GD30AD3344_PGA_0V256 = 5,       // ±0.256V
    GD30AD3344_PGA_0V064 = 6        // ±0.064V
} GD30AD3344_PGA_TypeDef;

// 兼容性定义，对应您例程中的命名
#define GD30AD3344_Channel_4    GD30AD3344_CH_AIN0_GND  // AIN0~GND通道

// GD30AD3344配置结构体
typedef struct {
    uint16_t SS         : 1;        // 开始转换位
    uint16_t MUX        : 3;        // 输入多路复用器配置
    uint16_t PGA        : 3;        // 可编程增益放大器配置
    uint16_t MODE       : 1;        // 设备工作模式
    uint16_t DR         : 3;        // 数据速率
    uint16_t RESERVED_1 : 1;        // 保留位
    uint16_t PULL_UP_EN : 1;        // DOUT引脚上拉使能
    uint16_t NOP        : 2;        // 无操作位
    uint16_t RESERVED   : 1;        // 保留位
} GD30AD3344;

// 配置寄存器值计算宏
#define GD30AD3344_InitStruct_Value ((GD30AD3344_InitStruct.SS << 15) | \
                                     (GD30AD3344_InitStruct.MUX << 12) | \
                                     (GD30AD3344_InitStruct.PGA << 9) | \
                                     (GD30AD3344_InitStruct.MODE << 8) | \
                                     (GD30AD3344_InitStruct.DR << 5) | \
                                     (GD30AD3344_InitStruct.RESERVED_1 << 4) | \
                                     (GD30AD3344_InitStruct.PULL_UP_EN << 3) | \
                                     (GD30AD3344_InitStruct.NOP << 1) | \
                                     (GD30AD3344_InitStruct.RESERVED))

// 全局变量声明
extern uint8_t gd30_send_array[ARRAYSIZE];      // SPI DMA发送缓冲区
extern uint8_t gd30_receive_array[ARRAYSIZE];   // SPI DMA接收缓冲区
extern GD30AD3344 GD30AD3344_InitStruct;        // 配置结构体
extern SPI_HandleTypeDef hspi1;                 // SPI1句柄
extern DMA_HandleTypeDef hdma_spi1_tx;          // SPI1 TX DMA句柄
extern DMA_HandleTypeDef hdma_spi1_rx;          // SPI1 RX DMA句柄

// 函数声明
void GD30AD3344_Init(void);                     // 初始化GD30AD3344
uint8_t spi_gd30ad3344_send_byte_dma(uint8_t byte); // DMA发送单字节
uint16_t spi_gd30ad3344_send_halfword_dma(uint16_t half_word); // DMA发送半字
void spi_gd30ad3344_transmit_receive_dma(uint8_t *tx_buffer, uint8_t *rx_buffer, uint16_t size); // DMA收发多字节
void spi_gd30ad3344_wait_for_dma_end(void);     // 等待DMA传输完成
float GD30AD3344_PGA_SET(GD30AD3344_PGA_TypeDef PGA); // 获取增益值
float GD30AD3344_AD_Read(GD30AD3344_Channel_TypeDef CH, GD30AD3344_PGA_TypeDef Ref); // 读取AD值

#endif /* __GD30AD3344_H_ */
