<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\GD32.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\GD32.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 6210000: Last Updated: Tue Aug 12 00:03:50 2025
<BR><P>
<H3>Maximum Stack Usage =      11232 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
handle_start_sample_cmd &rArr; adc_start_sampling &rArr; adc_process_sample &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[25]">CAN1_RX0_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[25]">CAN1_RX0_IRQHandler</a><BR>
 <LI><a href="#[b]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[b]">BusFault_Handler</a><BR>
 <LI><a href="#[9]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[9]">HardFault_Handler</a><BR>
 <LI><a href="#[a]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[a]">MemManage_Handler</a><BR>
 <LI><a href="#[8]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[8]">NMI_Handler</a><BR>
 <LI><a href="#[c]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[c]">UsageFault_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[72]">ADC_DMAConvCplt</a> from stm32f4xx_hal_adc.o(.text.ADC_DMAConvCplt) referenced 2 times from stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA)
 <LI><a href="#[74]">ADC_DMAError</a> from stm32f4xx_hal_adc.o(.text.ADC_DMAError) referenced 2 times from stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA)
 <LI><a href="#[73]">ADC_DMAHalfConvCplt</a> from stm32f4xx_hal_adc.o(.text.ADC_DMAHalfConvCplt) referenced 2 times from stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA)
 <LI><a href="#[23]">ADC_IRQHandler</a> from stm32f4xx_it.o(.text.ADC_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[b]">BusFault_Handler</a> from stm32f4xx_it.o(.text.BusFault_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[25]">CAN1_RX0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[26]">CAN1_RX1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[27]">CAN1_SCE_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[24]">CAN1_TX_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[51]">CAN2_RX0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[52]">CAN2_RX1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[53]">CAN2_SCE_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[50]">CAN2_TX_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5f]">DCMI_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1c]">DMA1_Stream0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1d]">DMA1_Stream1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1e]">DMA1_Stream2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1f]">DMA1_Stream3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[20]">DMA1_Stream4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[21]">DMA1_Stream5_IRQHandler</a> from stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[22]">DMA1_Stream6_IRQHandler</a> from stm32f4xx_it.o(.text.DMA1_Stream6_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[40]">DMA1_Stream7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[6a]">DMA2D_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[49]">DMA2_Stream0_IRQHandler</a> from stm32f4xx_it.o(.text.DMA2_Stream0_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4a]">DMA2_Stream1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4b]">DMA2_Stream2_IRQHandler</a> from stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4c]">DMA2_Stream3_IRQHandler</a> from stm32f4xx_it.o(.text.DMA2_Stream3_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4d]">DMA2_Stream4_IRQHandler</a> from stm32f4xx_it.o(.text.DMA2_Stream4_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[55]">DMA2_Stream5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[56]">DMA2_Stream6_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[57]">DMA2_Stream7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[e]">DebugMon_Handler</a> from stm32f4xx_it.o(.text.DebugMon_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4e]">ETH_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4f]">ETH_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[17]">EXTI0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[39]">EXTI15_10_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[18]">EXTI1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[19]">EXTI2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1a]">EXTI3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1b]">EXTI4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[28]">EXTI9_5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[15]">FLASH_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[41]">FMC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[61]">FPU_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[60]">HASH_RNG_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[9]">HardFault_Handler</a> from stm32f4xx_it.o(.text.HardFault_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[31]">I2C1_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[30]">I2C1_EV_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[33]">I2C2_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[32]">I2C2_EV_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5a]">I2C3_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[59]">I2C3_EV_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[69]">LTDC_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[68]">LTDC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[a]">MemManage_Handler</a> from stm32f4xx_it.o(.text.MemManage_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[8]">NMI_Handler</a> from stm32f4xx_it.o(.text.NMI_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[54]">OTG_FS_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3b]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5c]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5b]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5e]">OTG_HS_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5d]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[12]">PVD_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[f]">PendSV_Handler</a> from stm32f4xx_it.o(.text.PendSV_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[16]">RCC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3a]">RTC_Alarm_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[14]">RTC_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[7]">Reset_Handler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[67]">SAI1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[42]">SDIO_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[83]">SD_initialize</a> from sd_diskio.o(.text.SD_initialize) referenced from sd_diskio.o(.rodata.SD_Driver)
 <LI><a href="#[87]">SD_ioctl</a> from sd_diskio.o(.text.SD_ioctl) referenced from sd_diskio.o(.rodata.SD_Driver)
 <LI><a href="#[85]">SD_read</a> from sd_diskio.o(.text.SD_read) referenced from sd_diskio.o(.rodata.SD_Driver)
 <LI><a href="#[84]">SD_status</a> from sd_diskio.o(.text.SD_status) referenced from sd_diskio.o(.rodata.SD_Driver)
 <LI><a href="#[86]">SD_write</a> from sd_diskio.o(.text.SD_write) referenced from sd_diskio.o(.rodata.SD_Driver)
 <LI><a href="#[34]">SPI1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[35]">SPI2_IRQHandler</a> from stm32f4xx_it.o(.text.SPI2_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[44]">SPI3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[64]">SPI4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[65]">SPI5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[66]">SPI6_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[75]">SPI_DMAAbortOnError</a> from stm32f4xx_hal_spi.o(.text.SPI_DMAAbortOnError) referenced 2 times from stm32f4xx_hal_spi.o(.text.HAL_SPI_IRQHandler)
 <LI><a href="#[7a]">SPI_DMAError</a> from stm32f4xx_hal_spi.o(.text.SPI_DMAError) referenced 2 times from stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA)
 <LI><a href="#[76]">SPI_DMAHalfReceiveCplt</a> from stm32f4xx_hal_spi.o(.text.SPI_DMAHalfReceiveCplt) referenced 2 times from stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA)
 <LI><a href="#[77]">SPI_DMAHalfTransmitReceiveCplt</a> from stm32f4xx_hal_spi.o(.text.SPI_DMAHalfTransmitReceiveCplt) referenced 2 times from stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA)
 <LI><a href="#[79]">SPI_DMAReceiveCplt</a> from stm32f4xx_hal_spi.o(.text.SPI_DMAReceiveCplt) referenced 2 times from stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA)
 <LI><a href="#[78]">SPI_DMATransmitReceiveCplt</a> from stm32f4xx_hal_spi.o(.text.SPI_DMATransmitReceiveCplt) referenced 2 times from stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA)
 <LI><a href="#[d]">SVC_Handler</a> from stm32f4xx_it.o(.text.SVC_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[10]">SysTick_Handler</a> from stm32f4xx_it.o(.text.SysTick_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[6c]">SystemInit</a> from system_stm32f4xx.o(.text.SystemInit) referenced from startup_stm32f429xx.o(.text)
 <LI><a href="#[13]">TAMP_STAMP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[29]">TIM1_BRK_TIM9_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2c]">TIM1_CC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2b]">TIM1_TRG_COM_TIM11_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2a]">TIM1_UP_TIM10_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2d]">TIM2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2e]">TIM3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2f]">TIM4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[43]">TIM5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[47]">TIM6_DAC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[48]">TIM7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3c]">TIM8_BRK_TIM12_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3f]">TIM8_CC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3e]">TIM8_TRG_COM_TIM14_IRQHandler</a> from stm32f4xx_it.o(.text.TIM8_TRG_COM_TIM14_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3d]">TIM8_UP_TIM13_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[45]">UART4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[46]">UART5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[62]">UART7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[63]">UART8_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[7e]">UART_DMAAbortOnError</a> from stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) referenced 2 times from stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
 <LI><a href="#[7d]">UART_DMAError</a> from stm32f4xx_hal_uart.o(.text.UART_DMAError) referenced 2 times from stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA)
 <LI><a href="#[7b]">UART_DMAReceiveCplt</a> from stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) referenced 2 times from stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA)
 <LI><a href="#[7c]">UART_DMARxHalfCplt</a> from stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) referenced 2 times from stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA)
 <LI><a href="#[36]">USART1_IRQHandler</a> from stm32f4xx_it.o(.text.USART1_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[37]">USART2_IRQHandler</a> from stm32f4xx_it.o(.text.USART2_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[38]">USART3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[58]">USART6_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[c]">UsageFault_Handler</a> from stm32f4xx_it.o(.text.UsageFault_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[11]">WWDG_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[6d]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_stm32f429xx.o(.text)
 <LI><a href="#[6f]">_sbackspace</a> from _sgetc.o(.text) referenced 2 times from __0sscanf.o(.text)
 <LI><a href="#[70]">_scanf_char_input</a> from scanf_char.o(.text) referenced from scanf_char.o(.text)
 <LI><a href="#[6e]">_sgetc</a> from _sgetc.o(.text) referenced 2 times from __0sscanf.o(.text)
 <LI><a href="#[81]">_snputc</a> from printfa.o(i._snputc) referenced from printfa.o(i.__0snprintf)
 <LI><a href="#[81]">_snputc</a> from printfa.o(i._snputc) referenced from printfa.o(i.__0vsnprintf)
 <LI><a href="#[82]">_sputc</a> from printfa.o(i._sputc) referenced from printfa.o(i.__0sprintf)
 <LI><a href="#[4]">adc_led1_blink_task</a> from adc_app.o(.text.adc_led1_blink_task) referenced 2 times from scheduler.o(.data.scheduler_task)
 <LI><a href="#[3]">adc_task</a> from adc_app.o(.text.adc_task) referenced 2 times from scheduler.o(.data.scheduler_task)
 <LI><a href="#[1]">btn_task</a> from btn_app.o(.text.btn_task) referenced 2 times from scheduler.o(.data.scheduler_task)
 <LI><a href="#[9f]">handle_check_state_cmd</a> from usart_app.o(.text.handle_check_state_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[92]">handle_conf_cmd</a> from usart_app.o(.text.handle_conf_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[8f]">handle_conf_delete_cmd</a> from usart_app.o(.text.handle_conf_delete_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[91]">handle_conf_fix_cmd</a> from usart_app.o(.text.handle_conf_fix_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[90]">handle_conf_test_cmd</a> from usart_app.o(.text.handle_conf_test_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[94]">handle_config_read_cmd</a> from usart_app.o(.text.handle_config_read_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[93]">handle_config_save_cmd</a> from usart_app.o(.text.handle_config_save_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[a3]">handle_debug_state_cmd</a> from usart_app.o(.text.handle_debug_state_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[b2]">handle_get_data_cmd</a> from usart_app.o(.text.handle_get_data_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[aa]">handle_get_device_id_cmd</a> from usart_app.o(.text.handle_get_device_id_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[b0]">handle_get_limit_cmd</a> from usart_app.o(.text.handle_get_limit_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[ae]">handle_get_ratio_cmd</a> from usart_app.o(.text.handle_get_ratio_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[ac]">handle_get_rtc_cmd</a> from usart_app.o(.text.handle_get_rtc_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[99]">handle_hide_cmd</a> from usart_app.o(.text.handle_hide_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[96]">handle_limit_cmd</a> from usart_app.o(.text.handle_limit_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[a0]">handle_log_validate_cmd</a> from usart_app.o(.text.handle_log_validate_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[95]">handle_ratio_cmd</a> from usart_app.o(.text.handle_ratio_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[9e]">handle_recover_sd_cmd</a> from usart_app.o(.text.handle_recover_sd_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[9c]">handle_reset_boot_cmd</a> from usart_app.o(.text.handle_reset_boot_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[9d]">handle_reset_stage_cmd</a> from usart_app.o(.text.handle_reset_stage_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[8d]">handle_rtc_config_cmd</a> from usart_app.o(.text.handle_rtc_config_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[8e]">handle_rtc_now_cmd</a> from usart_app.o(.text.handle_rtc_now_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[a9]">handle_sb_check_cmd</a> from usart_app.o(.text.handle_sb_check_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[a7]">handle_sb_mode_cmd</a> from usart_app.o(.text.handle_sb_mode_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[a8]">handle_sb_read_cmd</a> from usart_app.o(.text.handle_sb_read_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[a5]">handle_sb_start_cmd</a> from usart_app.o(.text.handle_sb_start_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[a6]">handle_sb_stop_cmd</a> from usart_app.o(.text.handle_sb_stop_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[8b]">handle_sd_detect_cmd</a> from usart_app.o(.text.handle_sd_detect_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[8c]">handle_sd_format_cmd</a> from usart_app.o(.text.handle_sd_format_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[a4]">handle_sd_sync_cmd</a> from usart_app.o(.text.handle_sd_sync_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[8a]">handle_sd_test_cmd</a> from usart_app.o(.text.handle_sd_test_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[ab]">handle_set_device_id_cmd</a> from usart_app.o(.text.handle_set_device_id_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[b5]">handle_set_interval_cmd</a> from usart_app.o(.text.handle_set_interval_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[b1]">handle_set_limit_cmd</a> from usart_app.o(.text.handle_set_limit_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[af]">handle_set_ratio_cmd</a> from usart_app.o(.text.handle_set_ratio_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[ad]">handle_set_rtc_cmd</a> from usart_app.o(.text.handle_set_rtc_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[97]">handle_start_cmd</a> from usart_app.o(.text.handle_start_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[b3]">handle_start_sample_cmd</a> from usart_app.o(.text.handle_start_sample_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[98]">handle_stop_cmd</a> from usart_app.o(.text.handle_stop_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[b4]">handle_stop_sample_cmd</a> from usart_app.o(.text.handle_stop_sample_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[9b]">handle_system_status_cmd</a> from usart_app.o(.text.handle_system_status_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[88]">handle_test_cmd</a> from usart_app.o(.text.handle_test_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[a2]">handle_test_report_cmd</a> from usart_app.o(.text.handle_test_report_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[89]">handle_test_simple_cmd</a> from usart_app.o(.text.handle_test_simple_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[a1]">handle_test_simulate_cmd</a> from usart_app.o(.text.handle_test_simulate_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[b6]">handle_uart_test_cmd</a> from usart_app.o(.text.handle_uart_test_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[9a]">handle_unhide_cmd</a> from usart_app.o(.text.handle_unhide_cmd) referenced from usart_app.o(.rodata.cmd_table)
 <LI><a href="#[71]">isspace</a> from isspace_o.o(.text) referenced 2 times from scanf_char.o(.text)
 <LI><a href="#[0]">led_task</a> from led_app.o(.text.led_task) referenced 2 times from scheduler.o(.data.scheduler_task)
 <LI><a href="#[6b]">main</a> from main.o(.text.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
 <LI><a href="#[6]">oled_task</a> from oled_app.o(.text.oled_task) referenced 2 times from scheduler.o(.data.scheduler_task)
 <LI><a href="#[7f]">prv_btn_event</a> from btn_app.o(.text.prv_btn_event) referenced 2 times from btn_app.o(.text.app_btn_init)
 <LI><a href="#[80]">prv_btn_get_state</a> from btn_app.o(.text.prv_btn_get_state) referenced 2 times from btn_app.o(.text.app_btn_init)
 <LI><a href="#[5]">sampling_board_task</a> from sampling_board_app.o(.text.sampling_board_task) referenced 2 times from scheduler.o(.data.scheduler_task)
 <LI><a href="#[2]">uart_task</a> from usart_app.o(.text.uart_task) referenced 2 times from scheduler.o(.data.scheduler_task)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[6d]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(.text)
</UL>
<P><STRONG><a name="[24d]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[b7]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[d8]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[24e]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[24f]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[250]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[251]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[252]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[7]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_RX0_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_RX0_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>CAN2_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>DCMI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[6a]"></a>DMA2D_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>HASH_RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[69]"></a>LTDC_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[68]"></a>LTDC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>SAI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[66]"></a>SPI6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIM1_BRK_TIM9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>TIM1_TRG_COM_TIM11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>TIM1_UP_TIM10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>UART8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>USART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[b9]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
<LI><a href="#[243]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[253]"></a>___aeabi_memcpy4$move</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memmovea.o(.text), UNUSED)

<P><STRONG><a name="[254]"></a>___aeabi_memcpy8$move</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memmovea.o(.text), UNUSED)

<P><STRONG><a name="[197]"></a>__aeabi_memcpy</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, memmovea.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_get
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_put
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;binary_protocol_generate
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;binary_protocol_parse
</UL>

<P><STRONG><a name="[255]"></a>__aeabi_memmove</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memmovea.o(.text), UNUSED)

<P><STRONG><a name="[256]"></a>__aeabi_memmove4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memmovea.o(.text), UNUSED)

<P><STRONG><a name="[257]"></a>__aeabi_memmove8</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memmovea.o(.text), UNUSED)

<P><STRONG><a name="[bd]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[258]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[259]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[bc]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkfs
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[113]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_MspInit
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_init
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_latest_incomplete_file
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_safe_read_from_sd
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_interval_cmd
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_stop_sample_cmd
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_start_sample_cmd
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_get_data_cmd
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_limit_cmd
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_limits_direct_from_sd
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_ratio_cmd
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_ratios_direct_from_sd
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_rtc_cmd
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sb_mode_cmd
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recover_from_sd_card_error
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_test_stage_to_initial
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_reset_boot_count
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_start_cmd
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_limit_cmd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_ratio_cmd
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_sd
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_rtc_now_cmd
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_test_simple_cmd
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_test_cmd
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_continuous_read
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_single_read
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_set_device_id
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_task
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_app_init
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_control_init
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_send_binary_data
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_process_sample
<LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cache_log_to_flash
</UL>

<P><STRONG><a name="[25a]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[be]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[1a8]"></a>strstr</STRONG> (Thumb, 36 bytes, Stack size 12 bytes, strstr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strstr
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_limit_cmd
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_limits_direct_from_sd
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_ratio_cmd
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_ratios_direct_from_sd
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_validate_all_files
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
</UL>

<P><STRONG><a name="[1ad]"></a>strncpy</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, strncpy.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strncpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_latest_incomplete_file
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_safe_read_from_sd
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cache_log_to_flash
<LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_check_and_create_new_file
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_restore_file_managers
</UL>

<P><STRONG><a name="[1a9]"></a>strchr</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, strchr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_safe_read_from_sd
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_interval_cmd
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_limit_cmd
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_limits_direct_from_sd
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_ratio_cmd
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_ratios_direct_from_sd
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_rtc_cmd
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_validate_all_files
</UL>

<P><STRONG><a name="[199]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_latest_incomplete_file
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_limits_direct_from_sd
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_ratios_direct_from_sd
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_sd
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_safe_sd_test
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_command_type
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_hex_string
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;binary_protocol_parse
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc16_calculate_exam
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_data
</UL>

<P><STRONG><a name="[1aa]"></a>strcmp</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, strcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_latest_incomplete_file
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_safe_read_from_sd
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_limits_direct_from_sd
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_ratios_direct_from_sd
<LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_check_and_create_new_file
</UL>

<P><STRONG><a name="[1ae]"></a>memcmp</STRONG> (Thumb, 26 bytes, Stack size 12 bytes, memcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_latest_incomplete_file
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_safe_read_from_sd
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_flash
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_sync_sd_to_memory_and_flash
</UL>

<P><STRONG><a name="[211]"></a>strncmp</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, strncmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strncmp
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_device_id_cmd
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_command_type
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
</UL>

<P><STRONG><a name="[1e4]"></a>strrchr</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, strrchr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_latest_incomplete_file
</UL>

<P><STRONG><a name="[bf]"></a>__0sscanf</STRONG> (Thumb, 48 bytes, Stack size 72 bytes, __0sscanf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = __0sscanf &rArr; __vfscanf_char &rArr; __vfscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf_char
</UL>
<BR>[Called By]<UL><LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_set_time_from_string
</UL>

<P><STRONG><a name="[c1]"></a>_scanf_int</STRONG> (Thumb, 332 bytes, Stack size 56 bytes, _scanf_int.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _scanf_int
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_chval
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>

<P><STRONG><a name="[c3]"></a>strtol</STRONG> (Thumb, 112 bytes, Stack size 32 bytes, strtol.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = strtol &rArr; _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_strtoul
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_device_id_cmd
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
</UL>

<P><STRONG><a name="[c7]"></a>atoi</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, atoi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = atoi &rArr; strtol &rArr; _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_interval_cmd
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_device_id_cmd
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sb_mode_cmd
</UL>

<P><STRONG><a name="[c8]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_validate_all_files
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
<LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[222]"></a>__aeabi_dcmple</STRONG> (Thumb, 54 bytes, Stack size 12 bytes, dcmple.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = __aeabi_dcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_validate_all_files
</UL>

<P><STRONG><a name="[206]"></a>__aeabi_dcmpge</STRONG> (Thumb, 54 bytes, Stack size 12 bytes, dcmpge.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = __aeabi_dcmpge
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sb_check_cmd
</UL>

<P><STRONG><a name="[ca]"></a>__aeabi_ui2d</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, dfltui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_ui2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_validate_all_files
</UL>

<P><STRONG><a name="[186]"></a>__aeabi_f2d</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_limit_cmd
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_get_limit_cmd
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_ratio_cmd
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_get_ratio_cmd
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sb_check_cmd
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sb_read_cmd
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_board_start_sampling
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_system_status_cmd
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_config_read_cmd
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_config_save_cmd
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_limit_cmd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_ratio_cmd
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_display_from_sd
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_sd
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_safe_sd_test
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_continuous_read
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_single_read
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
<LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_format_output
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_sample_data
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_hidedata_with_voltage
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_overlimit_data
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_process_sample
</UL>

<P><STRONG><a name="[cb]"></a>__aeabi_d2f</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, d2f.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_safe_read_from_sd
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_limit_cmd
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_limits_direct_from_sd
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_ratio_cmd
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_ratios_direct_from_sd
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sb_check_cmd
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_limit_cmd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_ratio_cmd
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[25b]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[24c]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[243]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[bb]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[25c]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[ba]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[25d]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[c4]"></a>__rt_ctype_table</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, ctype_o.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;isspace
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
</UL>

<P><STRONG><a name="[71]"></a>isspace</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, isspace_o.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = isspace
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
</UL>
<BR>[Address Reference Count : 2]<UL><LI> scanf_char.o(.text)
<LI> strtod.o(.text)
</UL>
<P><STRONG><a name="[c2]"></a>_chval</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, _chval.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_strtoul
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_int
</UL>

<P><STRONG><a name="[c0]"></a>__vfscanf_char</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, scanf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = __vfscanf_char &rArr; __vfscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
</UL>

<P><STRONG><a name="[6e]"></a>_sgetc</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 2]<UL><LI> __0sscanf.o(.text)
<LI> strtod.o(.text)
</UL>
<P><STRONG><a name="[6f]"></a>_sbackspace</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 2]<UL><LI> __0sscanf.o(.text)
<LI> strtod.o(.text)
</UL>
<P><STRONG><a name="[c5]"></a>_strtoul</STRONG> (Thumb, 158 bytes, Stack size 40 bytes, _strtoul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_chval
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
</UL>

<P><STRONG><a name="[d0]"></a>__strtod_int</STRONG> (Thumb, 94 bytes, Stack size 40 bytes, strtod.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_local_sscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
</UL>

<P><STRONG><a name="[25e]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[cc]"></a>_float_round</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>

<P><STRONG><a name="[25f]"></a>_float_epilogue</STRONG> (Thumb, 92 bytes, Stack size 4 bytes, fepilogue.o(.text), UNUSED)

<P><STRONG><a name="[d1]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[c9]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2d
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[d2]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[d4]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[d5]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[d6]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
<LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[d7]"></a>__aeabi_d2ulz</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[249]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[b8]"></a>__scatterload</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[260]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[d3]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[261]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[cd]"></a>__vfscanf</STRONG> (Thumb, 808 bytes, Stack size 88 bytes, _scanf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = __vfscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_real
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_int
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf_char
</UL>

<P><STRONG><a name="[cf]"></a>_scanf_real</STRONG> (Thumb, 0 bytes, Stack size 104 bytes, scanf_fp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = _scanf_real
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_local_sscanf
</UL>

<P><STRONG><a name="[db]"></a>_scanf_really_real</STRONG> (Thumb, 556 bytes, Stack size 104 bytes, scanf_fp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>

<P><STRONG><a name="[da]"></a>__aeabi_ul2d</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, dfltul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>

<P><STRONG><a name="[262]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[263]"></a>__decompress1</STRONG> (Thumb, 86 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[23]"></a>ADC_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.ADC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ADC_IRQHandler &rArr; HAL_ADC_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[e1]"></a>BSP_SD_GetCardInfo</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, bsp_driver_sd.o(.text.BSP_SD_GetCardInfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = BSP_SD_GetCardInfo &rArr; HAL_SD_GetCardInfo
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardInfo
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_ioctl
</UL>

<P><STRONG><a name="[e3]"></a>BSP_SD_GetCardState</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, bsp_driver_sd.o(.text.BSP_SD_GetCardState))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = BSP_SD_GetCardState &rArr; HAL_SD_GetCardState
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardState
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_write
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_read
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_status
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_initialize
</UL>

<P><STRONG><a name="[e5]"></a>BSP_SD_Init</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, bsp_driver_sd.o(.text.BSP_SD_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = BSP_SD_Init &rArr; HAL_SD_ConfigWideBusOperation &rArr; SD_FindSCR &rArr; SDIO_ConfigData
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Init
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_IsDetected
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_initialize
</UL>

<P><STRONG><a name="[e6]"></a>BSP_SD_IsDetected</STRONG> (Thumb, 68 bytes, Stack size 48 bytes, bsp_driver_sd.o(.text.BSP_SD_IsDetected))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = BSP_SD_IsDetected &rArr; HAL_SD_GetCardState
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardState
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardInfo
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sd_detect_cmd
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_Init
</UL>

<P><STRONG><a name="[e9]"></a>BSP_SD_ReadBlocks</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, bsp_driver_sd.o(.text.BSP_SD_ReadBlocks))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = BSP_SD_ReadBlocks &rArr; HAL_SD_ReadBlocks &rArr; SDIO_ConfigData
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_read
</UL>

<P><STRONG><a name="[eb]"></a>BSP_SD_WriteBlocks</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, bsp_driver_sd.o(.text.BSP_SD_WriteBlocks))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = BSP_SD_WriteBlocks &rArr; HAL_SD_WriteBlocks &rArr; SDIO_ConfigData
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_write
</UL>

<P><STRONG><a name="[b]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.BusFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DMA1_Stream5_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.DMA1_Stream6_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DMA1_Stream6_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.DMA2_Stream0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DMA2_Stream0_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DMA2_Stream2_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.DMA2_Stream3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DMA2_Stream3_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.DMA2_Stream4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DMA2_Stream4_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[ee]"></a>Error_Handler</STRONG> (Thumb, 310 bytes, Stack size 8 bytes, main.o(.text.Error_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Error_Handler
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_MspInit
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI1_Init
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RTC_Init
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM14_Init
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM6_Init
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[159]"></a>FATFS_LinkDriver</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, ff_gen_drv.o(.text.FATFS_LinkDriver))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = FATFS_LinkDriver
</UL>
<BR>[Called By]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FATFS_Init
</UL>

<P><STRONG><a name="[f0]"></a>GD30AD3344_AD_Read</STRONG> (Thumb, 256 bytes, Stack size 24 bytes, gd30ad3344.o(.text.GD30AD3344_AD_Read))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = GD30AD3344_AD_Read &rArr; spi_gd30ad3344_send_halfword_dma &rArr; uart_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_printf
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_gd30ad3344_send_halfword_dma
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sb_check_cmd
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_board_task
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_board_read_channel
</UL>

<P><STRONG><a name="[f4]"></a>GD30AD3344_Init</STRONG> (Thumb, 180 bytes, Stack size 16 bytes, gd30ad3344.o(.text.GD30AD3344_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = GD30AD3344_Init &rArr; spi_gd30ad3344_send_halfword_dma &rArr; uart_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_printf
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_gd30ad3344_send_halfword_dma
</UL>
<BR>[Called By]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_hardware_init
</UL>

<P><STRONG><a name="[f5]"></a>HAL_ADCEx_InjectedConvCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedConvCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[156]"></a>HAL_ADC_ConfigChannel</STRONG> (Thumb, 368 bytes, Stack size 20 bytes, stm32f4xx_hal_adc.o(.text.HAL_ADC_ConfigChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_ADC_ConfigChannel
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
</UL>

<P><STRONG><a name="[de]"></a>HAL_ADC_ConvCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_adc.o(.text.HAL_ADC_ConvCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAConvCplt
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[df]"></a>HAL_ADC_ConvHalfCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_adc.o(.text.HAL_ADC_ConvHalfCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAHalfConvCplt
</UL>

<P><STRONG><a name="[dd]"></a>HAL_ADC_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_adc.o(.text.HAL_ADC_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAError
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAConvCplt
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[e0]"></a>HAL_ADC_IRQHandler</STRONG> (Thumb, 310 bytes, Stack size 16 bytes, stm32f4xx_hal_adc.o(.text.HAL_ADC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_ADC_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_LevelOutOfWindowCallback
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ErrorCallback
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedConvCpltCallback
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>

<P><STRONG><a name="[f7]"></a>HAL_ADC_Init</STRONG> (Thumb, 350 bytes, Stack size 8 bytes, stm32f4xx_hal_adc.o(.text.HAL_ADC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = HAL_ADC_Init &rArr; HAL_ADC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
</UL>

<P><STRONG><a name="[f6]"></a>HAL_ADC_LevelOutOfWindowCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_adc.o(.text.HAL_ADC_LevelOutOfWindowCallback))
<BR><BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[f8]"></a>HAL_ADC_MspInit</STRONG> (Thumb, 180 bytes, Stack size 40 bytes, adc.o(.text.HAL_ADC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = HAL_ADC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
</UL>

<P><STRONG><a name="[fd]"></a>HAL_ADC_Start_DMA</STRONG> (Thumb, 416 bytes, Stack size 24 bytes, stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_ADC_Start_DMA &rArr; HAL_DMA_Start_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
</UL>

<P><STRONG><a name="[ff]"></a>HAL_DMA_Abort</STRONG> (Thumb, 142 bytes, Stack size 16 bytes, stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
</UL>

<P><STRONG><a name="[132]"></a>HAL_DMA_Abort_IT</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT))
<BR><BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_IRQHandler
</UL>

<P><STRONG><a name="[ed]"></a>HAL_DMA_IRQHandler</STRONG> (Thumb, 448 bytes, Stack size 24 bytes, stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_DMA_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream4_IRQHandler
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream3_IRQHandler
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream2_IRQHandler
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream0_IRQHandler
<LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Stream6_IRQHandler
<LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Stream5_IRQHandler
</UL>

<P><STRONG><a name="[fa]"></a>HAL_DMA_Init</STRONG> (Thumb, 354 bytes, Stack size 24 bytes, stm32f4xx_hal_dma.o(.text.HAL_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_DMA_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
</UL>

<P><STRONG><a name="[fe]"></a>HAL_DMA_Start_IT</STRONG> (Thumb, 162 bytes, Stack size 16 bytes, stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start_DMA
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive_DMA
</UL>

<P><STRONG><a name="[f2]"></a>HAL_Delay</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, stm32f4xx_hal.o(.text.HAL_Delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_init
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_check_and_reinit_if_needed
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD30AD3344_AD_Read
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_simulate_full_sequence
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_sd
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sd_format_cmd
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sd_detect_cmd
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_set_time_from_string
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_printf
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_app_init
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD30AD3344_Init
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_write_with_retry
</UL>

<P><STRONG><a name="[f9]"></a>HAL_GPIO_Init</STRONG> (Thumb, 414 bytes, Stack size 44 bytes, stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_MspInit
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
</UL>

<P><STRONG><a name="[22c]"></a>HAL_GPIO_ReadPin</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_btn_get_state
</UL>

<P><STRONG><a name="[ef]"></a>HAL_GPIO_WritePin</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin))
<BR><BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_id_alt
<LI><a href="#[23d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_id
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
<LI><a href="#[23f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_printf
<LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_disp
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD30AD3344_Init
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_gd30ad3344_send_halfword_dma
</UL>

<P><STRONG><a name="[100]"></a>HAL_GetTick</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnMasterAddressFlagUntilTimeout
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetDate
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetTime
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_Init
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_run
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMATransmitReceiveCplt
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAReceiveCplt
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_print_system_state
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_board_start_sampling
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_generate_report
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_repair_inconsistent_state
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_test_stage_to_initial
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_reset_boot_count
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_system_status_cmd
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smart_log_switch
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_board_task
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_stage_init
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_start_continuous_sampling_binary
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_start_continuous_sampling_text
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_start_sampling
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_process_sample
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_gd30ad3344_send_halfword_dma
<LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_check_and_create_new_file
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_restore_file_managers
</UL>

<P><STRONG><a name="[15c]"></a>HAL_I2CEx_ConfigAnalogFilter</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, stm32f4xx_hal_i2c_ex.o(.text.HAL_I2CEx_ConfigAnalogFilter))
<BR><BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
</UL>

<P><STRONG><a name="[15d]"></a>HAL_I2CEx_ConfigDigitalFilter</STRONG> (Thumb, 64 bytes, Stack size 0 bytes, stm32f4xx_hal_i2c_ex.o(.text.HAL_I2CEx_ConfigDigitalFilter))
<BR><BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
</UL>

<P><STRONG><a name="[101]"></a>HAL_I2C_Init</STRONG> (Thumb, 356 bytes, Stack size 24 bytes, stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
</UL>

<P><STRONG><a name="[104]"></a>HAL_I2C_Mem_Write</STRONG> (Thumb, 348 bytes, Stack size 40 bytes, stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Position
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
</UL>

<P><STRONG><a name="[102]"></a>HAL_I2C_MspInit</STRONG> (Thumb, 108 bytes, Stack size 40 bytes, i2c.o(.text.HAL_I2C_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
</UL>

<P><STRONG><a name="[176]"></a>HAL_IncTick</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_hal.o(.text.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[108]"></a>HAL_Init</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, stm32f4xx_hal.o(.text.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[10a]"></a>HAL_InitTick</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, stm32f4xx_hal.o(.text.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[10b]"></a>HAL_MspInit</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f4xx_hal_msp.o(.text.HAL_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[fc]"></a>HAL_NVIC_EnableIRQ</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
</UL>

<P><STRONG><a name="[fb]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 86 bytes, Stack size 8 bytes, stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
</UL>

<P><STRONG><a name="[109]"></a>HAL_NVIC_SetPriorityGrouping</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping))
<BR><BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[10d]"></a>HAL_RCCEx_PeriphCLKConfig</STRONG> (Thumb, 576 bytes, Stack size 24 bytes, stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_MspInit
</UL>

<P><STRONG><a name="[10e]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 356 bytes, Stack size 24 bytes, stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[103]"></a>HAL_RCC_GetPCLK1Freq</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq))
<BR><BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[179]"></a>HAL_RCC_GetPCLK2Freq</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq))
<BR><BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[10f]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
</UL>

<P><STRONG><a name="[110]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 944 bytes, Stack size 32 bytes, stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_RCC_OscConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[1ed]"></a>HAL_RTC_GetDate</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, stm32f4xx_hal_rtc.o(.text.HAL_RTC_GetDate))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_RTC_GetDate
</UL>
<BR>[Called By]<UL><LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_set_time_from_string
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_sample_data
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_hidedata_with_voltage
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_overlimit_data
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_get_unix_timestamp_now
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_format_current_time_string
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_generate_filename
</UL>

<P><STRONG><a name="[1ec]"></a>HAL_RTC_GetTime</STRONG> (Thumb, 112 bytes, Stack size 8 bytes, stm32f4xx_hal_rtc.o(.text.HAL_RTC_GetTime))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_RTC_GetTime
</UL>
<BR>[Called By]<UL><LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_set_time_from_string
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_sample_data
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_hidedata_with_voltage
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_overlimit_data
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_get_unix_timestamp_now
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_format_current_time_string
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_generate_filename
</UL>

<P><STRONG><a name="[111]"></a>HAL_RTC_Init</STRONG> (Thumb, 252 bytes, Stack size 16 bytes, stm32f4xx_hal_rtc.o(.text.HAL_RTC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_RTC_Init &rArr; HAL_RTC_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RTC_Init
</UL>

<P><STRONG><a name="[112]"></a>HAL_RTC_MspInit</STRONG> (Thumb, 76 bytes, Stack size 56 bytes, rtc.o(.text.HAL_RTC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_RTC_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_Init
</UL>

<P><STRONG><a name="[114]"></a>HAL_RTC_SetDate</STRONG> (Thumb, 356 bytes, Stack size 32 bytes, stm32f4xx_hal_rtc.o(.text.HAL_RTC_SetDate))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_RTC_SetDate
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RTC_Init
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_set_time_from_string
</UL>

<P><STRONG><a name="[115]"></a>HAL_RTC_SetTime</STRONG> (Thumb, 428 bytes, Stack size 40 bytes, stm32f4xx_hal_rtc.o(.text.HAL_RTC_SetTime))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_RTC_SetTime
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RTC_Init
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_set_time_from_string
</UL>

<P><STRONG><a name="[e8]"></a>HAL_SD_ConfigWideBusOperation</STRONG> (Thumb, 294 bytes, Stack size 48 bytes, stm32f4xx_hal_sd.o(.text.HAL_SD_ConfigWideBusOperation))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = HAL_SD_ConfigWideBusOperation &rArr; SD_FindSCR &rArr; SDIO_ConfigData
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBusWidth
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_Init
</UL>

<P><STRONG><a name="[124]"></a>HAL_SD_GetCardCSD</STRONG> (Thumb, 390 bytes, Stack size 8 bytes, stm32f4xx_hal_sd.o(.text.HAL_SD_GetCardCSD))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_SD_GetCardCSD
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[e2]"></a>HAL_SD_GetCardInfo</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, stm32f4xx_hal_sd.o(.text.HAL_SD_GetCardInfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_SD_GetCardInfo
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_IsDetected
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sd_detect_cmd
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sd_test_cmd
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_GetCardInfo
</UL>

<P><STRONG><a name="[e4]"></a>HAL_SD_GetCardState</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, stm32f4xx_hal_sd.o(.text.HAL_SD_GetCardState))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_SD_GetCardState
</UL>
<BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendStatus
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
</UL>
<BR>[Called By]<UL><LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_check_and_reinit_if_needed
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_IsDetected
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sd_detect_cmd
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sd_test_cmd
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_app_init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_GetCardState
</UL>

<P><STRONG><a name="[e7]"></a>HAL_SD_Init</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, stm32f4xx_hal_sd.o(.text.HAL_SD_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = HAL_SD_Init &rArr; HAL_SD_InitCard &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_MspInit
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>
<BR>[Called By]<UL><LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_check_and_reinit_if_needed
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sd_format_cmd
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sd_detect_cmd
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_app_init
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_Init
</UL>

<P><STRONG><a name="[11e]"></a>HAL_SD_InitCard</STRONG> (Thumb, 540 bytes, Stack size 80 bytes, stm32f4xx_hal_sd.o(.text.HAL_SD_InitCard))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_SD_InitCard &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardCSD
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSetRelAdd
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCSD
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendCID
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppOperCommand
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdOperCond
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdGoIdleState
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSelDesel
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetResponse
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_GetPowerState
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_PowerState_ON
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Init
</UL>

<P><STRONG><a name="[11d]"></a>HAL_SD_MspInit</STRONG> (Thumb, 164 bytes, Stack size 48 bytes, sdio.o(.text.HAL_SD_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = HAL_SD_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Init
</UL>

<P><STRONG><a name="[ea]"></a>HAL_SD_ReadBlocks</STRONG> (Thumb, 568 bytes, Stack size 56 bytes, stm32f4xx_hal_sd.o(.text.HAL_SD_ReadBlocks))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_SD_ReadBlocks &rArr; SDIO_ConfigData
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadMultiBlock
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadSingleBlock
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ConfigData
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ReadFIFO
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_ReadBlocks
</UL>

<P><STRONG><a name="[ec]"></a>HAL_SD_WriteBlocks</STRONG> (Thumb, 566 bytes, Stack size 64 bytes, stm32f4xx_hal_sd.o(.text.HAL_SD_WriteBlocks))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_SD_WriteBlocks &rArr; SDIO_ConfigData
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteMultiBlock
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteSingleBlock
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ConfigData
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_WriteFIFO
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_WriteBlocks
</UL>

<P><STRONG><a name="[133]"></a>HAL_SPI_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(.text.HAL_SPI_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_IRQHandler
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAAbortOnError
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMATransmitReceiveCplt
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAReceiveCplt
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAError
</UL>

<P><STRONG><a name="[240]"></a>HAL_SPI_GetState</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(.text.HAL_SPI_GetState))
<BR><BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_gd30ad3344_send_halfword_dma
</UL>

<P><STRONG><a name="[131]"></a>HAL_SPI_IRQHandler</STRONG> (Thumb, 300 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(.text.HAL_SPI_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_SPI_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_ErrorCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI2_IRQHandler
</UL>

<P><STRONG><a name="[134]"></a>HAL_SPI_Init</STRONG> (Thumb, 180 bytes, Stack size 16 bytes, stm32f4xx_hal_spi.o(.text.HAL_SPI_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI1_Init
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
</UL>

<P><STRONG><a name="[135]"></a>HAL_SPI_MspInit</STRONG> (Thumb, 358 bytes, Stack size 56 bytes, spi.o(.text.HAL_SPI_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
</UL>

<P><STRONG><a name="[174]"></a>HAL_SPI_RxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(.text.HAL_SPI_RxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAReceiveCplt
</UL>

<P><STRONG><a name="[172]"></a>HAL_SPI_RxHalfCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(.text.HAL_SPI_RxHalfCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAHalfReceiveCplt
</UL>

<P><STRONG><a name="[136]"></a>HAL_SPI_TransmitReceive</STRONG> (Thumb, 846 bytes, Stack size 40 bytes, stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_id_alt
<LI><a href="#[23d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_id
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
<LI><a href="#[23f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
</UL>

<P><STRONG><a name="[138]"></a>HAL_SPI_TransmitReceive_DMA</STRONG> (Thumb, 300 bytes, Stack size 16 bytes, stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_SPI_TransmitReceive_DMA &rArr; HAL_DMA_Start_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_gd30ad3344_send_halfword_dma
</UL>

<P><STRONG><a name="[175]"></a>HAL_SPI_TxRxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(.text.HAL_SPI_TxRxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMATransmitReceiveCplt
</UL>

<P><STRONG><a name="[173]"></a>HAL_SPI_TxRxHalfCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(.text.HAL_SPI_TxRxHalfCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAHalfTransmitReceiveCplt
</UL>

<P><STRONG><a name="[10c]"></a>HAL_SYSTICK_Config</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config))
<BR><BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[141]"></a>HAL_TIMEx_BreakCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback))
<BR><BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[143]"></a>HAL_TIMEx_CommutCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback))
<BR><BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[164]"></a>HAL_TIMEx_MasterConfigSynchronization</STRONG> (Thumb, 186 bytes, Stack size 8 bytes, stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_TIMEx_MasterConfigSynchronization
</UL>
<BR>[Called By]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM6_Init
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
</UL>

<P><STRONG><a name="[139]"></a>HAL_TIM_Base_Init</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM14_Init
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM6_Init
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
</UL>

<P><STRONG><a name="[13a]"></a>HAL_TIM_Base_MspInit</STRONG> (Thumb, 162 bytes, Stack size 24 bytes, tim.o(.text.HAL_TIM_Base_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[191]"></a>HAL_TIM_Base_Start_IT</STRONG> (Thumb, 170 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT))
<BR><BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_btn_init
</UL>

<P><STRONG><a name="[163]"></a>HAL_TIM_ConfigClockSource</STRONG> (Thumb, 416 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_TIM_ConfigClockSource
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
</UL>

<P><STRONG><a name="[13d]"></a>HAL_TIM_IC_CaptureCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback))
<BR><BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[13c]"></a>HAL_TIM_IRQHandler</STRONG> (Thumb, 334 bytes, Stack size 16 bytes, stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = HAL_TIM_IRQHandler &rArr; HAL_TIM_PeriodElapsedCallback &rArr; ebtn_process &rArr; ebtn_process_with_curr_state &rArr; prv_process_btn
</UL>
<BR>[Calls]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_CommutCallback
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_TriggerCallback
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_BreakCallback
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_OC_DelayElapsedCallback
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_CaptureCallback
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_PulseFinishedCallback
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PeriodElapsedCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM8_TRG_COM_TIM14_IRQHandler
</UL>

<P><STRONG><a name="[13e]"></a>HAL_TIM_OC_DelayElapsedCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback))
<BR><BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[13f]"></a>HAL_TIM_PWM_PulseFinishedCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback))
<BR><BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[140]"></a>HAL_TIM_PeriodElapsedCallback</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, btn_app.o(.text.HAL_TIM_PeriodElapsedCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = HAL_TIM_PeriodElapsedCallback &rArr; ebtn_process &rArr; ebtn_process_with_curr_state &rArr; prv_process_btn
</UL>
<BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process
</UL>
<BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[142]"></a>HAL_TIM_TriggerCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback))
<BR><BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[145]"></a>HAL_UARTEx_ReceiveToIdle_DMA</STRONG> (Thumb, 458 bytes, Stack size 24 bytes, stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_UARTEx_ReceiveToIdle_DMA &rArr; HAL_DMA_Start_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>

<P><STRONG><a name="[146]"></a>HAL_UARTEx_RxEventCallback</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, usart_app.o(.text.HAL_UARTEx_RxEventCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; HAL_DMA_Start_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_put
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMARxHalfCplt
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAReceiveCplt
</UL>

<P><STRONG><a name="[147]"></a>HAL_UART_DMAStop</STRONG> (Thumb, 540 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_UART_DMAStop &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>

<P><STRONG><a name="[14c]"></a>HAL_UART_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAAbortOnError
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
</UL>

<P><STRONG><a name="[149]"></a>HAL_UART_IRQHandler</STRONG> (Thumb, 1392 bytes, Stack size 24 bytes, stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; HAL_DMA_Start_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxCpltCallback
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[14d]"></a>HAL_UART_Init</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(.text.HAL_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
</UL>

<P><STRONG><a name="[14e]"></a>HAL_UART_MspInit</STRONG> (Thumb, 424 bytes, Stack size 56 bytes, usart.o(.text.HAL_UART_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[151]"></a>HAL_UART_Receive_IT</STRONG> (Thumb, 84 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT))
<BR><BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
</UL>

<P><STRONG><a name="[150]"></a>HAL_UART_RxCpltCallback</STRONG> (Thumb, 74 bytes, Stack size 0 bytes, usart_app.o(.text.HAL_UART_RxCpltCallback))
<BR><BR>[Calls]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAReceiveCplt
</UL>

<P><STRONG><a name="[178]"></a>HAL_UART_RxHalfCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMARxHalfCplt
</UL>

<P><STRONG><a name="[152]"></a>HAL_UART_Transmit</STRONG> (Thumb, 376 bytes, Stack size 24 bytes, stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_printf
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc16_calculate_exam
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>

<P><STRONG><a name="[14b]"></a>HAL_UART_TxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[9]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[155]"></a>MX_ADC1_Init</STRONG> (Thumb, 168 bytes, Stack size 32 bytes, adc.o(.text.MX_ADC1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = MX_ADC1_Init &rArr; HAL_ADC_Init &rArr; HAL_ADC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start_DMA
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[157]"></a>MX_DMA_Init</STRONG> (Thumb, 156 bytes, Stack size 16 bytes, dma.o(.text.MX_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = MX_DMA_Init &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[158]"></a>MX_FATFS_Init</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, fatfs.o(.text.MX_FATFS_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = MX_FATFS_Init &rArr; FATFS_LinkDriver
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FATFS_LinkDriver
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[15a]"></a>MX_GPIO_Init</STRONG> (Thumb, 320 bytes, Stack size 56 bytes, gpio.o(.text.MX_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = MX_GPIO_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[15b]"></a>MX_I2C1_Init</STRONG> (Thumb, 110 bytes, Stack size 8 bytes, i2c.o(.text.MX_I2C1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = MX_I2C1_Init &rArr; HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2CEx_ConfigDigitalFilter
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2CEx_ConfigAnalogFilter
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[15e]"></a>MX_RTC_Init</STRONG> (Thumb, 140 bytes, Stack size 32 bytes, rtc.o(.text.MX_RTC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = MX_RTC_Init &rArr; HAL_RTC_Init &rArr; HAL_RTC_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetDate
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetTime
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_Init
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[223]"></a>MX_SDIO_SD_Init</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, sdio.o(.text.MX_SDIO_SD_Init))
<BR><BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[15f]"></a>MX_SPI1_Init</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, spi.o(.text.MX_SPI1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = MX_SPI1_Init &rArr; HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[160]"></a>MX_SPI2_Init</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, spi.o(.text.MX_SPI2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = MX_SPI2_Init &rArr; HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[161]"></a>MX_TIM14_Init</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, tim.o(.text.MX_TIM14_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = MX_TIM14_Init &rArr; HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[162]"></a>MX_TIM3_Init</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, tim.o(.text.MX_TIM3_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = MX_TIM3_Init &rArr; HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[165]"></a>MX_TIM6_Init</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, tim.o(.text.MX_TIM6_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = MX_TIM6_Init &rArr; HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[166]"></a>MX_USART1_UART_Init</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, usart.o(.text.MX_USART1_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = MX_USART1_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[167]"></a>MX_USART2_UART_Init</STRONG> (Thumb, 108 bytes, Stack size 8 bytes, usart.o(.text.MX_USART2_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = MX_USART2_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.MemManage_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.NMI_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[168]"></a>OLED_Clear</STRONG> (Thumb, 540 bytes, Stack size 56 bytes, oled.o(.text.OLED_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = OLED_Clear &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
</UL>

<P><STRONG><a name="[169]"></a>OLED_Init</STRONG> (Thumb, 706 bytes, Stack size 48 bytes, oled.o(.text.OLED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = OLED_Init &rArr; OLED_Clear &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[16a]"></a>OLED_Set_Position</STRONG> (Thumb, 114 bytes, Stack size 40 bytes, oled.o(.text.OLED_Set_Position))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = OLED_Set_Position &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
</UL>

<P><STRONG><a name="[16b]"></a>OLED_ShowChar</STRONG> (Thumb, 932 bytes, Stack size 72 bytes, oled.o(.text.OLED_ShowChar))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = OLED_ShowChar &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowStr
</UL>

<P><STRONG><a name="[16c]"></a>OLED_ShowStr</STRONG> (Thumb, 64 bytes, Stack size 24 bytes, oled.o(.text.OLED_ShowStr))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = OLED_ShowStr &rArr; OLED_ShowChar &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>
<BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Oled_Printf
</UL>

<P><STRONG><a name="[16d]"></a>OLED_Write_data</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, oled.o(.text.OLED_Write_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = OLED_Write_data &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
</UL>

<P><STRONG><a name="[16e]"></a>Oled_Printf</STRONG> (Thumb, 52 bytes, Stack size 160 bytes, oled_app.o(.text.Oled_Printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 360<LI>Call Chain = Oled_Printf &rArr; OLED_ShowStr &rArr; OLED_ShowChar &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowStr
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
</UL>

<P><STRONG><a name="[f]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[129]"></a>SDIO_ConfigData</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, stm32f4xx_ll_sdmmc.o(.text.SDIO_ConfigData))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SDIO_ConfigData
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>

<P><STRONG><a name="[123]"></a>SDIO_GetPowerState</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDIO_GetPowerState))
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[117]"></a>SDIO_GetResponse</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDIO_GetResponse))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardState
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[11b]"></a>SDIO_Init</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, stm32f4xx_ll_sdmmc.o(.text.SDIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SDIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[11f]"></a>SDIO_PowerState_ON</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDIO_PowerState_ON))
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[12c]"></a>SDIO_ReadFIFO</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDIO_ReadFIFO))
<BR><BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>

<P><STRONG><a name="[130]"></a>SDIO_WriteFIFO</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDIO_WriteFIFO))
<BR><BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
</UL>

<P><STRONG><a name="[119]"></a>SDMMC_CmdAppCommand</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdAppCommand))
<BR><BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>

<P><STRONG><a name="[122]"></a>SDMMC_CmdAppOperCommand</STRONG> (Thumb, 144 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdAppOperCommand))
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[116]"></a>SDMMC_CmdBlockLength</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdBlockLength))
<BR><BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>

<P><STRONG><a name="[11a]"></a>SDMMC_CmdBusWidth</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdBusWidth))
<BR><BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
</UL>

<P><STRONG><a name="[120]"></a>SDMMC_CmdGoIdleState</STRONG> (Thumb, 96 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdGoIdleState))
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[121]"></a>SDMMC_CmdOperCond</STRONG> (Thumb, 166 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdOperCond))
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[12a]"></a>SDMMC_CmdReadMultiBlock</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdReadMultiBlock))
<BR><BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
</UL>

<P><STRONG><a name="[12b]"></a>SDMMC_CmdReadSingleBlock</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdReadSingleBlock))
<BR><BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
</UL>

<P><STRONG><a name="[128]"></a>SDMMC_CmdSelDesel</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdSelDesel))
<BR><BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[125]"></a>SDMMC_CmdSendCID</STRONG> (Thumb, 156 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdSendCID))
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[127]"></a>SDMMC_CmdSendCSD</STRONG> (Thumb, 156 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdSendCSD))
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[171]"></a>SDMMC_CmdSendSCR</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdSendSCR))
<BR><BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SD_FindSCR
</UL>

<P><STRONG><a name="[11c]"></a>SDMMC_CmdSendStatus</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdSendStatus))
<BR><BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardState
</UL>

<P><STRONG><a name="[126]"></a>SDMMC_CmdSetRelAdd</STRONG> (Thumb, 204 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdSetRelAdd))
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_InitCard
</UL>

<P><STRONG><a name="[12d]"></a>SDMMC_CmdStopTransfer</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdStopTransfer))
<BR><BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ReadBlocks
</UL>

<P><STRONG><a name="[12e]"></a>SDMMC_CmdWriteMultiBlock</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdWriteMultiBlock))
<BR><BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_GetCmdResp1
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
</UL>

<P><STRONG><a name="[12f]"></a>SDMMC_CmdWriteSingleBlock</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_CmdWriteSingleBlock))
<BR><BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_WriteBlocks
</UL>

<P><STRONG><a name="[170]"></a>SDMMC_GetCmdResp1</STRONG> (Thumb, 358 bytes, Stack size 0 bytes, stm32f4xx_ll_sdmmc.o(.text.SDMMC_GetCmdResp1))
<BR><BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendStatus
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendSCR
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBusWidth
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSelDesel
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdStopTransfer
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdWriteMultiBlock
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadMultiBlock
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdReadSingleBlock
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
</UL>

<P><STRONG><a name="[83]"></a>SD_initialize</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, sd_diskio.o(.text.SD_initialize))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = SD_initialize &rArr; BSP_SD_Init &rArr; HAL_SD_ConfigWideBusOperation &rArr; SD_FindSCR &rArr; SDIO_ConfigData
</UL>
<BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_GetCardState
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_Init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sd_diskio.o(.rodata.SD_Driver)
</UL>
<P><STRONG><a name="[87]"></a>SD_ioctl</STRONG> (Thumb, 86 bytes, Stack size 40 bytes, sd_diskio.o(.text.SD_ioctl))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SD_ioctl &rArr; BSP_SD_GetCardInfo &rArr; HAL_SD_GetCardInfo
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_GetCardInfo
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sd_diskio.o(.rodata.SD_Driver)
</UL>
<P><STRONG><a name="[85]"></a>SD_read</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, sd_diskio.o(.text.SD_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = SD_read &rArr; BSP_SD_ReadBlocks &rArr; HAL_SD_ReadBlocks &rArr; SDIO_ConfigData
</UL>
<BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_GetCardState
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_ReadBlocks
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sd_diskio.o(.rodata.SD_Driver)
</UL>
<P><STRONG><a name="[84]"></a>SD_status</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, sd_diskio.o(.text.SD_status))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SD_status &rArr; BSP_SD_GetCardState &rArr; HAL_SD_GetCardState
</UL>
<BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_GetCardState
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sd_diskio.o(.rodata.SD_Driver)
</UL>
<P><STRONG><a name="[86]"></a>SD_write</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, sd_diskio.o(.text.SD_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = SD_write &rArr; BSP_SD_WriteBlocks &rArr; HAL_SD_WriteBlocks &rArr; SDIO_ConfigData
</UL>
<BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_GetCardState
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_WriteBlocks
</UL>
<BR>[Address Reference Count : 1]<UL><LI> sd_diskio.o(.rodata.SD_Driver)
</UL>
<P><STRONG><a name="[35]"></a>SPI2_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.SPI2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SPI2_IRQHandler &rArr; HAL_SPI_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>SysTick_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.SysTick_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[177]"></a>SystemClock_Config</STRONG> (Thumb, 160 bytes, Stack size 88 bytes, main.o(.text.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6c]"></a>SystemInit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, system_stm32f4xx.o(.text.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(.text)
</UL>
<P><STRONG><a name="[3e]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.TIM8_TRG_COM_TIM14_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = TIM8_TRG_COM_TIM14_IRQHandler &rArr; HAL_TIM_IRQHandler &rArr; HAL_TIM_PeriodElapsedCallback &rArr; ebtn_process &rArr; ebtn_process_with_curr_state &rArr; prv_process_btn
</UL>
<BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[13b]"></a>TIM_Base_SetConfig</STRONG> (Thumb, 332 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[36]"></a>USART1_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = USART1_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; HAL_DMA_Start_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>USART2_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.USART2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = USART2_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; HAL_DMA_Start_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text.UsageFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[17a]"></a>adc_control_init</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, adc_app.o(.text.adc_control_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = adc_control_init
</UL>
<BR>[Calls]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_sample_cycle
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_init
</UL>

<P><STRONG><a name="[17c]"></a>adc_format_hex_string</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, adc_app.o(.text.adc_format_hex_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = adc_format_hex_string &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_hidedata_with_voltage
</UL>

<P><STRONG><a name="[4]"></a>adc_led1_blink_task</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, adc_app.o(.text.adc_led1_blink_task))
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data.scheduler_task)
</UL>
<P><STRONG><a name="[17e]"></a>adc_process_sample</STRONG> (Thumb, 660 bytes, Stack size 400 bytes, adc_app.o(.text.adc_process_sample))
<BR><BR>[Stack]<UL><LI>Max Depth = 11016<LI>Call Chain = adc_process_sample &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_sample_data
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_hidedata_with_voltage
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_overlimit_data
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_send_binary_data
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_get_unix_timestamp_now
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_all_limits
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;voltage_calibrate_segmented
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_all_ratios
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_board_read_channel
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_format_current_time_string
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_limit
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_start_sampling
<LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_task
</UL>

<P><STRONG><a name="[18d]"></a>adc_start_sampling</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, adc_app.o(.text.adc_start_sampling))
<BR><BR>[Stack]<UL><LI>Max Depth = 11024<LI>Call Chain = adc_start_sampling &rArr; adc_process_sample &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_process_sample
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_start_sample_cmd
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_start_cmd
</UL>

<P><STRONG><a name="[215]"></a>adc_stop_sampling</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, adc_app.o(.text.adc_stop_sampling))
<BR><BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_stop_sample_cmd
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_stop_cmd
</UL>

<P><STRONG><a name="[3]"></a>adc_task</STRONG> (Thumb, 696 bytes, Stack size 24 bytes, adc_app.o(.text.adc_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 11040<LI>Call Chain = adc_task &rArr; adc_process_sample &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_process_sample
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_limit
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_ratio
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data.scheduler_task)
</UL>
<P><STRONG><a name="[18f]"></a>app_btn_init</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, btn_app.o(.text.app_btn_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = app_btn_init &rArr; ebtn_init
</UL>
<BR>[Calls]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_init
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[192]"></a>auto_repair_inconsistent_state</STRONG> (Thumb, 236 bytes, Stack size 16 bytes, sd_app.o(.text.auto_repair_inconsistent_state))
<BR><BR>[Stack]<UL><LI>Max Depth = 1224<LI>Call Chain = auto_repair_inconsistent_state &rArr; check_test_stage_consistency &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_test_stage_consistency
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_write_with_retry
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_check_state_cmd
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recover_from_sd_card_error
</UL>

<P><STRONG><a name="[196]"></a>binary_protocol_generate</STRONG> (Thumb, 810 bytes, Stack size 64 bytes, usart_app.o(.text.binary_protocol_generate))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = binary_protocol_generate &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_single_read
</UL>

<P><STRONG><a name="[198]"></a>binary_protocol_parse</STRONG> (Thumb, 356 bytes, Stack size 64 bytes, usart_app.o(.text.binary_protocol_parse))
<BR><BR>[Stack]<UL><LI>Max Depth = 632<LI>Call Chain = binary_protocol_parse &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc16_calculate_exam
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_protocol_cmd
</UL>

<P><STRONG><a name="[1]"></a>btn_task</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, btn_app.o(.text.btn_task))
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data.scheduler_task)
</UL>
<P><STRONG><a name="[195]"></a>check_test_stage_consistency</STRONG> (Thumb, 164 bytes, Stack size 640 bytes, sd_app.o(.text.check_test_stage_consistency))
<BR><BR>[Stack]<UL><LI>Max Depth = 1208<LI>Call Chain = check_test_stage_consistency &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_print_system_state
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_generate_report
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_repair_inconsistent_state
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_check_state_cmd
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recover_from_sd_card_error
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_system_status_cmd
</UL>

<P><STRONG><a name="[19d]"></a>config_app_init</STRONG> (Thumb, 86 bytes, Stack size 80 bytes, config_app.o(.text.config_app_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = config_app_init &rArr; flash_direct_read &rArr; spi_flash_buffer_read &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_read
</UL>
<BR>[Called By]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_init
</UL>

<P><STRONG><a name="[1b8]"></a>config_calculate_checksum</STRONG> (Thumb, 224 bytes, Stack size 0 bytes, config_app.o(.text.config_calculate_checksum))
<BR><BR>[Called By]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_set_all_limits
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_set_all_ratios
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_set_ratio
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_set_limit
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_sync_sd_to_memory_and_flash
</UL>

<P><STRONG><a name="[19f]"></a>config_check_sd_flash_sync_needed</STRONG> (Thumb, 204 bytes, Stack size 104 bytes, config_app.o(.text.config_check_sd_flash_sync_needed))
<BR><BR>[Stack]<UL><LI>Max Depth = 1160<LI>Call Chain = config_check_sd_flash_sync_needed &rArr; config_safe_read_from_sd &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_safe_read_from_sd
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_read
</UL>
<BR>[Called By]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_init
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_sync_sd_to_memory_and_flash
</UL>

<P><STRONG><a name="[1a1]"></a>config_display_from_sd</STRONG> (Thumb, 244 bytes, Stack size 848 bytes, config_app.o(.text.config_display_from_sd))
<BR><BR>[Stack]<UL><LI>Max Depth = 1416<LI>Call Chain = config_display_from_sd &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_stat
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_read
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_conf_cmd
</UL>

<P><STRONG><a name="[1a4]"></a>config_ensure_ini_file</STRONG> (Thumb, 110 bytes, Stack size 584 bytes, config_app.o(.text.config_ensure_ini_file))
<BR><BR>[Stack]<UL><LI>Max Depth = 944<LI>Call Chain = config_ensure_ini_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>
<BR>[Called By]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_init
</UL>

<P><STRONG><a name="[183]"></a>config_get_all_limits</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, config_app.o(.text.config_get_all_limits))
<BR><BR>[Called By]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_check_limits
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_process_sample
</UL>

<P><STRONG><a name="[181]"></a>config_get_all_ratios</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, config_app.o(.text.config_get_all_ratios))
<BR><BR>[Called By]<UL><LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_apply_ratios
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_process_sample
</UL>

<P><STRONG><a name="[189]"></a>config_get_limit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, config_app.o(.text.config_get_limit))
<BR><BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_system_status_cmd
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_config_save_cmd
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_limit_cmd
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_process_sample
<LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_task
</UL>

<P><STRONG><a name="[1a6]"></a>config_get_limits_direct_from_sd</STRONG> (Thumb, 840 bytes, Stack size 696 bytes, config_app.o(.text.config_get_limits_direct_from_sd))
<BR><BR>[Stack]<UL><LI>Max Depth = 1264<LI>Call Chain = config_get_limits_direct_from_sd &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_gets
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strchr
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_get_limit_cmd
</UL>

<P><STRONG><a name="[18e]"></a>config_get_ratio</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, config_app.o(.text.config_get_ratio))
<BR><BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_system_status_cmd
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_config_save_cmd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_ratio_cmd
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
<LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_task
</UL>

<P><STRONG><a name="[1ac]"></a>config_get_ratios_direct_from_sd</STRONG> (Thumb, 832 bytes, Stack size 696 bytes, config_app.o(.text.config_get_ratios_direct_from_sd))
<BR><BR>[Stack]<UL><LI>Max Depth = 1264<LI>Call Chain = config_get_ratios_direct_from_sd &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_gets
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strchr
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_get_ratio_cmd
</UL>

<P><STRONG><a name="[17b]"></a>config_get_sample_cycle</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, config_app.o(.text.config_get_sample_cycle))
<BR><BR>[Called By]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_control_init
</UL>

<P><STRONG><a name="[1a0]"></a>config_safe_read_from_sd</STRONG> (Thumb, 720 bytes, Stack size 696 bytes, config_app.o(.text.config_safe_read_from_sd))
<BR><BR>[Stack]<UL><LI>Max Depth = 1056<LI>Call Chain = config_safe_read_from_sd &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_gets
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strchr
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
</UL>
<BR>[Called By]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_sync_sd_to_memory_and_flash
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_check_sd_flash_sync_needed
</UL>

<P><STRONG><a name="[1af]"></a>config_safe_sd_test</STRONG> (Thumb, 580 bytes, Stack size 912 bytes, config_app.o(.text.config_safe_sd_test))
<BR><BR>[Stack]<UL><LI>Max Depth = 1480<LI>Call Chain = config_safe_sd_test &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_read
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_conf_test_cmd
</UL>

<P><STRONG><a name="[1b2]"></a>config_save_to_flash</STRONG> (Thumb, 212 bytes, Stack size 128 bytes, config_app.o(.text.config_save_to_flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = config_save_to_flash &rArr; flash_direct_write &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_read
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_write
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_interval_cmd
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_limit_cmd
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_ratio_cmd
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_config_save_cmd
</UL>

<P><STRONG><a name="[1b4]"></a>config_save_to_sd</STRONG> (Thumb, 872 bytes, Stack size 944 bytes, config_app.o(.text.config_save_to_sd))
<BR><BR>[Stack]<UL><LI>Max Depth = 1512<LI>Call Chain = config_save_to_sd &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getfree
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_limit_cmd
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_ratio_cmd
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_config_save_cmd
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_conf_fix_cmd
</UL>

<P><STRONG><a name="[1b7]"></a>config_set_all_limits</STRONG> (Thumb, 108 bytes, Stack size 8 bytes, config_app.o(.text.config_set_all_limits))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = config_set_all_limits
</UL>
<BR>[Calls]<UL><LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_calculate_checksum
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_limit_cmd
</UL>

<P><STRONG><a name="[1b9]"></a>config_set_all_ratios</STRONG> (Thumb, 104 bytes, Stack size 8 bytes, config_app.o(.text.config_set_all_ratios))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = config_set_all_ratios
</UL>
<BR>[Calls]<UL><LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_calculate_checksum
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_ratio_cmd
</UL>

<P><STRONG><a name="[1ba]"></a>config_set_limit</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, config_app.o(.text.config_set_limit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = config_set_limit
</UL>
<BR>[Calls]<UL><LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_calculate_checksum
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_limit_cmd
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
</UL>

<P><STRONG><a name="[1bb]"></a>config_set_ratio</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, config_app.o(.text.config_set_ratio))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = config_set_ratio
</UL>
<BR>[Calls]<UL><LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_calculate_checksum
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_ratio_cmd
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
</UL>

<P><STRONG><a name="[212]"></a>config_set_sample_cycle</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, config_app.o(.text.config_set_sample_cycle))
<BR><BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_interval_cmd
</UL>

<P><STRONG><a name="[1bc]"></a>config_sync_sd_to_memory_and_flash</STRONG> (Thumb, 392 bytes, Stack size 176 bytes, config_app.o(.text.config_sync_sd_to_memory_and_flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 1336<LI>Call Chain = config_sync_sd_to_memory_and_flash &rArr; config_check_sd_flash_sync_needed &rArr; config_safe_read_from_sd &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_safe_read_from_sd
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_calculate_checksum
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_check_sd_flash_sync_needed
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_read
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_write
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_init
</UL>

<P><STRONG><a name="[19a]"></a>crc16_calculate_exam</STRONG> (Thumb, 1068 bytes, Stack size 136 bytes, usart_app.o(.text.crc16_calculate_exam))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = crc16_calculate_exam &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc16_calculate_set_device_id
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_stop_read
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_get_device_id
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_set_device_id
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;binary_protocol_parse
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_send_binary_data
</UL>

<P><STRONG><a name="[1bd]"></a>crc16_calculate_set_device_id</STRONG> (Thumb, 952 bytes, Stack size 24 bytes, usart_app.o(.text.crc16_calculate_set_device_id))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = crc16_calculate_set_device_id
</UL>
<BR>[Called By]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc16_calculate_exam
</UL>

<P><STRONG><a name="[204]"></a>current_calibrate_linear</STRONG> (Thumb, 1064 bytes, Stack size 0 bytes, sampling_board_app.o(.text.current_calibrate_linear))
<BR><BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sb_check_cmd
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_board_task
</UL>

<P><STRONG><a name="[205]"></a>current_filter_update</STRONG> (Thumb, 100 bytes, Stack size 0 bytes, sampling_board_app.o(.text.current_filter_update))
<BR><BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sb_check_cmd
</UL>

<P><STRONG><a name="[1c2]"></a>debug_print_system_state</STRONG> (Thumb, 1352 bytes, Stack size 1112 bytes, sd_app.o(.text.debug_print_system_state))
<BR><BR>[Stack]<UL><LI>Max Depth = 2320<LI>Call Chain = debug_print_system_state &rArr; check_test_stage_consistency &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_test_stage_consistency
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_debug_state_cmd
</UL>

<P><STRONG><a name="[1f3]"></a>device_id_get</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, selftest_app.o(.text.device_id_get))
<BR><BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_get_device_id_cmd
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_stop_read
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_continuous_read
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_single_read
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_get_device_id
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_set_device_id
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_protocol_cmd
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_send_binary_data
</UL>

<P><STRONG><a name="[231]"></a>device_id_init</STRONG> (Thumb, 266 bytes, Stack size 8 bytes, selftest_app.o(.text.device_id_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = device_id_init
</UL>
<BR>[Called By]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_init
</UL>

<P><STRONG><a name="[1c4]"></a>device_id_load_from_flash</STRONG> (Thumb, 116 bytes, Stack size 48 bytes, selftest_app.o(.text.device_id_load_from_flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = device_id_load_from_flash &rArr; flash_direct_write &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_read
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_write
</UL>
<BR>[Called By]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_init
</UL>

<P><STRONG><a name="[1c5]"></a>device_id_save_to_flash</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, selftest_app.o(.text.device_id_save_to_flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = device_id_save_to_flash &rArr; flash_direct_write &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_write
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_device_id_cmd
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_set_device_id
</UL>

<P><STRONG><a name="[1f9]"></a>device_id_set</STRONG> (Thumb, 224 bytes, Stack size 8 bytes, selftest_app.o(.text.device_id_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = device_id_set
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_device_id_cmd
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_set_device_id
</UL>

<P><STRONG><a name="[1d9]"></a>disk_initialize</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, diskio.o(.text.disk_initialize))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = disk_initialize
</UL>
<BR>[Called By]<UL><LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkfs
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>

<P><STRONG><a name="[1da]"></a>disk_ioctl</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, diskio.o(.text.disk_ioctl))
<BR><BR>[Called By]<UL><LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkfs
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
</UL>

<P><STRONG><a name="[1db]"></a>disk_read</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, diskio.o(.text.disk_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>

<P><STRONG><a name="[1b5]"></a>disk_status</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, diskio.o(.text.disk_status))
<BR><BR>[Called By]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_sd
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_closedir
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
</UL>

<P><STRONG><a name="[1d5]"></a>disk_write</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, diskio.o(.text.disk_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = disk_write
</UL>
<BR>[Called By]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkfs
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_window
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[228]"></a>display_mode_get</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, btn_app.o(.text.display_mode_get))
<BR><BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
</UL>

<P><STRONG><a name="[229]"></a>display_mode_get_name</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, btn_app.o(.text.display_mode_get_name))
<BR><BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
</UL>

<P><STRONG><a name="[190]"></a>ebtn_init</STRONG> (Thumb, 76 bytes, Stack size 32 bytes, ebtn.o(.text.ebtn_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = ebtn_init
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_btn_init
</UL>

<P><STRONG><a name="[144]"></a>ebtn_process</STRONG> (Thumb, 154 bytes, Stack size 40 bytes, ebtn.o(.text.ebtn_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = ebtn_process &rArr; ebtn_process_with_curr_state &rArr; prv_process_btn
</UL>
<BR>[Calls]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
</UL>
<BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PeriodElapsedCallback
</UL>

<P><STRONG><a name="[1ce]"></a>ebtn_process_with_curr_state</STRONG> (Thumb, 474 bytes, Stack size 56 bytes, ebtn.o(.text.ebtn_process_with_curr_state))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = ebtn_process_with_curr_state &rArr; prv_process_btn
</UL>
<BR>[Calls]<UL><LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_process_btn
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process
</UL>

<P><STRONG><a name="[19c]"></a>f_close</STRONG> (Thumb, 126 bytes, Stack size 8 bytes, ff.o(.text.f_close))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = f_close &rArr; f_sync &rArr; sync_fs &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
</UL>
<BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_latest_incomplete_file
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_safe_read_from_sd
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_limits_direct_from_sd
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_ratios_direct_from_sd
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_print_system_state
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_generate_report
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_validate_all_files
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_count_records_in_file
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_test_stage_consistency
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_display_from_sd
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_sd
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_conf_fix_cmd
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_safe_sd_test
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_ensure_ini_file
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_data
</UL>

<P><STRONG><a name="[1d0]"></a>f_closedir</STRONG> (Thumb, 120 bytes, Stack size 8 bytes, ff.o(.text.f_closedir))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = f_closedir
</UL>
<BR>[Calls]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
</UL>
<BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_latest_incomplete_file
</UL>

<P><STRONG><a name="[1b6]"></a>f_getfree</STRONG> (Thumb, 256 bytes, Stack size 72 bytes, ff.o(.text.f_getfree))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = f_getfree &rArr; find_volume &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>
<BR>[Called By]<UL><LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_check_and_reinit_if_needed
<LI><a href="#[239]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_comprehensive_check
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_sd
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sd_format_cmd
<LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_check_card_status
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sd_test_cmd
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_check_sd
</UL>

<P><STRONG><a name="[1a7]"></a>f_gets</STRONG> (Thumb, 104 bytes, Stack size 40 bytes, ff.o(.text.f_gets))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = f_gets &rArr; f_read &rArr; get_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_latest_incomplete_file
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_safe_read_from_sd
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_limits_direct_from_sd
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_ratios_direct_from_sd
</UL>

<P><STRONG><a name="[193]"></a>f_mkdir</STRONG> (Thumb, 682 bytes, Stack size 104 bytes, ff.o(.text.f_mkdir))
<BR><BR>[Stack]<UL><LI>Max Depth = 360<LI>Call Chain = f_mkdir &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
</UL>
<BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_repair_inconsistent_state
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_create_directories
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_app_init
</UL>

<P><STRONG><a name="[1d8]"></a>f_mkfs</STRONG> (Thumb, 1676 bytes, Stack size 88 bytes, ff.o(.text.f_mkfs))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = f_mkfs &rArr; get_fattime &rArr; HAL_RTC_GetDate
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_ioctl
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sd_format_cmd
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_app_init
</UL>

<P><STRONG><a name="[1b1]"></a>f_mount</STRONG> (Thumb, 168 bytes, Stack size 24 bytes, ff.o(.text.f_mount))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = f_mount &rArr; find_volume &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>
<BR>[Called By]<UL><LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_check_and_reinit_if_needed
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_sd
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_conf_fix_cmd
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_safe_sd_test
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sd_format_cmd
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_app_init
</UL>

<P><STRONG><a name="[19b]"></a>f_open</STRONG> (Thumb, 994 bytes, Stack size 104 bytes, ff.o(.text.f_open))
<BR><BR>[Stack]<UL><LI>Max Depth = 360<LI>Call Chain = f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
</UL>
<BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_latest_incomplete_file
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_safe_read_from_sd
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_limits_direct_from_sd
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_ratios_direct_from_sd
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_print_system_state
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_generate_report
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_validate_all_files
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_count_records_in_file
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_test_stage_consistency
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_display_from_sd
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_sd
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_conf_fix_cmd
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_safe_sd_test
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_ensure_ini_file
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_data
</UL>

<P><STRONG><a name="[1dc]"></a>f_opendir</STRONG> (Thumb, 176 bytes, Stack size 32 bytes, ff.o(.text.f_opendir))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = f_opendir &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;inc_lock
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>
<BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_latest_incomplete_file
</UL>

<P><STRONG><a name="[1c3]"></a>f_read</STRONG> (Thumb, 514 bytes, Stack size 48 bytes, ff.o(.text.f_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = f_read &rArr; get_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_gets
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_print_system_state
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_generate_report
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_validate_all_files
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_count_records_in_file
</UL>

<P><STRONG><a name="[1de]"></a>f_readdir</STRONG> (Thumb, 154 bytes, Stack size 24 bytes, ff.o(.text.f_readdir))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = f_readdir &rArr; dir_read &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fileinfo
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_read
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
</UL>
<BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_latest_incomplete_file
</UL>

<P><STRONG><a name="[1a3]"></a>f_stat</STRONG> (Thumb, 92 bytes, Stack size 72 bytes, ff.o(.text.f_stat))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = f_stat &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fileinfo
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>
<BR>[Called By]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_display_from_sd
</UL>

<P><STRONG><a name="[1a2]"></a>f_sync</STRONG> (Thumb, 200 bytes, Stack size 24 bytes, ff.o(.text.f_sync))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = f_sync &rArr; sync_fs &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
</UL>
<BR>[Called By]<UL><LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_force_sync_filesystem
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_display_from_sd
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_sd
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_data
</UL>

<P><STRONG><a name="[1b0]"></a>f_unlink</STRONG> (Thumb, 394 bytes, Stack size 152 bytes, ff.o(.text.f_unlink))
<BR><BR>[Stack]<UL><LI>Max Depth = 408<LI>Call Chain = f_unlink &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_read
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fs
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_conf_delete_cmd
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_safe_sd_test
</UL>

<P><STRONG><a name="[1a5]"></a>f_write</STRONG> (Thumb, 566 bytes, Stack size 40 bytes, ff.o(.text.f_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_sd
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_safe_sd_test
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_ensure_ini_file
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_data
</UL>

<P><STRONG><a name="[1eb]"></a>ff_convert</STRONG> (Thumb, 148 bytes, Stack size 20 bytes, cc936.o(.text.ff_convert))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = ff_convert
</UL>
<BR>[Called By]<UL><LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fileinfo
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
</UL>

<P><STRONG><a name="[1d2]"></a>ff_memalloc</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, syscall.o(.text.ff_memalloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = ff_memalloc &rArr; malloc
</UL>
<BR>[Calls]<UL><LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_stat
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[1d7]"></a>ff_memfree</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, syscall.o(.text.ff_memfree))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ff_memfree &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
</UL>
<BR>[Called By]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_stat
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[1ca]"></a>ff_wtoupper</STRONG> (Thumb, 192 bytes, Stack size 8 bytes, cc936.o(.text.ff_wtoupper))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ff_wtoupper
</UL>
<BR>[Called By]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
</UL>

<P><STRONG><a name="[1e5]"></a>flash_app_init</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, flash_app.o(.text.flash_app_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = flash_app_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
</UL>
<BR>[Called By]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_init
</UL>

<P><STRONG><a name="[1e7]"></a>flash_direct_erase_sector</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, flash_app.o(.text.flash_direct_erase_sector))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = flash_direct_erase_sector &rArr; spi_flash_sector_erase &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
</UL>
<BR>[Called By]<UL><LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_reset_boot_count
</UL>

<P><STRONG><a name="[19e]"></a>flash_direct_read</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, flash_app.o(.text.flash_direct_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = flash_direct_read &rArr; spi_flash_buffer_read &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
</UL>
<BR>[Called By]<UL><LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_check_system_init_logged
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_get_cached_log_count
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_reset_boot_count
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_config_read_cmd
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_flash
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_display_from_sd
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_safe_sd_test
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_sync_sd_to_memory_and_flash
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_check_sd_flash_sync_needed
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_stage_init
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_app_init
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_load_from_flash
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_app_init
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cache_log_to_flash
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_restore_logs_from_flash
</UL>

<P><STRONG><a name="[1b3]"></a>flash_direct_write</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, flash_app.o(.text.flash_direct_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = flash_direct_write &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_write
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
</UL>
<BR>[Called By]<UL><LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_mark_system_init_logged
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_save_to_flash
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recover_from_sd_card_error
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_test_stage_to_initial
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_reset_boot_count
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_flash
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_sync_sd_to_memory_and_flash
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_app_init
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_load_from_flash
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_write_with_retry
<LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cache_log_to_flash
</UL>

<P><STRONG><a name="[194]"></a>flash_write_with_retry</STRONG> (Thumb, 356 bytes, Stack size 24 bytes, sd_app.o(.text.flash_write_with_retry))
<BR><BR>[Stack]<UL><LI>Max Depth = 592<LI>Call Chain = flash_write_with_retry &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_write
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_repair_inconsistent_state
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_test_stage_to_initial
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_reset_boot_count
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smart_log_switch
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_stage_init
</UL>

<P><STRONG><a name="[1d4]"></a>get_fattime</STRONG> (Thumb, 116 bytes, Stack size 32 bytes, fatfs.o(.text.get_fattime))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = get_fattime &rArr; HAL_RTC_GetDate
</UL>
<BR>[Calls]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
</UL>
<BR>[Called By]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkfs
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[1ee]"></a>handle_binary_continuous_read</STRONG> (Thumb, 868 bytes, Stack size 352 bytes, usart_app.o(.text.handle_binary_continuous_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 10968<LI>Call Chain = handle_binary_continuous_read &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_printf
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_start_continuous_sampling_binary
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_get
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_update_timestamp
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_apply_ratios
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_read_data
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_protocol_cmd
</UL>

<P><STRONG><a name="[1f4]"></a>handle_binary_get_device_id</STRONG> (Thumb, 324 bytes, Stack size 144 bytes, usart_app.o(.text.handle_binary_get_device_id))
<BR><BR>[Stack]<UL><LI>Max Depth = 10760<LI>Call Chain = handle_binary_get_device_id &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_printf
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc16_calculate_exam
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_get
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_protocol_cmd
</UL>

<P><STRONG><a name="[1f5]"></a>handle_binary_protocol_cmd</STRONG> (Thumb, 328 bytes, Stack size 48 bytes, usart_app.o(.text.handle_binary_protocol_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 11016<LI>Call Chain = handle_binary_protocol_cmd &rArr; handle_binary_continuous_read &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_stop_read
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_continuous_read
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_single_read
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_get_device_id
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_set_device_id
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_printf
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;binary_protocol_parse
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_get
</UL>
<BR>[Called By]<UL><LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
</UL>

<P><STRONG><a name="[1f6]"></a>handle_binary_set_device_id</STRONG> (Thumb, 344 bytes, Stack size 152 bytes, usart_app.o(.text.handle_binary_set_device_id))
<BR><BR>[Stack]<UL><LI>Max Depth = 10768<LI>Call Chain = handle_binary_set_device_id &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_save_to_flash
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_set
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_printf
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc16_calculate_exam
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_get
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_protocol_cmd
</UL>

<P><STRONG><a name="[1f7]"></a>handle_binary_single_read</STRONG> (Thumb, 288 bytes, Stack size 280 bytes, usart_app.o(.text.handle_binary_single_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 10896<LI>Call Chain = handle_binary_single_read &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_printf
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;binary_protocol_generate
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_get
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_update_timestamp
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_apply_ratios
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_read_data
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_protocol_cmd
</UL>

<P><STRONG><a name="[1f8]"></a>handle_binary_stop_read</STRONG> (Thumb, 256 bytes, Stack size 72 bytes, usart_app.o(.text.handle_binary_stop_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 10688<LI>Call Chain = handle_binary_stop_read &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_printf
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_stop_continuous_sampling
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc16_calculate_exam
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_get
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_protocol_cmd
</UL>

<P><STRONG><a name="[9f]"></a>handle_check_state_cmd</STRONG> (Thumb, 140 bytes, Stack size 72 bytes, usart_app.o(.text.handle_check_state_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10688<LI>Call Chain = handle_check_state_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_repair_inconsistent_state
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_test_stage_consistency
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_stage_get_current_stage
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[92]"></a>handle_conf_cmd</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, usart_app.o(.text.handle_conf_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 1424<LI>Call Chain = handle_conf_cmd &rArr; config_display_from_sd &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_display_from_sd
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[8f]"></a>handle_conf_delete_cmd</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, usart_app.o(.text.handle_conf_delete_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 576<LI>Call Chain = handle_conf_delete_cmd &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[91]"></a>handle_conf_fix_cmd</STRONG> (Thumb, 208 bytes, Stack size 576 bytes, usart_app.o(.text.handle_conf_fix_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 2088<LI>Call Chain = handle_conf_fix_cmd &rArr; config_save_to_sd &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_sd
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[90]"></a>handle_conf_test_cmd</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, usart_app.o(.text.handle_conf_test_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 1488<LI>Call Chain = handle_conf_test_cmd &rArr; config_safe_sd_test &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_safe_sd_test
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[94]"></a>handle_config_read_cmd</STRONG> (Thumb, 84 bytes, Stack size 56 bytes, usart_app.o(.text.handle_config_read_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 624<LI>Call Chain = handle_config_read_cmd &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_read
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[93]"></a>handle_config_save_cmd</STRONG> (Thumb, 108 bytes, Stack size 8 bytes, usart_app.o(.text.handle_config_save_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10624<LI>Call Chain = handle_config_save_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_flash
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_sd
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_limit
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_ratio
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[a3]"></a>handle_debug_state_cmd</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, usart_app.o(.text.handle_debug_state_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10624<LI>Call Chain = handle_debug_state_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_print_system_state
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[b2]"></a>handle_get_data_cmd</STRONG> (Thumb, 58 bytes, Stack size 136 bytes, usart_app.o(.text.handle_get_data_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 760<LI>Call Chain = handle_get_data_cmd &rArr; multi_channel_read_data &rArr; sampling_board_read_channel &rArr; GD30AD3344_AD_Read &rArr; spi_gd30ad3344_send_halfword_dma &rArr; uart_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_printf
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_format_output
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_update_timestamp
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_check_limits
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_apply_ratios
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_read_data
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[aa]"></a>handle_get_device_id_cmd</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, usart_app.o(.text.handle_get_device_id_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 576<LI>Call Chain = handle_get_device_id_cmd &rArr; uart_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_printf
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[b0]"></a>handle_get_limit_cmd</STRONG> (Thumb, 76 bytes, Stack size 56 bytes, usart_app.o(.text.handle_get_limit_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 1320<LI>Call Chain = handle_get_limit_cmd &rArr; config_get_limits_direct_from_sd &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_limits_direct_from_sd
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_printf
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[ae]"></a>handle_get_ratio_cmd</STRONG> (Thumb, 76 bytes, Stack size 56 bytes, usart_app.o(.text.handle_get_ratio_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 1320<LI>Call Chain = handle_get_ratio_cmd &rArr; config_get_ratios_direct_from_sd &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_ratios_direct_from_sd
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_printf
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[ac]"></a>handle_get_rtc_cmd</STRONG> (Thumb, 44 bytes, Stack size 40 bytes, usart_app.o(.text.handle_get_rtc_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = handle_get_rtc_cmd &rArr; uart_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_printf
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_format_current_time_string
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[99]"></a>handle_hide_cmd</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, usart_app.o(.text.handle_hide_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10624<LI>Call Chain = handle_hide_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smart_log_switch
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[96]"></a>handle_limit_cmd</STRONG> (Thumb, 252 bytes, Stack size 80 bytes, usart_app.o(.text.handle_limit_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10696<LI>Call Chain = handle_limit_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smart_log_switch
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_set_limit
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_limit
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[a0]"></a>handle_log_validate_cmd</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, usart_app.o(.text.handle_log_validate_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10624<LI>Call Chain = handle_log_validate_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_validate_all_files
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[95]"></a>handle_ratio_cmd</STRONG> (Thumb, 248 bytes, Stack size 80 bytes, usart_app.o(.text.handle_ratio_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10696<LI>Call Chain = handle_ratio_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_set_ratio
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_ratio
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[9e]"></a>handle_recover_sd_cmd</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, usart_app.o(.text.handle_recover_sd_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10624<LI>Call Chain = handle_recover_sd_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recover_from_sd_card_error
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[9c]"></a>handle_reset_boot_cmd</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, usart_app.o(.text.handle_reset_boot_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 4488<LI>Call Chain = handle_reset_boot_cmd &rArr; sd_reset_boot_count &rArr; flash_write_with_retry &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_get_cached_log_count
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_reset_boot_count
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[9d]"></a>handle_reset_stage_cmd</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, usart_app.o(.text.handle_reset_stage_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10624<LI>Call Chain = handle_reset_stage_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_test_stage_to_initial
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_stage_get_current_stage
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[8d]"></a>handle_rtc_config_cmd</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, usart_app.o(.text.handle_rtc_config_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 576<LI>Call Chain = handle_rtc_config_cmd &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[8e]"></a>handle_rtc_now_cmd</STRONG> (Thumb, 72 bytes, Stack size 112 bytes, usart_app.o(.text.handle_rtc_now_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10728<LI>Call Chain = handle_rtc_now_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_format_current_time_string
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[a9]"></a>handle_sb_check_cmd</STRONG> (Thumb, 904 bytes, Stack size 80 bytes, usart_app.o(.text.handle_sb_check_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10696<LI>Call Chain = handle_sb_check_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dcmpge
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;current_filter_update
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;current_calibrate_linear
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD30AD3344_AD_Read
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[a7]"></a>handle_sb_mode_cmd</STRONG> (Thumb, 136 bytes, Stack size 80 bytes, usart_app.o(.text.handle_sb_mode_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10696<LI>Call Chain = handle_sb_mode_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_board_get_measure_mode
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_board_set_measure_mode
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[a8]"></a>handle_sb_read_cmd</STRONG> (Thumb, 116 bytes, Stack size 48 bytes, usart_app.o(.text.handle_sb_read_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10664<LI>Call Chain = handle_sb_read_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_board_get_data
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_format_current_time_string
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[a5]"></a>handle_sb_start_cmd</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, usart_app.o(.text.handle_sb_start_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10624<LI>Call Chain = handle_sb_start_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_board_start_sampling
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[a6]"></a>handle_sb_stop_cmd</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, usart_app.o(.text.handle_sb_stop_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10624<LI>Call Chain = handle_sb_stop_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_board_stop_sampling
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[8b]"></a>handle_sd_detect_cmd</STRONG> (Thumb, 488 bytes, Stack size 40 bytes, usart_app.o(.text.handle_sd_detect_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = handle_sd_detect_cmd &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardState
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardInfo
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Init
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_SD_IsDetected
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[8c]"></a>handle_sd_format_cmd</STRONG> (Thumb, 520 bytes, Stack size 544 bytes, usart_app.o(.text.handle_sd_format_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 1112<LI>Call Chain = handle_sd_format_cmd &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Init
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkfs
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getfree
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[a4]"></a>handle_sd_sync_cmd</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, usart_app.o(.text.handle_sd_sync_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10624<LI>Call Chain = handle_sd_sync_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_force_sync_filesystem
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[8a]"></a>handle_sd_test_cmd</STRONG> (Thumb, 624 bytes, Stack size 48 bytes, usart_app.o(.text.handle_sd_test_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10664<LI>Call Chain = handle_sd_test_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardState
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardInfo
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_hidedata
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_create_directories
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getfree
<LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_check_card_status
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_app_init
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_sample_data
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_overlimit_data
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[ab]"></a>handle_set_device_id_cmd</STRONG> (Thumb, 108 bytes, Stack size 8 bytes, usart_app.o(.text.handle_set_device_id_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 576<LI>Call Chain = handle_set_device_id_cmd &rArr; uart_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_save_to_flash
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_set
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_printf
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncmp
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[b5]"></a>handle_set_interval_cmd</STRONG> (Thumb, 128 bytes, Stack size 80 bytes, usart_app.o(.text.handle_set_interval_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10696<LI>Call Chain = handle_set_interval_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_set_sample_cycle
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_flash
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_printf
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strchr
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[b1]"></a>handle_set_limit_cmd</STRONG> (Thumb, 344 bytes, Stack size 192 bytes, usart_app.o(.text.handle_set_limit_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10808<LI>Call Chain = handle_set_limit_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_set_all_limits
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_flash
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_sd
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_printf
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strchr
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[af]"></a>handle_set_ratio_cmd</STRONG> (Thumb, 356 bytes, Stack size 192 bytes, usart_app.o(.text.handle_set_ratio_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10808<LI>Call Chain = handle_set_ratio_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_set_all_ratios
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_flash
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_sd
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_printf
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strchr
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[ad]"></a>handle_set_rtc_cmd</STRONG> (Thumb, 116 bytes, Stack size 80 bytes, usart_app.o(.text.handle_set_rtc_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10696<LI>Call Chain = handle_set_rtc_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_set_time_from_string
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_printf
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strchr
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[97]"></a>handle_start_cmd</STRONG> (Thumb, 84 bytes, Stack size 80 bytes, usart_app.o(.text.handle_start_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 11104<LI>Call Chain = handle_start_cmd &rArr; adc_start_sampling &rArr; adc_process_sample &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_start_sampling
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[b3]"></a>handle_start_sample_cmd</STRONG> (Thumb, 124 bytes, Stack size 208 bytes, usart_app.o(.text.handle_start_sample_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 11232<LI>Call Chain = handle_start_sample_cmd &rArr; adc_start_sampling &rArr; adc_process_sample &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_printf
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_start_continuous_sampling_text
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_format_output
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_update_timestamp
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_check_limits
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_apply_ratios
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_read_data
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_start_sampling
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[98]"></a>handle_stop_cmd</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, usart_app.o(.text.handle_stop_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10624<LI>Call Chain = handle_stop_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_stop_sampling
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[b4]"></a>handle_stop_sample_cmd</STRONG> (Thumb, 68 bytes, Stack size 80 bytes, usart_app.o(.text.handle_stop_sample_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10696<LI>Call Chain = handle_stop_sample_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_printf
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_stop_continuous_sampling
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_stop_sampling
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[9b]"></a>handle_system_status_cmd</STRONG> (Thumb, 576 bytes, Stack size 56 bytes, usart_app.o(.text.handle_system_status_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10672<LI>Call Chain = handle_system_status_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_count_records_in_file
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_test_stage_consistency
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_stage_get_current_stage
<LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_get_status
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_get_task_count
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_format_current_time_string
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_limit
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_ratio
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[88]"></a>handle_test_cmd</STRONG> (Thumb, 160 bytes, Stack size 64 bytes, usart_app.o(.text.handle_test_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10680<LI>Call Chain = handle_test_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_print_results
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_check_rtc
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_check_sd
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_check_flash_simple
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_get_boot_count
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[a2]"></a>handle_test_report_cmd</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, usart_app.o(.text.handle_test_report_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10624<LI>Call Chain = handle_test_report_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_generate_report
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[89]"></a>handle_test_simple_cmd</STRONG> (Thumb, 56 bytes, Stack size 56 bytes, usart_app.o(.text.handle_test_simple_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10672<LI>Call Chain = handle_test_simple_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_print_results
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_check_rtc
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_check_sd
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_check_flash_simple
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[a1]"></a>handle_test_simulate_cmd</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, usart_app.o(.text.handle_test_simulate_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10640<LI>Call Chain = handle_test_simulate_cmd &rArr; test_simulate_full_sequence &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_simulate_full_sequence
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[b6]"></a>handle_uart_test_cmd</STRONG> (Thumb, 132 bytes, Stack size 8 bytes, usart_app.o(.text.handle_uart_test_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10624<LI>Call Chain = handle_uart_test_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[9a]"></a>handle_unhide_cmd</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, usart_app.o(.text.handle_unhide_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 10616<LI>Call Chain = handle_unhide_cmd &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usart_app.o(.rodata.cmd_table)
</UL>
<P><STRONG><a name="[220]"></a>is_hex_string</STRONG> (Thumb, 224 bytes, Stack size 16 bytes, usart_app.o(.text.is_hex_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = is_hex_string
</UL>
<BR>[Calls]<UL><LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
</UL>

<P><STRONG><a name="[221]"></a>led_disp</STRONG> (Thumb, 200 bytes, Stack size 40 bytes, led_app.o(.text.led_disp))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = led_disp
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_task
</UL>

<P><STRONG><a name="[0]"></a>led_task</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, led_app.o(.text.led_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = led_task &rArr; led_disp
</UL>
<BR>[Calls]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_disp
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data.scheduler_task)
</UL>
<P><STRONG><a name="[218]"></a>log_count_records_in_file</STRONG> (Thumb, 240 bytes, Stack size 1112 bytes, sd_app.o(.text.log_count_records_in_file))
<BR><BR>[Stack]<UL><LI>Max Depth = 1472<LI>Call Chain = log_count_records_in_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_system_status_cmd
</UL>

<P><STRONG><a name="[1ff]"></a>log_validate_all_files</STRONG> (Thumb, 1100 bytes, Stack size 2688 bytes, sd_app.o(.text.log_validate_all_files))
<BR><BR>[Stack]<UL><LI>Max Depth = 3256<LI>Call Chain = log_validate_all_files &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strchr
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dcmple
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_log_validate_cmd
</UL>

<P><STRONG><a name="[6b]"></a>main</STRONG> (Thumb, 106 bytes, Stack size 0 bytes, main.o(.text.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 10704<LI>Call Chain = main &rArr; scheduler_init &rArr; multi_channel_hardware_init &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_run
<LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_init
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_btn_init
<LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_init
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI1_Init
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RTC_Init
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FATFS_Init
<LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SDIO_SD_Init
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM14_Init
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM6_Init
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[1f1]"></a>multi_channel_apply_ratios</STRONG> (Thumb, 100 bytes, Stack size 24 bytes, adc_app.o(.text.multi_channel_apply_ratios))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = multi_channel_apply_ratios
</UL>
<BR>[Calls]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;voltage_calibrate_segmented
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_all_ratios
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_start_sample_cmd
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_get_data_cmd
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_continuous_read
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_single_read
<LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
</UL>

<P><STRONG><a name="[1fc]"></a>multi_channel_check_limits</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, adc_app.o(.text.multi_channel_check_limits))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = multi_channel_check_limits
</UL>
<BR>[Calls]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_all_limits
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_start_sample_cmd
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_get_data_cmd
</UL>

<P><STRONG><a name="[1fd]"></a>multi_channel_format_output</STRONG> (Thumb, 356 bytes, Stack size 136 bytes, adc_app.o(.text.multi_channel_format_output))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = multi_channel_format_output &rArr; rtc_format_current_time_string &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_format_current_time_string
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_start_sample_cmd
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_get_data_cmd
</UL>

<P><STRONG><a name="[227]"></a>multi_channel_hardware_init</STRONG> (Thumb, 80 bytes, Stack size 80 bytes, adc_app.o(.text.multi_channel_hardware_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 10696<LI>Call Chain = multi_channel_hardware_init &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD30AD3344_Init
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
</UL>
<BR>[Called By]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_init
</UL>

<P><STRONG><a name="[230]"></a>multi_channel_init</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, adc_app.o(.text.multi_channel_init))
<BR><BR>[Called By]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_init
</UL>

<P><STRONG><a name="[1f0]"></a>multi_channel_read_data</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, adc_app.o(.text.multi_channel_read_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 624<LI>Call Chain = multi_channel_read_data &rArr; sampling_board_read_channel &rArr; GD30AD3344_AD_Read &rArr; spi_gd30ad3344_send_halfword_dma &rArr; uart_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_board_read_channel
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_start_sample_cmd
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_get_data_cmd
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_continuous_read
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_single_read
<LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
</UL>

<P><STRONG><a name="[185]"></a>multi_channel_send_binary_data</STRONG> (Thumb, 454 bytes, Stack size 104 bytes, adc_app.o(.text.multi_channel_send_binary_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 672<LI>Call Chain = multi_channel_send_binary_data &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc16_calculate_exam
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_get
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_process_sample
</UL>

<P><STRONG><a name="[1ef]"></a>multi_channel_start_continuous_sampling_binary</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, adc_app.o(.text.multi_channel_start_continuous_sampling_binary))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = multi_channel_start_continuous_sampling_binary
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_continuous_read
</UL>

<P><STRONG><a name="[214]"></a>multi_channel_start_continuous_sampling_text</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, adc_app.o(.text.multi_channel_start_continuous_sampling_text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = multi_channel_start_continuous_sampling_text
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_start_sample_cmd
</UL>

<P><STRONG><a name="[1fa]"></a>multi_channel_stop_continuous_sampling</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, adc_app.o(.text.multi_channel_stop_continuous_sampling))
<BR><BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_stop_sample_cmd
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_stop_read
</UL>

<P><STRONG><a name="[1f2]"></a>multi_channel_update_timestamp</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, adc_app.o(.text.multi_channel_update_timestamp))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = multi_channel_update_timestamp &rArr; rtc_get_unix_timestamp_now &rArr; HAL_RTC_GetDate
</UL>
<BR>[Calls]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_get_unix_timestamp_now
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_start_sample_cmd
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_get_data_cmd
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_continuous_read
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_single_read
</UL>

<P><STRONG><a name="[187]"></a>my_printf</STRONG> (Thumb, 104 bytes, Stack size 544 bytes, usart_app.o(.text.my_printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 568<LI>Call Chain = my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_mark_system_init_logged
<LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_check_system_init_logged
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_uart_test_cmd
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_limits_direct_from_sd
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_ratios_direct_from_sd
<LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_force_sync_filesystem
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sd_sync_cmd
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_print_system_state
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sb_check_cmd
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sb_read_cmd
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_board_set_measure_mode
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sb_mode_cmd
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_board_stop_sampling
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_board_start_sampling
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_generate_report
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_simulate_full_sequence
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_test_simulate_cmd
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_validate_all_files
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_log_validate_cmd
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_repair_inconsistent_state
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_check_state_cmd
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recover_from_sd_card_error
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_recover_sd_cmd
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_test_stage_to_initial
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_reset_stage_cmd
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_reset_boot_count
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_reset_boot_cmd
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_test_stage_consistency
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_system_status_cmd
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_stop_cmd
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_start_cmd
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_config_read_cmd
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_config_save_cmd
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smart_log_switch
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_limit_cmd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_ratio_cmd
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_display_from_sd
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_conf_cmd
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_sd
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_conf_fix_cmd
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_conf_delete_cmd
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_safe_sd_test
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_conf_test_cmd
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_rtc_now_cmd
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_rtc_config_cmd
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sd_format_cmd
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sd_detect_cmd
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sd_test_cmd
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_test_simple_cmd
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_print_results
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_test_cmd
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;binary_protocol_parse
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_stage_init
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_app_init
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_startup_sequence
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_send_binary_data
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_process_sample
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_write_with_retry
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_restore_logs_from_flash
</UL>

<P><STRONG><a name="[6]"></a>oled_task</STRONG> (Thumb, 356 bytes, Stack size 40 bytes, oled_app.o(.text.oled_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 664<LI>Call Chain = oled_task &rArr; multi_channel_read_data &rArr; sampling_board_read_channel &rArr; GD30AD3344_AD_Read &rArr; spi_gd30ad3344_send_halfword_dma &rArr; uart_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Position
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Oled_Printf
<LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_mode_get_name
<LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_mode_get
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_apply_ratios
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_read_data
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data.scheduler_task)
</UL>
<P><STRONG><a name="[22a]"></a>parse_command_type</STRONG> (Thumb, 222 bytes, Stack size 40 bytes, usart_app.o(.text.parse_command_type))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = parse_command_type &rArr; strncmp
</UL>
<BR>[Calls]<UL><LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncmp
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
</UL>

<P><STRONG><a name="[22b]"></a>parse_uart_command</STRONG> (Thumb, 1048 bytes, Stack size 184 bytes, usart_app.o(.text.parse_uart_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 11200<LI>Call Chain = parse_uart_command &rArr; handle_binary_protocol_cmd &rArr; handle_binary_continuous_read &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_command_type
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_protocol_cmd
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_hex_string
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_set_ratio
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_set_limit
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_set_time_from_string
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_printf
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_format_current_time_string
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_limit
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_ratio
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncmp
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_task
</UL>

<P><STRONG><a name="[7f]"></a>prv_btn_event</STRONG> (Thumb, 216 bytes, Stack size 8 bytes, btn_app.o(.text.prv_btn_event))
<BR><BR>[Stack]<UL><LI>Max Depth = 10624<LI>Call Chain = prv_btn_event &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
</UL>
<BR>[Address Reference Count : 1]<UL><LI> btn_app.o(.text.app_btn_init)
</UL>
<P><STRONG><a name="[80]"></a>prv_btn_get_state</STRONG> (Thumb, 170 bytes, Stack size 8 bytes, btn_app.o(.text.prv_btn_get_state))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prv_btn_get_state
</UL>
<BR>[Calls]<UL><LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
</UL>
<BR>[Address Reference Count : 1]<UL><LI> btn_app.o(.text.app_btn_init)
</UL>
<P><STRONG><a name="[200]"></a>recover_from_sd_card_error</STRONG> (Thumb, 528 bytes, Stack size 3880 bytes, sd_app.o(.text.recover_from_sd_card_error))
<BR><BR>[Stack]<UL><LI>Max Depth = 10304<LI>Call Chain = recover_from_sd_card_error &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_check_and_reinit_if_needed
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auto_repair_inconsistent_state
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_test_stage_consistency
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_write
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_restore_logs_from_flash
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_recover_sd_cmd
</UL>

<P><STRONG><a name="[203]"></a>reset_test_stage_to_initial</STRONG> (Thumb, 416 bytes, Stack size 3880 bytes, sd_app.o(.text.reset_test_stage_to_initial))
<BR><BR>[Stack]<UL><LI>Max Depth = 4472<LI>Call Chain = reset_test_stage_to_initial &rArr; flash_write_with_retry &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_write
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_write_with_retry
</UL>
<BR>[Called By]<UL><LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_simulate_full_sequence
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_reset_stage_cmd
</UL>

<P><STRONG><a name="[241]"></a>rt_ringbuffer_data_len</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, ringbuffer.o(.text.rt_ringbuffer_data_len))
<BR><BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_task
</UL>

<P><STRONG><a name="[22f]"></a>rt_ringbuffer_get</STRONG> (Thumb, 180 bytes, Stack size 24 bytes, ringbuffer.o(.text.rt_ringbuffer_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = rt_ringbuffer_get &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_task
</UL>

<P><STRONG><a name="[224]"></a>rt_ringbuffer_init</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, ringbuffer.o(.text.rt_ringbuffer_init))
<BR><BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[148]"></a>rt_ringbuffer_put</STRONG> (Thumb, 182 bytes, Stack size 24 bytes, ringbuffer.o(.text.rt_ringbuffer_put))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = rt_ringbuffer_put &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>

<P><STRONG><a name="[23c]"></a>rtc_convert_to_unix_timestamp</STRONG> (Thumb, 608 bytes, Stack size 68 bytes, rtc_app.o(.text.rtc_convert_to_unix_timestamp))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = rtc_convert_to_unix_timestamp
</UL>
<BR>[Called By]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_hidedata_with_voltage
</UL>

<P><STRONG><a name="[17f]"></a>rtc_format_current_time_string</STRONG> (Thumb, 122 bytes, Stack size 64 bytes, rtc_app.o(.text.rtc_format_current_time_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = rtc_format_current_time_string &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_get_rtc_cmd
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sb_read_cmd
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_board_start_sampling
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_system_status_cmd
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_rtc_now_cmd
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_check_rtc
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_format_output
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_process_sample
</UL>

<P><STRONG><a name="[184]"></a>rtc_get_unix_timestamp_now</STRONG> (Thumb, 634 bytes, Stack size 96 bytes, rtc_app.o(.text.rtc_get_unix_timestamp_now))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = rtc_get_unix_timestamp_now &rArr; HAL_RTC_GetDate
</UL>
<BR>[Calls]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
</UL>
<BR>[Called By]<UL><LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_update_timestamp
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_process_sample
</UL>

<P><STRONG><a name="[213]"></a>rtc_set_time_from_string</STRONG> (Thumb, 348 bytes, Stack size 104 bytes, rtc_app.o(.text.rtc_set_time_from_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 368<LI>Call Chain = rtc_set_time_from_string &rArr; __0sscanf &rArr; __vfscanf_char &rArr; __vfscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetDate
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetTime
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_rtc_cmd
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
</UL>

<P><STRONG><a name="[209]"></a>sampling_board_get_data</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, sampling_board_app.o(.text.sampling_board_get_data))
<BR><BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sb_read_cmd
</UL>

<P><STRONG><a name="[208]"></a>sampling_board_get_measure_mode</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, sampling_board_app.o(.text.sampling_board_get_measure_mode))
<BR><BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sb_mode_cmd
</UL>

<P><STRONG><a name="[180]"></a>sampling_board_read_channel</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, sampling_board_app.o(.text.sampling_board_read_channel))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = sampling_board_read_channel &rArr; GD30AD3344_AD_Read &rArr; spi_gd30ad3344_send_halfword_dma &rArr; uart_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD30AD3344_AD_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_read_data
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_process_sample
</UL>

<P><STRONG><a name="[207]"></a>sampling_board_set_measure_mode</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, sampling_board_app.o(.text.sampling_board_set_measure_mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 568<LI>Call Chain = sampling_board_set_measure_mode &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sb_mode_cmd
</UL>

<P><STRONG><a name="[20a]"></a>sampling_board_start_sampling</STRONG> (Thumb, 152 bytes, Stack size 56 bytes, sampling_board_app.o(.text.sampling_board_start_sampling))
<BR><BR>[Stack]<UL><LI>Max Depth = 624<LI>Call Chain = sampling_board_start_sampling &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_format_current_time_string
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sb_start_cmd
</UL>

<P><STRONG><a name="[20b]"></a>sampling_board_stop_sampling</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, sampling_board_app.o(.text.sampling_board_stop_sampling))
<BR><BR>[Stack]<UL><LI>Max Depth = 568<LI>Call Chain = sampling_board_stop_sampling &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sb_stop_cmd
</UL>

<P><STRONG><a name="[5]"></a>sampling_board_task</STRONG> (Thumb, 136 bytes, Stack size 24 bytes, sampling_board_app.o(.text.sampling_board_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 640<LI>Call Chain = sampling_board_task &rArr; GD30AD3344_AD_Read &rArr; spi_gd30ad3344_send_halfword_dma &rArr; uart_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;current_calibrate_linear
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD30AD3344_AD_Read
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data.scheduler_task)
</UL>
<P><STRONG><a name="[217]"></a>scheduler_get_status</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, scheduler.o(.text.scheduler_get_status))
<BR><BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_system_status_cmd
</UL>

<P><STRONG><a name="[216]"></a>scheduler_get_task_count</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, scheduler.o(.text.scheduler_get_task_count))
<BR><BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_system_status_cmd
</UL>

<P><STRONG><a name="[225]"></a>scheduler_init</STRONG> (Thumb, 82 bytes, Stack size 8 bytes, scheduler.o(.text.scheduler_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 10704<LI>Call Chain = scheduler_init &rArr; multi_channel_hardware_init &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_sync_sd_to_memory_and_flash
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_check_sd_flash_sync_needed
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_ensure_ini_file
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_stage_init
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_app_init
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_startup_sequence
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_load_from_flash
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device_id_init
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_app_init
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_app_init
<LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_hardware_init
<LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_init
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_control_init
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[226]"></a>scheduler_run</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, scheduler.o(.text.scheduler_run))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = scheduler_run
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[20e]"></a>sd_app_init</STRONG> (Thumb, 1256 bytes, Stack size 3888 bytes, sd_app.o(.text.sd_app_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 5720<LI>Call Chain = sd_app_init &rArr; sd_restore_file_managers &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardState
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Init
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkfs
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_read
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_write
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_restore_file_managers
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>
<BR>[Called By]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_init
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sd_test_cmd
</UL>

<P><STRONG><a name="[235]"></a>sd_cache_log_to_flash</STRONG> (Thumb, 424 bytes, Stack size 3880 bytes, sd_app.o(.text.sd_cache_log_to_flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 4040<LI>Call Chain = sd_cache_log_to_flash &rArr; flash_direct_write &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_read
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_write
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
</UL>

<P><STRONG><a name="[236]"></a>sd_check_and_create_new_file</STRONG> (Thumb, 192 bytes, Stack size 80 bytes, sd_app.o(.text.sd_check_and_create_new_file))
<BR><BR>[Stack]<UL><LI>Max Depth = 1832<LI>Call Chain = sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_latest_incomplete_file
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_generate_filename
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_data
</UL>

<P><STRONG><a name="[22d]"></a>sd_check_and_reinit_if_needed</STRONG> (Thumb, 696 bytes, Stack size 24 bytes, sd_app.o(.text.sd_check_and_reinit_if_needed))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = sd_check_and_reinit_if_needed &rArr; f_getfree &rArr; find_volume &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_GetCardState
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_Init
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getfree
</UL>
<BR>[Called By]<UL><LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recover_from_sd_card_error
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_check_sd
</UL>

<P><STRONG><a name="[20d]"></a>sd_check_card_status</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, sd_app.o(.text.sd_check_card_status))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = sd_check_card_status &rArr; f_getfree &rArr; find_volume &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getfree
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sd_test_cmd
</UL>

<P><STRONG><a name="[238]"></a>sd_check_system_init_logged</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, sd_app.o(.text.sd_check_system_init_logged))
<BR><BR>[Stack]<UL><LI>Max Depth = 592<LI>Call Chain = sd_check_system_init_logged &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_read
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_startup_sequence
</UL>

<P><STRONG><a name="[239]"></a>sd_comprehensive_check</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, sd_app.o(.text.sd_comprehensive_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = sd_comprehensive_check &rArr; f_getfree &rArr; find_volume &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getfree
</UL>
<BR>[Called By]<UL><LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_check_sd
</UL>

<P><STRONG><a name="[20f]"></a>sd_create_directories</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, sd_app.o(.text.sd_create_directories))
<BR><BR>[Stack]<UL><LI>Max Depth = 368<LI>Call Chain = sd_create_directories &rArr; f_mkdir &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sd_test_cmd
</UL>

<P><STRONG><a name="[20c]"></a>sd_force_sync_filesystem</STRONG> (Thumb, 136 bytes, Stack size 8 bytes, sd_app.o(.text.sd_force_sync_filesystem))
<BR><BR>[Stack]<UL><LI>Max Depth = 576<LI>Call Chain = sd_force_sync_filesystem &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sd_sync_cmd
</UL>

<P><STRONG><a name="[237]"></a>sd_generate_filename</STRONG> (Thumb, 180 bytes, Stack size 80 bytes, sd_app.o(.text.sd_generate_filename))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = sd_generate_filename &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_check_and_create_new_file
</UL>

<P><STRONG><a name="[219]"></a>sd_get_boot_count</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, sd_app.o(.text.sd_get_boot_count))
<BR><BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_test_cmd
</UL>

<P><STRONG><a name="[202]"></a>sd_get_cached_log_count</STRONG> (Thumb, 296 bytes, Stack size 3880 bytes, sd_app.o(.text.sd_get_cached_log_count))
<BR><BR>[Stack]<UL><LI>Max Depth = 4016<LI>Call Chain = sd_get_cached_log_count &rArr; flash_direct_read &rArr; spi_flash_buffer_read &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_read
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_reset_boot_cmd
</UL>

<P><STRONG><a name="[23a]"></a>sd_mark_system_init_logged</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, sd_app.o(.text.sd_mark_system_init_logged))
<BR><BR>[Stack]<UL><LI>Max Depth = 584<LI>Call Chain = sd_mark_system_init_logged &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_write
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_startup_sequence
</UL>

<P><STRONG><a name="[201]"></a>sd_reset_boot_count</STRONG> (Thumb, 880 bytes, Stack size 3888 bytes, sd_app.o(.text.sd_reset_boot_count))
<BR><BR>[Stack]<UL><LI>Max Depth = 4480<LI>Call Chain = sd_reset_boot_count &rArr; flash_write_with_retry &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_read
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_erase_sector
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_write
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_write_with_retry
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_reset_boot_cmd
</UL>

<P><STRONG><a name="[234]"></a>sd_restore_file_managers</STRONG> (Thumb, 138 bytes, Stack size 80 bytes, sd_app.o(.text.sd_restore_file_managers))
<BR><BR>[Stack]<UL><LI>Max Depth = 1832<LI>Call Chain = sd_restore_file_managers &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_latest_incomplete_file
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
</UL>
<BR>[Called By]<UL><LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_app_init
</UL>

<P><STRONG><a name="[22e]"></a>sd_restore_logs_from_flash</STRONG> (Thumb, 448 bytes, Stack size 3880 bytes, sd_app.o(.text.sd_restore_logs_from_flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 6424<LI>Call Chain = sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_read
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_data
</UL>
<BR>[Called By]<UL><LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recover_from_sd_card_error
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
</UL>

<P><STRONG><a name="[23b]"></a>sd_write_data</STRONG> (Thumb, 188 bytes, Stack size 712 bytes, sd_app.o(.text.sd_write_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 2544<LI>Call Chain = sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_check_and_create_new_file
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_sample_data
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_hidedata_with_voltage
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_overlimit_data
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_restore_logs_from_flash
</UL>

<P><STRONG><a name="[210]"></a>sd_write_hidedata</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, sd_app.o(.text.sd_write_hidedata))
<BR><BR>[Stack]<UL><LI>Max Depth = 3064<LI>Call Chain = sd_write_hidedata &rArr; sd_write_hidedata_with_voltage &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_hidedata_with_voltage
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sd_test_cmd
</UL>

<P><STRONG><a name="[18b]"></a>sd_write_hidedata_with_voltage</STRONG> (Thumb, 236 bytes, Stack size 520 bytes, sd_app.o(.text.sd_write_hidedata_with_voltage))
<BR><BR>[Stack]<UL><LI>Max Depth = 3064<LI>Call Chain = sd_write_hidedata_with_voltage &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_convert_to_unix_timestamp
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_format_hex_string
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_data
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_hidedata
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_process_sample
</UL>

<P><STRONG><a name="[188]"></a>sd_write_log_data</STRONG> (Thumb, 1152 bytes, Stack size 4192 bytes, sd_app.o(.text.sd_write_log_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 10616<LI>Call Chain = sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_read
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cache_log_to_flash
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_restore_logs_from_flash
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_data
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_uart_test_cmd
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_interval_cmd
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_stop_sample_cmd
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_start_sample_cmd
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_limit_cmd
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_ratio_cmd
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_rtc_cmd
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sd_sync_cmd
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_debug_state_cmd
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sb_check_cmd
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sb_read_cmd
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sb_mode_cmd
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sb_stop_cmd
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sb_start_cmd
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_test_report_cmd
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_simulate_full_sequence
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_test_simulate_cmd
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_log_validate_cmd
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_check_state_cmd
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_recover_sd_cmd
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_reset_stage_cmd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_system_status_cmd
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_unhide_cmd
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_hide_cmd
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_stop_cmd
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_start_cmd
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_config_save_cmd
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_limit_cmd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_ratio_cmd
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_rtc_now_cmd
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sd_test_cmd
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_test_simple_cmd
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_test_cmd
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_stop_read
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_continuous_read
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_single_read
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_get_device_id
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_set_device_id
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_startup_sequence
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_btn_event
<LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_hardware_init
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_process_sample
</UL>

<P><STRONG><a name="[18a]"></a>sd_write_overlimit_data</STRONG> (Thumb, 168 bytes, Stack size 232 bytes, sd_app.o(.text.sd_write_overlimit_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 2776<LI>Call Chain = sd_write_overlimit_data &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_data
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sd_test_cmd
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_process_sample
</UL>

<P><STRONG><a name="[18c]"></a>sd_write_sample_data</STRONG> (Thumb, 152 bytes, Stack size 216 bytes, sd_app.o(.text.sd_write_sample_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 2760<LI>Call Chain = sd_write_sample_data &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetDate
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_GetTime
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_data
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sd_test_cmd
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_process_sample
</UL>

<P><STRONG><a name="[21a]"></a>selftest_check_flash_simple</STRONG> (Thumb, 104 bytes, Stack size 8 bytes, selftest_app.o(.text.selftest_check_flash_simple))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = selftest_check_flash_simple &rArr; spi_flash_read_id_alt &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_id_alt
<LI><a href="#[23d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_id
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_test_simple_cmd
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_test_cmd
</UL>

<P><STRONG><a name="[21c]"></a>selftest_check_rtc</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, selftest_app.o(.text.selftest_check_rtc))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = selftest_check_rtc &rArr; rtc_format_current_time_string &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_format_current_time_string
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_test_simple_cmd
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_test_cmd
</UL>

<P><STRONG><a name="[21b]"></a>selftest_check_sd</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, selftest_app.o(.text.selftest_check_sd))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = selftest_check_sd &rArr; sd_check_and_reinit_if_needed &rArr; f_getfree &rArr; find_volume &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_check_and_reinit_if_needed
<LI><a href="#[239]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_comprehensive_check
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getfree
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_test_simple_cmd
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_test_cmd
</UL>

<P><STRONG><a name="[21d]"></a>selftest_print_results</STRONG> (Thumb, 188 bytes, Stack size 16 bytes, selftest_app.o(.text.selftest_print_results))
<BR><BR>[Stack]<UL><LI>Max Depth = 584<LI>Call Chain = selftest_print_results &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_test_simple_cmd
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_test_cmd
</UL>

<P><STRONG><a name="[1fe]"></a>smart_log_switch</STRONG> (Thumb, 244 bytes, Stack size 24 bytes, sd_app.o(.text.smart_log_switch))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = smart_log_switch &rArr; flash_write_with_retry &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_write_with_retry
</UL>
<BR>[Called By]<UL><LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_simulate_full_sequence
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_hide_cmd
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_limit_cmd
</UL>

<P><STRONG><a name="[1e9]"></a>spi_flash_buffer_read</STRONG> (Thumb, 228 bytes, Stack size 56 bytes, gd25qxx.o(.text.spi_flash_buffer_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = spi_flash_buffer_read &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
</UL>
<BR>[Called By]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_read
</UL>

<P><STRONG><a name="[1ea]"></a>spi_flash_buffer_write</STRONG> (Thumb, 190 bytes, Stack size 24 bytes, gd25qxx.o(.text.spi_flash_buffer_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[23f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
</UL>
<BR>[Called By]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_write
</UL>

<P><STRONG><a name="[1e6]"></a>spi_flash_init</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd25qxx.o(.text.spi_flash_init))
<BR><BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_app_init
</UL>

<P><STRONG><a name="[23f]"></a>spi_flash_page_write</STRONG> (Thumb, 382 bytes, Stack size 48 bytes, gd25qxx.o(.text.spi_flash_page_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = spi_flash_page_write &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
</UL>
<BR>[Called By]<UL><LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_write
</UL>

<P><STRONG><a name="[23d]"></a>spi_flash_read_id</STRONG> (Thumb, 200 bytes, Stack size 48 bytes, gd25qxx.o(.text.spi_flash_read_id))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = spi_flash_read_id &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
</UL>
<BR>[Called By]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_check_flash_simple
</UL>

<P><STRONG><a name="[23e]"></a>spi_flash_read_id_alt</STRONG> (Thumb, 218 bytes, Stack size 48 bytes, gd25qxx.o(.text.spi_flash_read_id_alt))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = spi_flash_read_id_alt &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
</UL>
<BR>[Called By]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_check_flash_simple
</UL>

<P><STRONG><a name="[1e8]"></a>spi_flash_sector_erase</STRONG> (Thumb, 318 bytes, Stack size 56 bytes, gd25qxx.o(.text.spi_flash_sector_erase))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = spi_flash_sector_erase &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
</UL>
<BR>[Called By]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_erase_sector
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_write
</UL>

<P><STRONG><a name="[f1]"></a>spi_gd30ad3344_send_halfword_dma</STRONG> (Thumb, 160 bytes, Stack size 24 bytes, gd30ad3344.o(.text.spi_gd30ad3344_send_halfword_dma))
<BR><BR>[Stack]<UL><LI>Max Depth = 592<LI>Call Chain = spi_gd30ad3344_send_halfword_dma &rArr; uart_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_GetState
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive_DMA
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD30AD3344_AD_Read
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD30AD3344_Init
</UL>

<P><STRONG><a name="[232]"></a>system_startup_sequence</STRONG> (Thumb, 132 bytes, Stack size 16 bytes, selftest_app.o(.text.system_startup_sequence))
<BR><BR>[Stack]<UL><LI>Max Depth = 10632<LI>Call Chain = system_startup_sequence &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_mark_system_init_logged
<LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_check_system_init_logged
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_init
</UL>

<P><STRONG><a name="[21e]"></a>test_generate_report</STRONG> (Thumb, 1888 bytes, Stack size 1120 bytes, sd_app.o(.text.test_generate_report))
<BR><BR>[Stack]<UL><LI>Max Depth = 2328<LI>Call Chain = test_generate_report &rArr; check_test_stage_consistency &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_test_stage_consistency
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_test_report_cmd
</UL>

<P><STRONG><a name="[21f]"></a>test_simulate_full_sequence</STRONG> (Thumb, 312 bytes, Stack size 16 bytes, sd_app.o(.text.test_simulate_full_sequence))
<BR><BR>[Stack]<UL><LI>Max Depth = 10632<LI>Call Chain = test_simulate_full_sequence &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_test_stage_to_initial
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smart_log_switch
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_test_simulate_cmd
</UL>

<P><STRONG><a name="[1fb]"></a>test_stage_get_current_stage</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, sd_app.o(.text.test_stage_get_current_stage))
<BR><BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_check_state_cmd
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_reset_stage_cmd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_system_status_cmd
</UL>

<P><STRONG><a name="[233]"></a>test_stage_init</STRONG> (Thumb, 220 bytes, Stack size 32 bytes, sd_app.o(.text.test_stage_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 624<LI>Call Chain = test_stage_init &rArr; flash_write_with_retry &rArr; my_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_direct_read
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_write_with_retry
</UL>
<BR>[Called By]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_init
</UL>

<P><STRONG><a name="[f3]"></a>uart_printf</STRONG> (Thumb, 104 bytes, Stack size 544 bytes, usart_app.o(.text.uart_printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 568<LI>Call Chain = uart_printf &rArr; HAL_UART_Transmit
</UL>
<BR>[Calls]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_interval_cmd
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_stop_sample_cmd
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_start_sample_cmd
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_get_data_cmd
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_limit_cmd
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_get_limit_cmd
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_ratio_cmd
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_get_ratio_cmd
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_rtc_cmd
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_get_rtc_cmd
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_device_id_cmd
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_get_device_id_cmd
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD30AD3344_AD_Read
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_stop_read
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_continuous_read
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_single_read
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_get_device_id
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_set_device_id
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_protocol_cmd
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD30AD3344_Init
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_gd30ad3344_send_halfword_dma
</UL>

<P><STRONG><a name="[2]"></a>uart_task</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, usart_app.o(.text.uart_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 11216<LI>Call Chain = uart_task &rArr; parse_uart_command &rArr; handle_binary_protocol_cmd &rArr; handle_binary_continuous_read &rArr; sd_write_log_data &rArr; sd_restore_logs_from_flash &rArr; sd_write_data &rArr; sd_check_and_create_new_file &rArr; find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_get
<LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_data_len
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data.scheduler_task)
</UL>
<P><STRONG><a name="[182]"></a>voltage_calibrate_segmented</STRONG> (Thumb, 1104 bytes, Stack size 0 bytes, sampling_board_app.o(.text.voltage_calibrate_segmented))
<BR><BR>[Called By]<UL><LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_apply_ratios
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_process_sample
</UL>

<P><STRONG><a name="[242]"></a>__0snprintf</STRONG> (Thumb, 48 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[243]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[264]"></a>__1snprintf</STRONG> (Thumb, 0 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[17d]"></a>__2snprintf</STRONG> (Thumb, 0 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_latest_incomplete_file
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_interval_cmd
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_limit_cmd
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_ratio_cmd
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_rtc_cmd
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sb_mode_cmd
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_check_state_cmd
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_test_stage_consistency
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_start_cmd
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_limit_cmd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_ratio_cmd
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_save_to_sd
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_safe_sd_test
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_rtc_now_cmd
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_continuous_read
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_single_read
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_set_device_id
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_format_hex_string
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_format_output
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_format_current_time_string
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_process_sample
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_data
<LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_check_and_create_new_file
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_generate_filename
</UL>

<P><STRONG><a name="[265]"></a>__c89snprintf</STRONG> (Thumb, 0 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[266]"></a>snprintf</STRONG> (Thumb, 0 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[244]"></a>__0sprintf</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[243]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[267]"></a>__1sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[1be]"></a>__2sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_stop_read
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_continuous_read
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_get_device_id
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_binary_set_device_id
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc16_calculate_exam
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_sample_data
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_hidedata_with_voltage
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_overlimit_data
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_write_log_data
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;multi_channel_send_binary_data
</UL>

<P><STRONG><a name="[268]"></a>__c89sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[269]"></a>sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[245]"></a>__0vsnprintf</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[243]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[26a]"></a>__1vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[26b]"></a>__2vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[26c]"></a>__c89vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[16f]"></a>vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_printf
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Oled_Printf
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>

<P><STRONG><a name="[c6]"></a>__aeabi_errno_addr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, errno.o(i.__aeabi_errno_addr))
<BR><BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_strtoul
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
</UL>

<P><STRONG><a name="[26d]"></a>__rt_errno_addr</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, errno.o(i.__aeabi_errno_addr), UNUSED)

<P><STRONG><a name="[1ab]"></a>__hardfp_atof</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, atof.o(i.__hardfp_atof))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = __hardfp_atof &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__strtod_int
<LI><a href="#[247]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[246]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__read_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_safe_read_from_sd
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_limit_cmd
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_limits_direct_from_sd
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_set_ratio_cmd
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_get_ratios_direct_from_sd
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_sb_check_cmd
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_limit_cmd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_ratio_cmd
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_uart_command
</UL>

<P><STRONG><a name="[246]"></a>__read_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__read_errno))
<BR><BR>[Called By]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
</UL>

<P><STRONG><a name="[26e]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[26f]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[270]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[247]"></a>__set_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__set_errno))
<BR><BR>[Called By]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
</UL>

<P><STRONG><a name="[dc]"></a>_is_digit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, scanf_fp.o(i._is_digit), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[1e2]"></a>free</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, malloc.o(i.free))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = free
</UL>
<BR>[Called By]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memfree
</UL>

<P><STRONG><a name="[1e1]"></a>malloc</STRONG> (Thumb, 92 bytes, Stack size 20 bytes, malloc.o(i.malloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_memalloc
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[72]"></a>ADC_DMAConvCplt</STRONG> (Thumb, 110 bytes, Stack size 0 bytes, stm32f4xx_hal_adc.o(.text.ADC_DMAConvCplt))
<BR><BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ErrorCallback
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA)
</UL>
<P><STRONG><a name="[73]"></a>ADC_DMAHalfConvCplt</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal_adc.o(.text.ADC_DMAHalfConvCplt))
<BR><BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvHalfCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA)
</UL>
<P><STRONG><a name="[74]"></a>ADC_DMAError</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_hal_adc.o(.text.ADC_DMAError))
<BR><BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_adc.o(.text.HAL_ADC_Start_DMA)
</UL>
<P><STRONG><a name="[153]"></a>I2C_WaitOnFlagUntilTimeout</STRONG> (Thumb, 458 bytes, Stack size 24 bytes, stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
</UL>

<P><STRONG><a name="[154]"></a>I2C_WaitOnMasterAddressFlagUntilTimeout</STRONG> (Thumb, 214 bytes, Stack size 24 bytes, stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
</UL>

<P><STRONG><a name="[106]"></a>I2C_WaitOnBTFFlagUntilTimeout</STRONG> (Thumb, 186 bytes, Stack size 16 bytes, stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_WaitOnBTFFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>

<P><STRONG><a name="[107]"></a>I2C_WaitOnTXEFlagUntilTimeout</STRONG> (Thumb, 186 bytes, Stack size 16 bytes, stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_WaitOnTXEFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
</UL>

<P><STRONG><a name="[105]"></a>I2C_RequestMemoryWrite</STRONG> (Thumb, 200 bytes, Stack size 40 bytes, stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnMasterAddressFlagUntilTimeout
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>

<P><STRONG><a name="[118]"></a>SD_FindSCR</STRONG> (Thumb, 248 bytes, Stack size 56 bytes, stm32f4xx_hal_sd.o(.text.SD_FindSCR))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = SD_FindSCR &rArr; SDIO_ConfigData
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdSendSCR
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdAppCommand
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDMMC_CmdBlockLength
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ConfigData
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_ReadFIFO
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SD_ConfigWideBusOperation
</UL>

<P><STRONG><a name="[137]"></a>SPI_WaitFlagStateUntilTimeout</STRONG> (Thumb, 288 bytes, Stack size 32 bytes, stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMATransmitReceiveCplt
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAReceiveCplt
</UL>

<P><STRONG><a name="[7a]"></a>SPI_DMAError</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(.text.SPI_DMAError))
<BR><BR>[Calls]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA)
</UL>
<P><STRONG><a name="[76]"></a>SPI_DMAHalfReceiveCplt</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(.text.SPI_DMAHalfReceiveCplt))
<BR><BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_RxHalfCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA)
</UL>
<P><STRONG><a name="[79]"></a>SPI_DMAReceiveCplt</STRONG> (Thumb, 172 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(.text.SPI_DMAReceiveCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = SPI_DMAReceiveCplt &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_RxCpltCallback
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_ErrorCallback
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA)
</UL>
<P><STRONG><a name="[77]"></a>SPI_DMAHalfTransmitReceiveCplt</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(.text.SPI_DMAHalfTransmitReceiveCplt))
<BR><BR>[Calls]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TxRxHalfCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA)
</UL>
<P><STRONG><a name="[78]"></a>SPI_DMATransmitReceiveCplt</STRONG> (Thumb, 258 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(.text.SPI_DMATransmitReceiveCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = SPI_DMATransmitReceiveCplt &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TxRxCpltCallback
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_ErrorCallback
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA)
</UL>
<P><STRONG><a name="[75]"></a>SPI_DMAAbortOnError</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(.text.SPI_DMAAbortOnError))
<BR><BR>[Calls]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(.text.HAL_SPI_IRQHandler)
</UL>
<P><STRONG><a name="[14f]"></a>UART_SetConfig</STRONG> (Thumb, 238 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(.text.UART_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = UART_SetConfig &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[7d]"></a>UART_DMAError</STRONG> (Thumb, 380 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text.UART_DMAError))
<BR><BR>[Calls]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA)
</UL>
<P><STRONG><a name="[7b]"></a>UART_DMAReceiveCplt</STRONG> (Thumb, 350 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = UART_DMAReceiveCplt &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; HAL_DMA_Start_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA)
</UL>
<P><STRONG><a name="[7c]"></a>UART_DMARxHalfCplt</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = UART_DMARxHalfCplt &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; HAL_DMA_Start_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxHalfCpltCallback
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA)
</UL>
<P><STRONG><a name="[14a]"></a>UART_Receive_IT</STRONG> (Thumb, 254 bytes, Stack size 4 bytes, stm32f4xx_hal_uart.o(.text.UART_Receive_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; HAL_DMA_Start_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[7e]"></a>UART_DMAAbortOnError</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError))
<BR><BR>[Calls]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
</UL>
<P><STRONG><a name="[1cf]"></a>prv_process_btn</STRONG> (Thumb, 410 bytes, Stack size 16 bytes, ebtn.o(.text.prv_process_btn))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = prv_process_btn
</UL>
<BR>[Called By]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
</UL>

<P><STRONG><a name="[1e3]"></a>find_latest_incomplete_file</STRONG> (Thumb, 396 bytes, Stack size 1392 bytes, sd_app.o(.text.find_latest_incomplete_file))
<BR><BR>[Stack]<UL><LI>Max Depth = 1752<LI>Call Chain = find_latest_incomplete_file &rArr; f_open &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_gets
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strrchr
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_closedir
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_check_and_create_new_file
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_restore_file_managers
</UL>

<P><STRONG><a name="[1d1]"></a>find_volume</STRONG> (Thumb, 904 bytes, Stack size 56 bytes, ff.o(.text.find_volume))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = find_volume &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>
<BR>[Called By]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_stat
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getfree
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[1c8]"></a>move_window</STRONG> (Thumb, 138 bytes, Stack size 32 bytes, ff.o(.text.move_window))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getfree
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_read
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>

<P><STRONG><a name="[1d3]"></a>follow_path</STRONG> (Thumb, 882 bytes, Stack size 80 bytes, ff.o(.text.follow_path))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_wtoupper
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_convert
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
</UL>
<BR>[Called By]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_stat
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[1cd]"></a>dir_register</STRONG> (Thumb, 744 bytes, Stack size 64 bytes, ff.o(.text.dir_register))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[1c0]"></a>get_fat</STRONG> (Thumb, 206 bytes, Stack size 24 bytes, ff.o(.text.get_fat))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = get_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_read
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getfree
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[1c1]"></a>put_fat</STRONG> (Thumb, 270 bytes, Stack size 32 bytes, ff.o(.text.put_fat))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[1c7]"></a>dir_sdi</STRONG> (Thumb, 160 bytes, Stack size 24 bytes, ff.o(.text.dir_sdi))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = dir_sdi &rArr; get_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
</UL>

<P><STRONG><a name="[1c6]"></a>dir_find</STRONG> (Thumb, 430 bytes, Stack size 48 bytes, ff.o(.text.dir_find))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_wtoupper
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
</UL>

<P><STRONG><a name="[1c9]"></a>dir_next</STRONG> (Thumb, 356 bytes, Stack size 32 bytes, ff.o(.text.dir_next))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_window
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_read
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
</UL>

<P><STRONG><a name="[1e0]"></a>remove_chain</STRONG> (Thumb, 142 bytes, Stack size 24 bytes, ff.o(.text.remove_chain))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = remove_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
</UL>

<P><STRONG><a name="[1dd]"></a>inc_lock</STRONG> (Thumb, 162 bytes, Stack size 20 bytes, ff.o(.text.inc_lock))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = inc_lock
</UL>
<BR>[Called By]<UL><LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
</UL>

<P><STRONG><a name="[1bf]"></a>create_chain</STRONG> (Thumb, 206 bytes, Stack size 24 bytes, ff.o(.text.create_chain))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
</UL>
<BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[1d6]"></a>sync_fs</STRONG> (Thumb, 210 bytes, Stack size 32 bytes, ff.o(.text.sync_fs))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = sync_fs &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_ioctl
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>
<BR>[Called By]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[1cc]"></a>dir_read</STRONG> (Thumb, 302 bytes, Stack size 40 bytes, ff.o(.text.dir_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = dir_read &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_unlink
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
</UL>

<P><STRONG><a name="[1df]"></a>get_fileinfo</STRONG> (Thumb, 300 bytes, Stack size 32 bytes, ff.o(.text.get_fileinfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = get_fileinfo &rArr; ff_convert
</UL>
<BR>[Calls]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_convert
</UL>
<BR>[Called By]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_stat
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_readdir
</UL>

<P><STRONG><a name="[1cb]"></a>sync_window</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, ff.o(.text.sync_window))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = sync_window &rArr; disk_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>
<BR>[Called By]<UL><LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>

<P><STRONG><a name="[248]"></a>_fp_digits</STRONG> (Thumb, 366 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[243]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[243]"></a>_printf_core</STRONG> (Thumb, 1744 bytes, Stack size 136 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[24b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[245]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0vsnprintf
<LI><a href="#[244]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
<LI><a href="#[242]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0snprintf
</UL>

<P><STRONG><a name="[24b]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[243]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[24a]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[243]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[81]"></a>_snputc</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, printfa.o(i._snputc))
<BR>[Address Reference Count : 2]<UL><LI> printfa.o(i.__0snprintf)
<LI> printfa.o(i.__0vsnprintf)
</UL>
<P><STRONG><a name="[82]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printfa.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[244]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0sprintf)
</UL>
<P><STRONG><a name="[70]"></a>_scanf_char_input</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, scanf_char.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> scanf_char.o(.text)
</UL>
<P><STRONG><a name="[ce]"></a>_local_sscanf</STRONG> (Thumb, 62 bytes, Stack size 64 bytes, strtod.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_real
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__strtod_int
</UL>

<P><STRONG><a name="[d9]"></a>_fp_value</STRONG> (Thumb, 296 bytes, Stack size 64 bytes, scanf_fp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2d
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
