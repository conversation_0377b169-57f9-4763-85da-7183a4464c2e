#include "btn_app.h"
#include "ebtn.h"
#include "gpio.h"
#include "adc_app.h"
#include "usart_app.h"
#include "config_app.h"
#include "rtc_app.h"
#include "sd_app.h"

/**
 * @brief LED控制数组的外部引用
 * @note  用于按键控制LED状态
 */
extern uint8_t ucLed[6];

// 全局显示模式变量 - 支持测评要求18项
display_mode_t g_display_mode = DISPLAY_CH0_RAW; // 默认显示Ch0原始数据

typedef enum
{
    USER_BUTTON_0 = 0,
    USER_BUTTON_1,
    USER_BUTTON_2,
    USER_BUTTON_3,
    USER_BUTTON_4,
    USER_BUTTON_5,
    USER_BUTTON_MAX,

} user_button_t;

/**
 * @brief 默认按键参数配置
 * @details 配置按键的防抖时间、有效按压时间等参数
 *          参数说明：
 *          - 防抖时间：20ms（按下）
 *          - 释放防抖：0ms（释放）
 *          - 最小按压时间：20ms（有效点击）
 *          - 最大按压时间：1000ms（长按检测）
 *          - 连击间隔：0ms（不检测连击）
 *          - 保持事件周期：1000ms（长按周期）
 *          - 最大连击次数：10次
 */
static const ebtn_btn_param_t defaul_ebtn_param = EBTN_PARAMS_INIT(20, 0, 20, 1000, 0, 1000, 10);

/**
 * @brief 按键对象数组
 * @details 为每个物理按键创建对应的按键对象
 *          使用统一的参数配置，确保按键行为一致
 */
static ebtn_btn_t btns[] = {
    EBTN_BUTTON_INIT(USER_BUTTON_0, &defaul_ebtn_param),    // 初始化KEY0
    EBTN_BUTTON_INIT(USER_BUTTON_1, &defaul_ebtn_param),    // 初始化KEY1
    EBTN_BUTTON_INIT(USER_BUTTON_2, &defaul_ebtn_param),    // 初始化KEY2
    EBTN_BUTTON_INIT(USER_BUTTON_3, &defaul_ebtn_param),    // 初始化KEY3
    EBTN_BUTTON_INIT(USER_BUTTON_4, &defaul_ebtn_param),    // 初始化KEY4
    EBTN_BUTTON_INIT(USER_BUTTON_5, &defaul_ebtn_param),    // 初始化KEY5
};


uint8_t prv_btn_get_state(struct ebtn_btn *btn)
{
    switch (btn->key_id)
    {
    case USER_BUTTON_0:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_15);
    case USER_BUTTON_1:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_13);
    case USER_BUTTON_2:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_11);
    case USER_BUTTON_3:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_9);
    case USER_BUTTON_4:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_7);
    case USER_BUTTON_5:
        return !HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_0);
    default:
        return 0;
    }
}

void prv_btn_event(struct ebtn_btn *btn, ebtn_evt_t evt)
{
    // 测评要求18项：按键显示切换功能
    // 按键0：单片机按键0 - 显示Ch0原始数据
    if ((btn->key_id == USER_BUTTON_0) && (ebtn_click_get_count(btn) == 1))
    {
        display_mode_set(DISPLAY_CH0_RAW);
        // my_printf(&huart1, "Display Mode: CH0 RAW\r\n");  // 关闭串口输出，避免干扰测评

        // 记录操作日志
        sd_write_log_data("display mode: CH0 RAW (key0->btn0 press)");
    }

    // 按键1：单片机按键1 - 显示Ch1原始数据
    if ((btn->key_id == USER_BUTTON_1) && (ebtn_click_get_count(btn) == 1))
    {
        display_mode_set(DISPLAY_CH1_RAW);
        // my_printf(&huart1, "Display Mode: CH1 RAW\r\n");  // 关闭串口输出，避免干扰测评

        // 记录操作日志
        sd_write_log_data("display mode: CH1 RAW (key1->btn1 press)");
    }

    // 按键2：单片机按键2 - 显示Ch2原始数据
    if ((btn->key_id == USER_BUTTON_2) && (ebtn_click_get_count(btn) == 1))
    {
        display_mode_set(DISPLAY_CH2_RAW);
        // my_printf(&huart1, "Display Mode: CH2 RAW\r\n");  // 关闭串口输出，避免干扰测评

        // 记录操作日志
        sd_write_log_data("display mode: CH2 RAW (key2->btn2 press)");
    }

    // 按键3：单片机按键3 - 显示Ch0变比后数据
    if ((btn->key_id == USER_BUTTON_3) && (ebtn_click_get_count(btn) == 1))
    {
        display_mode_set(DISPLAY_CH0_RATIO);
        // my_printf(&huart1, "Display Mode: CH0 RATIO\r\n");  // 关闭串口输出，避免干扰测评

        // 记录操作日志
        sd_write_log_data("display mode: CH0 RATIO (key3->btn3 press)");
    }

    // 按键4：单片机按键4 - 显示Ch1变比后数据
    if ((btn->key_id == USER_BUTTON_4) && (ebtn_click_get_count(btn) == 1))
    {
        display_mode_set(DISPLAY_CH1_RATIO);
        // my_printf(&huart1, "Display Mode: CH1 RATIO\r\n");  // 关闭串口输出，避免干扰测评

        // 记录操作日志
        sd_write_log_data("display mode: CH1 RATIO (key4->btn4 press)");
    }

    // 按键5：单片机按键5 - 显示Ch2变比后数据
    if ((btn->key_id == USER_BUTTON_5) && (ebtn_click_get_count(btn) == 1))
    {
        display_mode_set(DISPLAY_CH2_RATIO);
        // my_printf(&huart1, "Display Mode: CH2 RATIO\r\n");  // 关闭串口输出，避免干扰测评

        // 记录操作日志
        sd_write_log_data("display mode: CH2 RATIO (key5->btn5 press)");
    }
}

void app_btn_init(void)
{
    ebtn_init(btns, EBTN_ARRAY_SIZE(btns), NULL, 0, prv_btn_get_state, prv_btn_event);
		HAL_TIM_Base_Start_IT(&htim14);
}

void  HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
		if(htim == (&htim14))
		{
			ebtn_process(uwTick);
		}
}

void btn_task(void)
{

}

// --- 显示模式管理函数实现区域 ---

/**
 * @brief 设置显示模式
 * @param mode 显示模式
 */
void display_mode_set(display_mode_t mode)
{
    if (mode < DISPLAY_MODE_MAX) {
        g_display_mode = mode;
    }
}

/**
 * @brief 获取当前显示模式
 * @retval 当前显示模式
 */
display_mode_t display_mode_get(void)
{
    return g_display_mode;
}

/**
 * @brief 获取显示模式名称
 * @param mode 显示模式
 * @retval 显示模式名称字符串
 */
const char* display_mode_get_name(display_mode_t mode)
{
    switch (mode) {
        case DISPLAY_CH0_RAW:
            return "CH0 RAW";
        case DISPLAY_CH1_RAW:
            return "CH1 RAW";
        case DISPLAY_CH2_RAW:
            return "CH2 RAW";
        case DISPLAY_CH0_RATIO:
            return "CH0 RATIO";
        case DISPLAY_CH1_RATIO:
            return "CH1 RATIO";
        case DISPLAY_CH2_RATIO:
            return "CH2 RATIO";
        default:
            return "UNKNOWN";
    }
}
