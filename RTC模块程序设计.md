# RTC模块程序设计

## 1. 高精度与稳定性配置

### 1.1 LSE时钟源配置

```c
// 文件：Core/Src/rtc.c (第131-136行)
void HAL_RTC_MspInit(RTC_HandleTypeDef* rtcHandle)
{
    RCC_PeriphCLKInitTypeDef PeriphClkInitStruct = {0};
    if(rtcHandle->Instance==RTC)
    {
        /** Initializes the peripherals clock */
        PeriphClkInitStruct.PeriphClockSelection = RCC_PERIPHCLK_RTC;
        PeriphClkInitStruct.RTCClockSelection = RCC_RTCCLKSOURCE_LSE;  // 选择LSE作为RTC时钟源
        if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInitStruct) != HAL_OK)
        {
            Error_Handler();
        }
        /* RTC clock enable */
        __HAL_RCC_RTC_ENABLE();
    }
}
```

### 1.2 精确的预分频器配置

```c
// 文件：Core/Src/rtc.c (第46-56行)
void MX_RTC_Init(void)
{
    /** Initialize RTC Only */
    hrtc.Instance = RTC;
    hrtc.Init.HourFormat = RTC_HOURFORMAT_24;
    hrtc.Init.AsynchPrediv = 127;    // 异步预分频器：实际分频128 (127+1)
    hrtc.Init.SynchPrediv = 255;     // 同步预分频器：实际分频256 (255+1)
    hrtc.Init.OutPut = RTC_OUTPUT_DISABLE;
    hrtc.Init.OutPutPolarity = RTC_OUTPUT_POLARITY_HIGH;
    hrtc.Init.OutPutType = RTC_OUTPUT_TYPE_OPENDRAIN;
    
    if (HAL_RTC_Init(&hrtc) != HAL_OK)
    {
        Error_Handler();
    }
    
    // 计算：32.768KHz ÷ 128 ÷ 256 = 1Hz (精确的秒级脉冲)
}
```

### 1.3 VBAT与备份域管理

```c
// 文件：Core/Src/rtc.c (第58-112行)
#define RTC_BACKUP_MARK 0x32F2  // 自定义备份域标记

void MX_RTC_Init(void)
{
    // 检查备份域是否已经初始化，避免重复设置时间
    uint32_t backup_reg = HAL_RTCEx_BKUPRead(&hrtc, RTC_BKP_DR0);
    
    // 额外检查：读取当前RTC时间，如果时间合理则不重新初始化
    RTC_TimeTypeDef current_time = {0};
    RTC_DateTypeDef current_date = {0};
    HAL_RTC_GetTime(&hrtc, &current_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_date, RTC_FORMAT_BIN);
    
    // 检查时间是否合理（年份在2024-2030之间，且不是默认值）
    uint8_t time_is_valid = 0;
    if (current_date.Year >= 24 && current_date.Year <= 30) {  // 2024-2030
        // 如果不是默认的2025-01-01 00:00:xx，认为时间有效
        if (!(current_date.Year == 25 && current_date.Month == 1 && current_date.Date == 1 &&
              current_time.Hours == 0 && current_time.Minutes == 0)) {
            time_is_valid = 1;
        }
    }
    
    if (backup_reg != RTC_BACKUP_MARK && !time_is_valid) {
        // 备份域未初始化且时间无效，设置默认时间
        sTime.Hours = 0;
        sTime.Minutes = 0;
        sTime.Seconds = 0;
        sDate.Year = 25;    // 2025年
        sDate.Month = RTC_MONTH_JANUARY;
        sDate.Date = 1;
        
        HAL_RTC_SetTime(&hrtc, &sTime, RTC_FORMAT_BIN);
        HAL_RTC_SetDate(&hrtc, &sDate, RTC_FORMAT_BIN);
        
        // 标记RTC已经初始化
        HAL_RTCEx_BKUPWrite(&hrtc, RTC_BKP_DR0, RTC_BACKUP_MARK);
    }
}
```

## 2. 智能初始化机制

### 2.1 三层检查逻辑

```c
// 文件：Core/Src/rtc.c (第79行)
if (backup_reg != RTC_BACKUP_MARK && !time_is_valid) {
    // 只有当"备份域未标记"并且"当前时间无效"时，才设置默认时间
}
```

**检查层次：**
1. **备份域标记检查**：读取RTC_BKP_DR0寄存器，检查是否为0x32F2
2. **当前时间有效性验证**：验证时间是否在合理范围内
3. **智能决策**：综合判断是否需要重新初始化

### 2.2 设置结果验证与同步

```c
// 文件：sysFunction/rtc_app.c (第104-124行)
HAL_StatusTypeDef rtc_set_time_from_string(const char *time_str)
{
    // 设置RTC日期（必须先设置日期再设置时间）
    if (HAL_RTC_SetDate(&hrtc, &sDate, RTC_FORMAT_BIN) != HAL_OK) {
        return HAL_ERROR;
    }
    
    // 设置RTC时间
    if (HAL_RTC_SetTime(&hrtc, &sTime, RTC_FORMAT_BIN) != HAL_OK) {
        return HAL_ERROR;
    }
    
    // 等待RTC寄存器同步（关键修复）
    HAL_Delay(10); // 短暂延时确保寄存器更新
    
    // 验证设置是否成功
    RTC_TimeTypeDef verify_time = {0};
    RTC_DateTypeDef verify_date = {0};
    HAL_RTC_GetTime(&hrtc, &verify_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &verify_date, RTC_FORMAT_BIN);
    
    // 检查设置的时间是否与读取的时间一致
    if (verify_time.Hours != sTime.Hours ||
        verify_time.Minutes != sTime.Minutes ||
        verify_date.Year != sDate.Year ||
        verify_date.Month != sDate.Month ||
        verify_date.Date != sDate.Date) {
        return HAL_ERROR;  // 设置失败
    }
    
    return HAL_OK;
}
```

## 3. 易用性功能实现

### 3.1 竞赛输出格式

```c
// 文件：sysFunction/rtc_app.c (第188-204行)
void rtc_format_current_time_string(char *buffer, size_t buffer_size)
{
    if (buffer == NULL || buffer_size == 0) return;
    
    RTC_TimeTypeDef current_time = {0};
    RTC_DateTypeDef current_date = {0};
    
    // 获取当前时间和日期（必须先读时间再读日期）
    HAL_RTC_GetTime(&hrtc, &current_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_date, RTC_FORMAT_BIN);
    
    // 格式化为竞赛要求的标准格式："YYYY-MM-DD HH:MM:SS"
    snprintf(buffer, buffer_size, "%04d-%02d-%02d %02d:%02d:%02d",
             current_date.Year + 2000,  // 转换为4位年份
             current_date.Month,
             current_date.Date,
             current_time.Hours,
             current_time.Minutes,
             current_time.Seconds);
}
```

### 3.2 时间字符串解析

```c
// 文件：sysFunction/rtc_app.c (第27-73行)
static HAL_StatusTypeDef parse_time_string(const char *time_str, 
                                          RTC_TimeTypeDef *sTime, 
                                          RTC_DateTypeDef *sDate)
{
    if (time_str == NULL || sTime == NULL || sDate == NULL) {
        return HAL_ERROR;
    }
    int year, month, day, hour, minute, second;
    int parsed = 0;
    
    // 尝试标准格式: "2025-01-01 12:00:30"
    parsed = sscanf(time_str, "%d-%d-%d %d:%d:%d", 
                   &year, &month, &day, &hour, &minute, &second);
    // 如果标准格式解析失败，尝试紧凑格式: "2025-01-01 01-30-10"
    if (parsed != 6) {
        parsed = sscanf(time_str, "%d-%d-%d %d-%d-%d", 
                       &year, &month, &day, &hour, &minute, &second);
    }
    // 参数验证
    if (parsed != 6 || year < 2000 || year > 2099 || 
        month < 1 || month > 12 || day < 1 || day > 31 || 
        hour > 23 || minute > 59 || second > 59) {
        return HAL_ERROR;
    }
    // 设置时间结构体
    sTime->Hours = hour;
    sTime->Minutes = minute;
    sTime->Seconds = second;
    sTime->DayLightSaving = RTC_DAYLIGHTSAVING_NONE;
    sTime->StoreOperation = RTC_STOREOPERATION_RESET;
    // 设置日期结构体 (年份需要转换为2位数)
    sDate->Year = year - 2000; // 2025 -> 25
    sDate->Month = month;
    sDate->Date = day;
    sDate->WeekDay = RTC_WEEKDAY_MONDAY; // 默认设置为周一
    return HAL_OK;
}
```

### 3.3 特定的时区修正

```c
// 文件：sysFunction/rtc_app.c (第265-267行)
uint32_t rtc_convert_to_unix_timestamp(const RTC_TimeTypeDef *sTime, 
                                      const RTC_DateTypeDef *sDate)
{
    // ... Unix时间戳计算逻辑 ...
    
    // 修正时区问题：赛事方解码程序会增加8小时，所以我们减去8小时（28800秒）
    // 这样解码后的时间就是正确的本地时间
    timestamp -= 28800;  // 8 * 3600 = 28800秒
    
    return timestamp;
}
```

## 4. 模块化与可扩展性

### 4.1 清晰的功能分层

#### 硬件抽象层
```c
// 文件：Core/Src/rtc.c
void MX_RTC_Init(void);           // RTC硬件初始化
void HAL_RTC_MspInit(RTC_HandleTypeDef* rtcHandle);  // MSP初始化
```

#### 应用服务层
```c
// 文件：sysFunction/rtc_app.c
HAL_StatusTypeDef rtc_set_time_from_string(const char *time_str);  // 时间设置服务
void rtc_format_current_time_string(char *buffer, size_t buffer_size);  // 时间查询服务
```

#### 业务逻辑层
```c
// 文件：sysFunction/rtc_app.c
static HAL_StatusTypeDef parse_time_string(...);  // 时间解析算法
uint32_t rtc_convert_to_unix_timestamp(...);      // Unix时间戳转换算法
```

#### 接口适配层
```c
// 文件：FATFS/App/fatfs.c (第46-86行)
DWORD get_fattime(void)
{
    RTC_TimeTypeDef current_time = {0};
    RTC_DateTypeDef current_date = {0};
    
    // 获取当前RTC时间
    HAL_RTC_GetTime(&hrtc, &current_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_date, RTC_FORMAT_BIN);
    
    // FATFS时间格式转换：
    // bit31:25 年份（从1980年开始计算）
    // bit24:21 月份（1-12）
    // bit20:16 日期（1-31）
    // bit15:11 小时（0-23）
    // bit10:5  分钟（0-59）
    // bit4:0   秒/2（0-29）
    
    DWORD fattime = 0;
    fattime |= ((DWORD)(current_date.Year + 2000 - 1980) & 0x7F) << 25;
    fattime |= ((DWORD)current_date.Month & 0x0F) << 21;
    fattime |= ((DWORD)current_date.Date & 0x1F) << 16;
    fattime |= ((DWORD)current_time.Hours & 0x1F) << 11;
    fattime |= ((DWORD)current_time.Minutes & 0x3F) << 5;
    fattime |= ((DWORD)current_time.Seconds / 2) & 0x1F;
    
    return fattime;
}
```

### 4.2 Unix时间戳的通用性

```c
// 文件：sysFunction/rtc_app.c (第221-270行)
uint32_t rtc_convert_to_unix_timestamp(const RTC_TimeTypeDef *sTime, 
                                      const RTC_DateTypeDef *sDate)
{
    if (sTime == NULL || sDate == NULL) {
        return 0;
    }
    // Unix时间戳基准：1970年1月1日 00:00:00 UTC
    int year = sDate->Year + 2000;  // 转换为4位年份
    int month = sDate->Month;
    int day = sDate->Date;
    int hour = sTime->Hours;
    int minute = sTime->Minutes;
    int second = sTime->Seconds;
    // 计算从1970年到指定年份的天数
    uint32_t days = 0;
    // 计算年份贡献的天数（包含闰年处理）
    for (int y = 1970; y < year; y++) {
        if ((y % 4 == 0 && y % 100 != 0) || (y % 400 == 0)) {
            days += 366;  // 闰年
        } else {
            days += 365;  // 平年
        }
    }
    // 每月天数表（平年）
    static const int days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    // 计算月份贡献的天数
    for (int m = 1; m < month; m++) {
        days += days_in_month[m - 1];
        // 如果是闰年且已经过了2月，需要加1天
        if (m == 2 && ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0))) {
            days += 1;
        }
    }
    // 加上当月的天数（减1因为当天还没过完）
    days += (day - 1);
    // 转换为秒
    uint32_t timestamp = days * 24 * 3600 + hour * 3600 + minute * 60 + second;
    // 时区修正：减去8小时（28800秒）
    timestamp -= 28800;
    return timestamp;
}

// 获取当前时间的Unix时间戳
uint32_t rtc_get_unix_timestamp_now(void)
{
    RTC_TimeTypeDef current_time = {0};
    RTC_DateTypeDef current_date = {0};
    
    HAL_RTC_GetTime(&hrtc, &current_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_date, RTC_FORMAT_BIN);
    
    return rtc_convert_to_unix_timestamp(&current_time, &current_date);
}
```

## 5. 关键技术特点

### 5.1 时钟精度保证
- **LSE晶振**：32.768KHz低速外部晶振
- **精确分频**：128 × 256 = 32768，确保1Hz输出
- **VBAT供电**：独立电源域，断电保持

### 5.2 智能化管理
- **三层验证**：备份域标记 + 时间有效性 + 智能决策
- **自动恢复**：系统重启后自动判断是否需要重新初始化
- **数据验证**：设置后立即验证，确保操作成功

### 5.3 多格式支持
- **竞赛格式**：YYYY-MM-DD HH:MM:SS
- **FatFS格式**：32位压缩时间戳
- **Unix时间戳**：标准Unix时间戳（含时区修正）
- **紧凑格式**：YYYYMMDDHHMMSS（14位数字）
