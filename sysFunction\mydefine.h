/**
 * @file    mydefine.h
 * @brief   系统公共头文件定义
 * @details 包含系统所有公共头文件和全局变量声明
 *          为2025 CIMC西门子杯竞赛项目提供统一的头文件管理
 * <AUTHOR> CIMC西门子杯竞赛项目组
 * @date    2025-01-01
 * @version 1.0
 */

#ifndef __MYDEFINE_H__
#define __MYDEFINE_H__

// === 串口选择配置宏定义 ===
#define UART_SELECT 2  // 1=使用串口1, 2=使用串口2

// === RS485控制引脚定义 ===
#define RS485_DE_RE_PIN GPIO_PIN_1  // PA1控制MAX3485的DE/RE引脚
#define RS485_DE_RE_PORT GPIOA
#define RS485_TX_ENABLE()  HAL_GPIO_WritePin(RS485_DE_RE_PORT, RS485_DE_RE_PIN, GPIO_PIN_SET)   // 发送模式
#define RS485_RX_ENABLE()  HAL_GPIO_WritePin(RS485_DE_RE_PORT, RS485_DE_RE_PIN, GPIO_PIN_RESET) // 接收模式

// === 标准C库头文件 ===
#include "stdio.h"
#include "string.h"
#include "stdarg.h"
#include "stdint.h"
#include "stdlib.h"

// === STM32 HAL库和外设头文件 ===
#include "main.h"
#include "usart.h"
#include "math.h"
#include "adc.h"
#include "tim.h"
//#include "dac.h"
#include "rtc.h"
#include "i2c.h"
#include "sdio.h"

// === 第三方库头文件 ===
#include "ringbuffer.h"     // 环形缓冲区
#include "arm_math.h"       // ARM数学库
#include "gd25qxx.h"        // Flash驱动

// === 应用层头文件（注意：不包含selftest_app.h避免循环包含） ===
#include "scheduler.h"      // 任务调度器
#include "adc_app.h"        // ADC应用层
#include "led_app.h"        // LED应用层
#include "btn_app.h"        // 按键应用层
#include "usart_app.h"      // 串口应用层
#include "oled_app.h"       // OLED应用层
#include "flash_app.h"      // Flash应用层
#include "sd_app.h"         // SD卡应用层
#include "rtc_app.h"        // RTC应用层
#include "config_app.h"     // 配置管理应用层
#include "gd30ad3344.h"      // GD30AD3344采样板驱动
#include "sampling_board_app.h" // 采样板应用层

// === 串口选择宏定义 ===
#if UART_SELECT == 1
    #define CURRENT_UART huart1
    #define CURRENT_DMA_RX hdma_usart1_rx
#elif UART_SELECT == 2
    #define CURRENT_UART huart2
    #define CURRENT_DMA_RX hdma_usart2_rx
#endif

// === 全局变量声明区域 ===
// 串口相关全局变量
extern uint16_t uart_rx_index;              // 串口接收索引
extern uint32_t uart_rx_ticks;              // 串口接收时间戳
extern uint8_t uart_rx_buffer[128];         // 串口接收缓冲区
extern uint8_t uart_rx_dma_buffer[128];     // 串口DMA接收缓冲区
extern UART_HandleTypeDef huart1;           // 串口1句柄
extern UART_HandleTypeDef huart2;           // 串口2句柄
extern DMA_HandleTypeDef hdma_usart1_rx;    // 串口1 DMA接收句柄
extern DMA_HandleTypeDef hdma_usart2_rx;    // 串口2 DMA接收句柄
extern struct rt_ringbuffer uart_ringbuffer; // 串口环形缓冲区
extern uint8_t ringbuffer_pool[128];        // 环形缓冲区内存池
extern uint8_t uart_send_flag;              // 串口发送标志

#endif /* __MYDEFINE_H__ */


