# 串口切换功能演示

## 功能完成情况

✅ **已完成串口2的完整数据收发功能**
✅ **已实现宏定义切换机制**
✅ **已添加测试命令和验证功能**

## 实现的功能

### 1. 串口2数据收发功能
- DMA接收配置完成
- 环形缓冲区支持
- 中断回调函数
- 命令解析功能
- 数据发送功能

### 2. 宏定义切换机制
```c
// 在 sysFunction/mydefine.h 中
#define UART_SELECT 1  // 1=串口1, 2=串口2

// 自动选择串口句柄
#if UART_SELECT == 1
    #define CURRENT_UART huart1
    #define CURRENT_DMA_RX hdma_usart1_rx
#elif UART_SELECT == 2
    #define CURRENT_UART huart2
    #define CURRENT_DMA_RX hdma_usart2_rx
#endif
```

### 3. 新增测试功能
- `uart_printf()` 函数：自动选择当前串口
- `uart_test` 命令：测试串口切换功能
- 完整的测试和验证机制

## 使用演示

### 步骤1：当前配置（串口1）
```c
#define UART_SELECT 1  // 使用串口1
```
- 波特率：460800
- 引脚：PA9(TX), PA10(RX)
- 所有命令和数据通过串口1收发

### 步骤2：切换到串口2
```c
#define UART_SELECT 2  // 使用串口2
```
- 波特率：9600
- 引脚：PA2(TX), PA3(RX)
- 所有命令和数据自动切换到串口2

### 步骤3：测试验证
在任一串口终端中输入：
```
uart_test
```

预期输出：
```
=== UART Switch Test ===
Current UART: UART1 (或 UART2)
Baud Rate: 460800 (或 9600)
GPIO: PA9(TX), PA10(RX) (或 PA2(TX), PA3(RX))
=== UART Communication Test ===
Test 1: Basic output - OK
Test 2: Formatted output - int:123, float:45.67
Test 3: Long string output - This is a longer test string...
=== Test Complete ===
```

## 技术特点

### 1. 完全自动化切换
- 修改一个宏定义即可切换所有功能
- 无需修改任何其他代码
- 编译时自动选择正确的串口配置

### 2. 功能完整性
- 数据接收：DMA + 环形缓冲区
- 数据发送：所有printf和输出
- 命令解析：完整的命令系统
- 中断处理：接收完成和DMA事件
- 二进制协议：支持所有协议功能

### 3. 兼容性保持
- 原有代码无需修改
- 保留所有现有功能
- 向后兼容所有接口

## 验证方法

### 方法1：命令测试
1. 连接串口1（PA9/PA10，460800波特率）
2. 输入 `test` 命令，验证系统响应
3. 输入 `uart_test` 命令，查看当前配置
4. 修改 `UART_SELECT` 为 2，重新编译
5. 连接串口2（PA2/PA3，9600波特率）
6. 重复测试，验证切换成功

### 方法2：功能验证
测试以下命令在两个串口上都能正常工作：
- `test` - 系统自检
- `RTC now` - 时间显示
- `conf` - 配置读取
- `system status` - 系统状态
- `uart_test` - 串口测试

## 文件修改总结

### 核心文件
1. **sysFunction/mydefine.h**
   - 添加 `UART_SELECT` 宏定义
   - 添加串口选择宏

2. **sysFunction/usart_app.c**
   - 修改所有串口相关函数支持切换
   - 添加 `uart_printf()` 函数
   - 添加测试命令处理

3. **Core/Src/usart.c**
   - 修改 `fputc()` 函数支持切换
   - 添加串口2的DMA初始化

### 新增文件
1. **sysFunction/uart_test.c** - 测试功能实现
2. **sysFunction/uart_test.h** - 测试功能头文件

## 总结

✅ **串口2功能完整实现**：已完成串口2的所有数据收发功能，包括DMA、环形缓冲区、中断处理等。

✅ **宏定义切换机制**：通过修改 `UART_SELECT` 宏定义（1或2），即可在串口1和串口2之间完全切换。

✅ **测试验证功能**：提供了完整的测试命令和验证机制，确保切换功能正常工作。

**使用方法**：只需修改 `mydefine.h` 中的 `UART_SELECT` 值，重新编译即可实现串口切换！
