/**
 * @file    第5章_实践练习与代码示例.c
 * @brief   OLED显示与I2C通信实现 - 实践练习代码
 * @details 通过实际代码示例帮助理解I2C配置、OLED显示原理和优化技巧的实现
 * <AUTHOR>
 * @date    2025-01-07
 */

#include "main.h"
#include "stdio.h"
#include "string.h"
#include "stdarg.h"
#include "i2c.h"

// ============================================================================
// 练习1：I2C配置详解
// ============================================================================

/**
 * @brief 演示I2C_HandleTypeDef结构体配置
 */
void practice_i2c_config_demo(void)
{
    printf("=== I2C配置演示 ===\r\n");
    
    // 分析项目中的I2C配置
    extern I2C_HandleTypeDef hi2c1;
    
    printf("I2C_HandleTypeDef结构体大小: %d 字节\r\n", sizeof(I2C_HandleTypeDef));
    printf("当前I2C配置:\r\n");
    printf("  外设实例: I2C%d\r\n", 
           (hi2c1.Instance == I2C1) ? 1 : 
           (hi2c1.Instance == I2C2) ? 2 : 3);
    printf("  时钟速度: %d Hz\r\n", hi2c1.Init.ClockSpeed);
    printf("  占空比: %s\r\n", 
           (hi2c1.Init.DutyCycle == I2C_DUTYCYCLE_2) ? "2:1" : "16:9");
    printf("  主机地址: 0x%02X\r\n", hi2c1.Init.OwnAddress1);
    printf("  地址模式: %s\r\n", 
           (hi2c1.Init.AddressingMode == I2C_ADDRESSINGMODE_7BIT) ? "7位" : "10位");
    printf("  双地址模式: %s\r\n", 
           (hi2c1.Init.DualAddressMode == I2C_DUALADDRESS_ENABLE) ? "使能" : "禁用");
    printf("  广播模式: %s\r\n", 
           (hi2c1.Init.GeneralCallMode == I2C_GENERALCALL_ENABLE) ? "使能" : "禁用");
    printf("  时钟拉伸: %s\r\n", 
           (hi2c1.Init.NoStretchMode == I2C_NOSTRETCH_ENABLE) ? "禁用" : "使能");
    
    // 计算I2C时序参数
    uint32_t pclk1_freq = HAL_RCC_GetPCLK1Freq();  // I2C1在APB1上
    uint32_t i2c_period = 1000000 / (hi2c1.Init.ClockSpeed / 1000);  // 周期(ns)
    
    printf("  APB1时钟: %d Hz\r\n", pclk1_freq);
    printf("  I2C周期: %d ns\r\n", i2c_period);
    printf("  理论最大速度: %s\r\n", 
           (hi2c1.Init.ClockSpeed <= 100000) ? "标准模式(100kHz)" :
           (hi2c1.Init.ClockSpeed <= 400000) ? "快速模式(400kHz)" : "快速模式+(1MHz)");
}

/**
 * @brief 演示I2C通信时序分析
 */
void practice_i2c_timing_demo(void)
{
    printf("=== I2C通信时序演示 ===\r\n");
    
    printf("I2C通信时序分析:\r\n");
    printf("1. 起始条件 (START):\r\n");
    printf("   - SCL为高电平时，SDA从高变低\r\n");
    printf("   - 表示通信开始\r\n");
    
    printf("2. 地址传输:\r\n");
    printf("   - 7位从设备地址 + 1位读写位\r\n");
    printf("   - OLED地址: 0x78 (0x3C << 1)\r\n");
    printf("   - 写操作: 0x78, 读操作: 0x79\r\n");
    
    printf("3. 应答信号 (ACK):\r\n");
    printf("   - 从设备拉低SDA表示应答\r\n");
    printf("   - 主设备检测到低电平确认通信成功\r\n");
    
    printf("4. 数据传输:\r\n");
    printf("   - 8位数据，MSB先传输\r\n");
    printf("   - 每字节后都有ACK/NACK\r\n");
    
    printf("5. 停止条件 (STOP):\r\n");
    printf("   - SCL为高电平时，SDA从低变高\r\n");
    printf("   - 表示通信结束\r\n");
    
    // 模拟I2C传输时间计算
    uint32_t clock_speed = 400000;  // 400kHz
    uint32_t bit_time = 1000000 / clock_speed;  // 每位时间(us)
    uint32_t byte_time = bit_time * 9;  // 8位数据 + 1位ACK
    
    printf("\n时序参数计算 (400kHz):\r\n");
    printf("  每位时间: %.1f μs\r\n", (float)bit_time / 1000);
    printf("  每字节时间: %.1f μs\r\n", (float)byte_time / 1000);
    printf("  传输1KB数据时间: %.1f ms\r\n", (float)(byte_time * 1024) / 1000000);
}

// ============================================================================
// 练习2：OLED基本操作演示
// ============================================================================

/**
 * @brief 演示OLED基本读写操作
 */
void practice_oled_basic_demo(void)
{
    printf("=== OLED基本操作演示 ===\r\n");
    
    printf("OLED硬件规格:\r\n");
    printf("  尺寸: 0.91英寸\r\n");
    printf("  分辨率: 128×32像素\r\n");
    printf("  控制器: SSD1306\r\n");
    printf("  接口: I2C\r\n");
    printf("  地址: 0x78\r\n");
    
    printf("\nOLED内存布局:\r\n");
    printf("  Page 0: 像素行 0-7   (第1行字符)\r\n");
    printf("  Page 1: 像素行 8-15  (第2行字符)\r\n");
    printf("  Page 2: 像素行 16-23 (第3行字符)\r\n");
    printf("  Page 3: 像素行 24-31 (第4行字符)\r\n");
    
    // 演示基本写入操作
    printf("\n基本写入操作演示:\r\n");
    
    // 模拟写入命令
    uint8_t test_cmd = 0xAF;  // 显示开启命令
    printf("1. 写入命令: 0x%02X\r\n", test_cmd);
    printf("   I2C地址: 0x78, 寄存器: 0x00\r\n");
    
    // 模拟写入数据
    uint8_t test_data = 0xFF;  // 全亮像素
    printf("2. 写入数据: 0x%02X\r\n", test_data);
    printf("   I2C地址: 0x78, 寄存器: 0x40\r\n");
    
    // 演示位置设置
    uint8_t x = 64, y = 1;  // 中间位置
    printf("3. 设置位置: x=%d, y=%d\r\n", x, y);
    printf("   页地址命令: 0x%02X\r\n", 0xB0 + y);
    printf("   列地址高位: 0x%02X\r\n", 0x10 + (x >> 4));
    printf("   列地址低位: 0x%02X\r\n", 0x01 + (x & 0x0F));
}

/**
 * @brief 演示OLED初始化序列
 */
void practice_oled_init_demo(void)
{
    printf("=== OLED初始化序列演示 ===\r\n");
    
    // SSD1306初始化命令解析
    typedef struct {
        uint8_t cmd;
        const char *description;
    } oled_cmd_desc_t;
    
    oled_cmd_desc_t init_commands[] = {
        {0xAE, "关闭显示"},
        {0xD5, "设置显示时钟分频比"},
        {0x80, "时钟分频比参数"},
        {0xA8, "设置多路复用比"},
        {0x1F, "多路复用比参数(32-1)"},
        {0xD3, "设置显示偏移"},
        {0x00, "显示偏移参数"},
        {0x40, "设置显示起始行"},
        {0x8D, "设置电荷泵"},
        {0x14, "电荷泵参数(使能)"},
        {0xA1, "设置段重映射(翻转)"},
        {0xC8, "设置COM扫描方向(翻转)"},
        {0xDA, "设置COM引脚配置"},
        {0x00, "COM引脚配置参数"},
        {0x81, "设置对比度控制"},
        {0x80, "对比度参数(中等)"},
        {0xD9, "设置预充电周期"},
        {0x1F, "预充电周期参数"},
        {0xDB, "设置VCOM取消选择级别"},
        {0x40, "VCOM级别参数"},
        {0xA4, "设置正常显示"},
        {0xAF, "开启显示"},
    };
    
    uint8_t cmd_count = sizeof(init_commands) / sizeof(oled_cmd_desc_t);
    
    printf("SSD1306初始化命令序列:\r\n");
    for (uint8_t i = 0; i < cmd_count; i++) {
        printf("  0x%02X - %s\r\n", init_commands[i].cmd, init_commands[i].description);
    }
    
    printf("\n关键参数说明:\r\n");
    printf("  对比度: 0x80 (中等亮度，可调范围0x00-0xFF)\r\n");
    printf("  多路复用: 0x1F (32行显示)\r\n");
    printf("  电荷泵: 0x14 (内部电源，无需外部电源)\r\n");
    printf("  扫描方向: 翻转显示，适配PCB布局\r\n");
}

// ============================================================================
// 练习3：字符显示算法演示
// ============================================================================

/**
 * @brief 演示字符编码和显示算法
 */
void practice_char_display_demo(void)
{
    printf("=== 字符显示算法演示 ===\r\n");
    
    // 模拟字符'A'的显示过程
    char test_char = 'A';
    uint8_t char_index = test_char - ' ';  // ASCII码转换
    
    printf("字符显示算法:\r\n");
    printf("1. 字符: '%c' (ASCII: %d)\r\n", test_char, test_char);
    printf("2. 字库索引: %d - %d = %d\r\n", test_char, ' ', char_index);
    printf("3. 字体大小: 8×16像素 (宽8，高16)\r\n");
    printf("4. 占用页数: 2页 (每页8像素高)\r\n");
    
    // 模拟字库数据
    printf("5. 字库数据结构:\r\n");
    printf("   F8X16[%d * 16 + 0~7]:  上半部分(Page n)\r\n", char_index);
    printf("   F8X16[%d * 16 + 8~15]: 下半部分(Page n+1)\r\n", char_index);
    
    // 显示位置计算
    uint8_t x = 32, y = 1;
    printf("6. 显示位置: x=%d, y=%d\r\n", x, y);
    printf("   上半部分位置: (%d, %d)\r\n", x, y);
    printf("   下半部分位置: (%d, %d)\r\n", x, y + 1);
    
    // 字符串显示算法
    printf("\n字符串显示算法:\r\n");
    const char *test_str = "Hello";
    printf("字符串: \"%s\"\r\n", test_str);
    
    uint8_t str_x = 0;
    for (uint8_t i = 0; test_str[i] != '\0'; i++) {
        printf("  字符%d: '%c' -> 位置(%d, %d)\r\n", 
               i + 1, test_str[i], str_x, y);
        str_x += 8;  // 每个字符宽8像素
        
        if (str_x > 120) {  // 换行处理
            str_x = 0;
            y += 2;
            printf("    换行到: (%d, %d)\r\n", str_x, y);
        }
    }
}

/**
 * @brief 演示格式化输出实现
 */
void practice_printf_demo(void)
{
    printf("=== 格式化输出演示 ===\r\n");
    
    // 模拟Oled_Printf函数的实现过程
    printf("Oled_Printf实现过程:\r\n");
    
    // 1. 格式化字符串
    const char *format = "%02d:%02d:%02d";
    int hours = 14, minutes = 30, seconds = 25;
    
    printf("1. 输入参数:\r\n");
    printf("   格式字符串: \"%s\"\r\n", format);
    printf("   参数: %d, %d, %d\r\n", hours, minutes, seconds);
    
    // 2. 使用vsnprintf格式化
    char buffer[128];
    int len = snprintf(buffer, sizeof(buffer), format, hours, minutes, seconds);
    
    printf("2. 格式化结果:\r\n");
    printf("   输出字符串: \"%s\"\r\n", buffer);
    printf("   字符串长度: %d\r\n", len);
    
    // 3. 调用OLED_ShowStr显示
    uint8_t x = 32, y = 0;
    printf("3. 显示到OLED:\r\n");
    printf("   位置: (%d, %d)\r\n", x, y);
    printf("   字体大小: 16\r\n");
    printf("   显示内容: \"%s\"\r\n", buffer);
    
    // 4. 计算显示区域
    uint8_t char_width = 8;
    uint8_t display_width = len * char_width;
    printf("4. 显示区域:\r\n");
    printf("   字符数: %d\r\n", len);
    printf("   显示宽度: %d像素\r\n", display_width);
    printf("   占用页数: 2页\r\n");
}

// ============================================================================
// 练习4：显示优化技巧演示
// ============================================================================

/**
 * @brief 演示显示优化策略
 */
void practice_display_optimization_demo(void)
{
    printf("=== 显示优化策略演示 ===\r\n");
    
    // 模拟状态检测优化
    printf("1. 状态检测优化:\r\n");
    
    static int last_state = -1;
    static float last_voltage = -1.0f;
    
    int current_state = 1;  // 模拟当前状态
    float current_voltage = 3.25f;  // 模拟当前电压
    
    printf("   上次状态: %d, 当前状态: %d\r\n", last_state, current_state);
    printf("   上次电压: %.2f, 当前电压: %.2f\r\n", last_voltage, current_voltage);
    
    if (last_state != current_state) {
        printf("   → 状态改变，执行全屏清除\r\n");
        last_state = current_state;
        last_voltage = -1.0f;  // 强制刷新电压显示
    } else {
        printf("   → 状态未变，跳过全屏清除\r\n");
    }
    
    if (last_voltage != current_voltage) {
        printf("   → 电压改变，局部清除电压显示区域\r\n");
        last_voltage = current_voltage;
    } else {
        printf("   → 电压未变，跳过电压区域更新\r\n");
    }
    
    // 局部刷新优化
    printf("\n2. 局部刷新优化:\r\n");
    printf("   全屏刷新: 128×32 = 4096像素\r\n");
    printf("   电压区域: 64×16 = 1024像素 (25%%)\r\n");
    printf("   时间区域: 64×16 = 1024像素 (25%%)\r\n");
    printf("   优化效果: 减少75%%的无效刷新\r\n");
    
    // 刷新频率优化
    printf("\n3. 刷新频率优化:\r\n");
    printf("   OLED任务周期: 100ms\r\n");
    printf("   时间更新频率: 1Hz (秒变化)\r\n");
    printf("   电压更新频率: 根据ADC采样\r\n");
    printf("   状态更新频率: 根据用户操作\r\n");
    
    // 显示内容布局优化
    printf("\n4. 显示布局优化:\r\n");
    printf("   第1行 (Page 0-1): 时间显示 \"14:30:25\"\r\n");
    printf("   第2行 (Page 1-2): 空行或状态\r\n");
    printf("   第3行 (Page 2-3): 电压显示 \"3.25V\"\r\n");
    printf("   第4行 (Page 3):   预留扩展\r\n");
}

/**
 * @brief 演示I2C通信优化
 */
void practice_i2c_optimization_demo(void)
{
    printf("=== I2C通信优化演示 ===\r\n");
    
    printf("I2C通信优化策略:\r\n");
    
    printf("1. 批量传输优化:\r\n");
    printf("   单字节传输: 每次9个时钟周期 (8位数据+ACK)\r\n");
    printf("   批量传输: 减少START/STOP开销\r\n");
    printf("   优化效果: 提高20-30%%传输效率\r\n");
    
    printf("2. 时钟速度优化:\r\n");
    printf("   标准模式: 100kHz\r\n");
    printf("   快速模式: 400kHz (项目使用)\r\n");
    printf("   速度提升: 4倍传输速度\r\n");
    
    printf("3. 错误处理优化:\r\n");
    printf("   超时设置: 0x100 (256ms)\r\n");
    printf("   重试机制: 检测ACK失败自动重试\r\n");
    printf("   错误恢复: 总线复位和重新初始化\r\n");
    
    printf("4. 功耗优化:\r\n");
    printf("   按需刷新: 只更新变化的内容\r\n");
    printf("   显示关闭: 空闲时关闭OLED显示\r\n");
    printf("   时钟门控: 不使用时关闭I2C时钟\r\n");
    
    // 计算通信时间
    uint32_t pixel_count = 128 * 32 / 8;  // 总字节数
    uint32_t clock_speed = 400000;        // 400kHz
    uint32_t bit_time = 1000000 / clock_speed;  // 每位时间(ns)
    uint32_t transfer_time = pixel_count * 9 * bit_time / 1000;  // 传输时间(us)
    
    printf("\n通信时间计算:\r\n");
    printf("   显示数据: %d 字节\r\n", pixel_count);
    printf("   传输时间: %d μs\r\n", transfer_time);
    printf("   刷新率: %.1f Hz\r\n", 1000000.0f / transfer_time);
}

// ============================================================================
// 主练习函数
// ============================================================================

/**
 * @brief 第5章所有练习的入口函数
 */
void chapter5_practice_all(void)
{
    printf("\r\n");
    printf("========================================\r\n");
    printf("    第5章：OLED显示与I2C通信实现\r\n");
    printf("           实践练习开始\r\n");
    printf("========================================\r\n");
    
    practice_i2c_config_demo();
    printf("\r\n");
    
    practice_i2c_timing_demo();
    printf("\r\n");
    
    practice_oled_basic_demo();
    printf("\r\n");
    
    practice_oled_init_demo();
    printf("\r\n");
    
    practice_char_display_demo();
    printf("\r\n");
    
    practice_printf_demo();
    printf("\r\n");
    
    practice_display_optimization_demo();
    printf("\r\n");
    
    practice_i2c_optimization_demo();
    printf("\r\n");
    
    printf("========================================\r\n");
    printf("           实践练习结束\r\n");
    printf("========================================\r\n");
    printf("\r\n");
}
