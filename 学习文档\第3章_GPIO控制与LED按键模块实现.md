# 第3章：GPIO控制与LED按键模块实现

## 🎯 学习目标
- 深入理解GPIO的工作原理和配置方法
- 掌握HAL库GPIO相关API的使用
- 学会LED控制的具体实现技巧
- 理解按键防抖算法的原理和实现
- 掌握上拉下拉电阻的硬件意义

## 📋 目录
1. [GPIO基础概念](#1-gpio基础概念)
2. [GPIO_InitTypeDef结构体详解](#2-gpio_inittypedef结构体详解)
3. [LED控制实现分析](#3-led控制实现分析)
4. [按键检测与防抖算法](#4-按键检测与防抖算法)
5. [硬件电路分析](#5-硬件电路分析)
6. [HAL库GPIO API详解](#6-hal库gpio-api详解)
7. [实践练习](#7-实践练习)

---

## 1. GPIO基础概念

### 1.1 什么是GPIO？
GPIO (General Purpose Input/Output) 通用输入输出端口，是微控制器与外部世界交互的基本接口。

**GPIO的作用：**
- **输出模式**: 控制LED、继电器、蜂鸣器等
- **输入模式**: 读取按键、传感器状态等
- **复用模式**: 作为其他外设的信号线（如UART、SPI等）

### 1.2 STM32F429 GPIO特性
- **端口数量**: GPIOA ~ GPIOK (11个端口)
- **每端口引脚**: 16个引脚 (PIN_0 ~ PIN_15)
- **总引脚数**: 最多176个GPIO引脚
- **驱动能力**: 每个引脚最大25mA输出电流

### 1.3 GPIO工作模式
| 模式 | 说明 | 应用场景 |
|------|------|----------|
| 输入浮空 | 高阻态，不确定电平 | 很少使用 |
| 输入上拉 | 内部上拉电阻，默认高电平 | 按键检测 |
| 输入下拉 | 内部下拉电阻，默认低电平 | 特殊传感器 |
| 推挽输出 | 可输出强高/低电平 | LED控制 |
| 开漏输出 | 只能输出低电平或高阻 | I2C通信 |
| 复用推挽 | 外设控制的推挽输出 | UART、SPI |
| 复用开漏 | 外设控制的开漏输出 | I2C外设 |

---

## 2. GPIO_InitTypeDef结构体详解

### 2.1 结构体定义
```c
typedef struct {
    uint32_t Pin;       // 引脚选择 ⭐
    uint32_t Mode;      // 工作模式 ⭐
    uint32_t Pull;      // 上拉下拉配置 ⭐
    uint32_t Speed;     // 输出速度 ⭐
    uint32_t Alternate; // 复用功能选择
} GPIO_InitTypeDef;
```

### 2.2 成员详细解析

#### 2.2.1 Pin - 引脚选择
```c
GPIO_InitStruct.Pin = GPIO_PIN_8;                    // 选择单个引脚
GPIO_InitStruct.Pin = GPIO_PIN_8 | GPIO_PIN_9;       // 选择多个引脚
GPIO_InitStruct.Pin = GPIO_PIN_All;                  // 选择所有引脚
```

**常用引脚宏定义：**
```c
#define GPIO_PIN_0    ((uint16_t)0x0001)  // 引脚0
#define GPIO_PIN_1    ((uint16_t)0x0002)  // 引脚1
#define GPIO_PIN_8    ((uint16_t)0x0100)  // 引脚8
// ... 其他引脚
```

#### 2.2.2 Mode - 工作模式
```c
// 输入模式
#define GPIO_MODE_INPUT           0x00000000U   // 输入模式
#define GPIO_MODE_OUTPUT_PP       0x00000001U   // 推挽输出
#define GPIO_MODE_OUTPUT_OD       0x00000011U   // 开漏输出
#define GPIO_MODE_AF_PP           0x00000002U   // 复用推挽
#define GPIO_MODE_AF_OD           0x00000012U   // 复用开漏
```

#### 2.2.3 Pull - 上拉下拉配置
```c
#define GPIO_NOPULL               0x00000000U   // 无上拉下拉
#define GPIO_PULLUP               0x00000001U   // 上拉
#define GPIO_PULLDOWN             0x00000002U   // 下拉
```

**硬件意义：**
- **上拉电阻**: 将引脚默认拉到高电平(3.3V)
- **下拉电阻**: 将引脚默认拉到低电平(0V)
- **无上拉下拉**: 引脚处于浮空状态

#### 2.2.4 Speed - 输出速度
```c
#define GPIO_SPEED_FREQ_LOW       0x00000000U   // 低速 (2MHz)
#define GPIO_SPEED_FREQ_MEDIUM    0x00000001U   // 中速 (25MHz)
#define GPIO_SPEED_FREQ_HIGH      0x00000002U   // 高速 (50MHz)
#define GPIO_SPEED_FREQ_VERY_HIGH 0x00000003U   // 超高速 (100MHz)
```

**选择原则：**
- LED控制：低速即可
- 高频信号：选择对应的高速
- 功耗考虑：速度越高功耗越大

---

## 3. LED控制实现分析

### 3.1 硬件连接分析
从代码可以看出，LED连接到GPIOD端口：
```c
// LED硬件连接
LED0 -> GPIOD, GPIO_PIN_8   // PD8
LED1 -> GPIOD, GPIO_PIN_9   // PD9
LED2 -> GPIOD, GPIO_PIN_10  // PD10
LED3 -> GPIOD, GPIO_PIN_11  // PD11
LED4 -> GPIOD, GPIO_PIN_12  // PD12
LED5 -> GPIOD, GPIO_PIN_13  // PD13
```

### 3.2 GPIO配置分析
```c
// LED GPIO配置（来自gpio.c）
GPIO_InitStruct.Pin = GPIO_PIN_8|GPIO_PIN_9|GPIO_PIN_10|GPIO_PIN_11
                     |GPIO_PIN_12|GPIO_PIN_13;
GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;    // 推挽输出模式
GPIO_InitStruct.Pull = GPIO_PULLUP;            // 上拉电阻
GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;   // 低速输出
HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);
```

**配置解析：**
- **推挽输出**: 可以输出强高电平和强低电平
- **上拉电阻**: 确保默认状态为高电平
- **低速输出**: LED控制不需要高频切换

### 3.3 LED控制逻辑分析
```c
uint8_t ucLed[6] = {0,0,0,0,0,0};  // LED状态数组

void led_disp(uint8_t *ucLed)
{
    uint8_t temp = 0x00;
    static uint8_t temp_old = 0xff;  // 记录上次状态
    
    // 将LED数组转换为位图
    for (int i = 0; i < 6; i++) {
        if (ucLed[i]) temp |= (1<<i);  // 位操作设置对应位
    }
    
    // 只有状态改变时才更新GPIO（优化性能）
    if (temp_old != temp) {
        // 逐个设置LED状态
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_8, 
                         (temp & 0x01) ? GPIO_PIN_SET : GPIO_PIN_RESET);
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_9, 
                         (temp & 0x02) ? GPIO_PIN_SET : GPIO_PIN_RESET);
        // ... 其他LED
        
        temp_old = temp;  // 更新状态记录
    }
}
```

**设计亮点：**
1. **状态缓存**: 只有状态改变时才更新GPIO
2. **位操作**: 高效的状态转换
3. **数组控制**: 便于程序控制LED状态

---

## 4. 按键检测与防抖算法

### 4.1 按键硬件连接
```c
// 按键硬件连接（来自btn_app.c）
KEY0 -> GPIOE, GPIO_PIN_15  // PE15
KEY1 -> GPIOE, GPIO_PIN_13  // PE13
KEY2 -> GPIOE, GPIO_PIN_11  // PE11
KEY3 -> GPIOE, GPIO_PIN_9   // PE9
KEY4 -> GPIOE, GPIO_PIN_7   // PE7
KEY5 -> GPIOB, GPIO_PIN_0   // PB0
```

### 4.2 按键GPIO配置
```c
// 按键GPIO配置（来自gpio.c）
GPIO_InitStruct.Pin = GPIO_PIN_7|GPIO_PIN_9|GPIO_PIN_11|GPIO_PIN_13|GPIO_PIN_15;
GPIO_InitStruct.Mode = GPIO_MODE_INPUT;     // 输入模式
GPIO_InitStruct.Pull = GPIO_NOPULL;         // 无内部上拉下拉
HAL_GPIO_Init(GPIOE, &GPIO_InitStruct);
```

**配置说明：**
- **输入模式**: 读取按键状态
- **无内部上拉**: 依赖外部上拉电阻

### 4.3 按键状态读取
```c
uint8_t prv_btn_get_state(struct ebtn_btn *btn)
{
    switch (btn->key_id) {
    case USER_BUTTON_0:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_15);  // 取反逻辑
    case USER_BUTTON_1:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_13);
    // ... 其他按键
    }
}
```

**取反逻辑说明：**
- 按键未按下：引脚为高电平(1)，函数返回0
- 按键按下：引脚为低电平(0)，函数返回1
- 这样设计使得返回值1表示按键按下，逻辑更直观

### 4.4 防抖算法原理

#### 4.4.1 为什么需要防抖？
```
按键物理特性导致的问题：
时间轴: 0ms -----> 5ms -----> 10ms -----> 15ms
实际电平: 高 -----> 抖动 -----> 低 -----> 稳定低
读取结果: 0 -----> 1010 -----> 1 -----> 1

如果没有防抖，会误认为按键被按下多次！
```

#### 4.4.2 防抖参数配置
```c
// 防抖参数配置（来自btn_app.c）
static const ebtn_btn_param_t defaul_ebtn_param = 
    EBTN_PARAMS_INIT(20, 0, 20, 1000, 0, 1000, 10);
//                   ↑   ↑  ↑   ↑    ↑   ↑    ↑
//                   |   |  |   |    |   |    └─ 最大连击次数
//                   |   |  |   |    |   └─ 长按周期(ms)
//                   |   |  |   |    └─ 连击间隔(ms)
//                   |   |  |   └─ 长按检测时间(ms)
//                   |   |  └─ 最小按压时间(ms)
//                   |   └─ 释放防抖时间(ms)
//                   └─ 按下防抖时间(ms)
```

#### 4.4.3 防抖算法流程
```
1. 检测到按键状态变化
2. 启动防抖计时器(20ms)
3. 在防抖时间内，忽略状态变化
4. 防抖时间结束后，确认按键状态
5. 如果状态稳定，触发按键事件
```

---

## 5. 硬件电路分析

### 5.1 LED驱动电路
```
STM32 GPIO -----> LED -----> 限流电阻 -----> VCC
    |
    └─ 推挽输出，可提供足够驱动电流
```

**电路特点：**
- GPIO输出高电平时，LED点亮
- 限流电阻保护LED和GPIO
- 推挽输出提供强驱动能力

### 5.2 按键输入电路
```
VCC -----> 上拉电阻 -----> STM32 GPIO
                |
                └─ 按键 -----> GND
```

**电路特点：**
- 按键未按下：GPIO读取到高电平
- 按键按下：GPIO读取到低电平
- 上拉电阻确保默认状态稳定

### 5.3 上拉下拉电阻的作用

#### 5.3.1 上拉电阻
```c
GPIO_InitStruct.Pull = GPIO_PULLUP;  // 内部上拉
```
**作用：**
- 将引脚默认拉到高电平(3.3V)
- 防止引脚悬空导致的不确定状态
- 适用于按键输入（按下时接地）

#### 5.3.2 下拉电阻
```c
GPIO_InitStruct.Pull = GPIO_PULLDOWN;  // 内部下拉
```
**作用：**
- 将引脚默认拉到低电平(0V)
- 适用于特殊的传感器输入

#### 5.3.3 无上拉下拉
```c
GPIO_InitStruct.Pull = GPIO_NOPULL;  // 无内部上拉下拉
```
**使用场景：**
- 外部已有上拉下拉电阻
- 复用功能模式
- 模拟输入模式

---

## 6. HAL库GPIO API详解

### 6.1 初始化函数
```c
void HAL_GPIO_Init(GPIO_TypeDef *GPIOx, GPIO_InitTypeDef *GPIO_Init);
```
**参数说明：**
- `GPIOx`: GPIO端口 (GPIOA, GPIOB, ...)
- `GPIO_Init`: 初始化结构体指针

**使用示例：**
```c
GPIO_InitTypeDef GPIO_InitStruct = {0};
GPIO_InitStruct.Pin = GPIO_PIN_8;
GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
GPIO_InitStruct.Pull = GPIO_NOPULL;
GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);
```

### 6.2 输出控制函数
```c
// 写入引脚状态
void HAL_GPIO_WritePin(GPIO_TypeDef *GPIOx, uint16_t GPIO_Pin, GPIO_PinState PinState);

// 翻转引脚状态
void HAL_GPIO_TogglePin(GPIO_TypeDef *GPIOx, uint16_t GPIO_Pin);
```

**使用示例：**
```c
// 点亮LED
HAL_GPIO_WritePin(GPIOD, GPIO_PIN_8, GPIO_PIN_SET);

// 熄灭LED
HAL_GPIO_WritePin(GPIOD, GPIO_PIN_8, GPIO_PIN_RESET);

// 翻转LED状态
HAL_GPIO_TogglePin(GPIOD, GPIO_PIN_8);
```

### 6.3 输入读取函数
```c
// 读取引脚状态
GPIO_PinState HAL_GPIO_ReadPin(GPIO_TypeDef *GPIOx, uint16_t GPIO_Pin);
```

**使用示例：**
```c
// 读取按键状态
GPIO_PinState key_state = HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_15);
if (key_state == GPIO_PIN_RESET) {
    // 按键被按下（低电平）
    printf("按键按下\r\n");
}
```

---

## 7. 实践练习

### 练习1：LED流水灯
实现6个LED的流水灯效果。

### 练习2：按键控制LED
使用按键控制对应LED的开关。

### 练习3：防抖算法实现
自己实现一个简单的按键防抖算法。

---

## 📝 本章小结

通过本章学习，您应该掌握：

✅ **GPIO基础概念** - 输入输出模式、推挽开漏、上拉下拉
✅ **GPIO_InitTypeDef结构体** - Pin、Mode、Pull、Speed参数配置
✅ **LED控制技巧** - 状态缓存、位操作、数组管理
✅ **按键防抖原理** - 硬件抖动问题和软件解决方案
✅ **HAL库GPIO API** - 初始化、输出控制、输入读取函数

**下一章预告：** 我们将学习串口通信与环形缓冲区的实现。

---

## 🔗 相关文件
- `sysFunction/led_app.c` - LED控制实现
- `sysFunction/btn_app.c` - 按键处理实现
- `Core/Src/gpio.c` - GPIO底层配置
- `Components/ebtn/` - 按键防抖库
