# 电压采集验证说明

## 🔧 系统配置修改

### ✅ 已完成的修改

#### 1. 数据采集通道切换
**文件**：`sysFunction/adc_app.c` 的 `multi_channel_read_data()`

```c
// 修改前（电流采集模式）
g_multi_channel_data.ch0_raw = 0.0f;                                    // CH0未接入
g_multi_channel_data.ch1_raw = sampling_board_read_channel(SAMPLING_BOARD_CH1); // CH1电流采集
g_multi_channel_data.ch2_raw = 0.0f;                                    // CH2未接入

// 修改后（电压采集模式）
g_multi_channel_data.ch0_raw = sampling_board_read_channel(SAMPLING_BOARD_CH0); // CH0电压采集
g_multi_channel_data.ch1_raw = 0.0f;                                    // CH1未接入
g_multi_channel_data.ch2_raw = 0.0f;                                    // CH2未接入
```

#### 2. 校准函数切换
**文件**：`sysFunction/adc_app.c` 的 `multi_channel_apply_ratios()`

```c
// 修改前（电流校准）
float calibrated_current = current_calibrate_linear(g_multi_channel_data.ch1_raw);
g_multi_channel_data.ch1_processed = calibrated_current * ch1_ratio;

// 修改后（电压校准）
float calibrated_voltage = voltage_calibrate_segmented(g_multi_channel_data.ch0_raw);
g_multi_channel_data.ch0_processed = calibrated_voltage * ch0_ratio;
```

## 📊 硬件通道映射

### 当前配置
- **CH0（AIN0-GND）**：✅ 电压采集通道（活跃）
- **CH1（AIN1-GND）**：❌ 电流采集通道（未接入）
- **CH2（AIN2-GND）**：❌ 电阻采集通道（未接入）

### GD30AD3344配置
- **通道**：AIN0-GND（单端输入）
- **增益**：±6.144V量程
- **采样方式**：单通道固定采集

## 🎯 电压校准算法

### 您的分段校准函数
系统使用您在`sampling_board_app.c`中实现的`voltage_calibrate_segmented()`函数：

#### 精细校准表（0.00V-0.14V）
```c
const calibration_point_t fine_calibration_table[FINE_CALIBRATION_POINTS] = {
    {0.00f, 0.0360f}, {0.01f, 0.0377f}, {0.02f, 0.0407f}, {0.03f, 0.0435f}, {0.04f, 0.0467f},
    {0.05f, 0.0487f}, {0.06f, 0.0527f}, {0.07f, 0.0555f}, {0.08f, 0.0585f}, {0.09f, 0.0615f},
    {0.10f, 0.0643f}, {0.11f, 0.0673f}, {0.12f, 0.0703f}, {0.13f, 0.0733f}, {0.14f, 0.0763f}
};
```

#### 粗糙校准表（0.0V-10.0V）
```c
const calibration_point_t coarse_calibration_table[COARSE_CALIBRATION_POINTS] = {
    {0.0f, 0.0384f}, {0.1f, 0.0643f}, {0.2f, 0.0939f}, {0.3f, 0.1236f}, {0.4f, 0.1534f},
    // ... 完整的101个校准点，覆盖0.0V-10.0V范围
};
```

### 智能校准选择
- **精细校准**：原始值在0.0360V-0.0763V范围内时使用
- **粗糙校准**：其他范围使用粗糙校准表
- **线性插值**：在校准点之间进行精确插值

## 📋 测试验证步骤

### 1. 硬件连接
- **断开电流源**：从AIN1-GND断开
- **连接电压源**：连接到AIN0-GND
- **电压范围**：建议测试0.1V-5.0V范围

### 2. 基础功能测试
```
command:get_data
```
**预期输出**：
```
report:ch0=[校准后的电压值],ch1=0.00,ch2=0.00
```

### 3. 连续采样测试
```
command:start_sample
```
**预期输出**：
```
report:2025-06-15 HH:MM:SS ch0=[电压值],ch1=0.00,ch2=0.00
```

### 4. 变比功能测试
```
command:set_ratio:ch0=2.0,ch1=1.0,ch2=1.0
command:get_data
```
**预期输出**：
```
report:ch0=[电压值×2],ch1=0.00,ch2=0.00
```

### 5. 二进制协议测试
```
command:000221000801E7B5
```
**预期输出**：
```
report:020001180001[二进制电压数据]
```

## ✅ 验证要点

### 1. 校准精度验证
- **低电压范围（0.1V-0.14V）**：使用精细校准表，精度更高
- **中高电压范围（0.15V-10V）**：使用粗糙校准表
- **线性插值**：校准点之间的值应该平滑过渡

### 2. 数据格式验证
- **CH0显示电压值**：经过校准的真实电压值（V）
- **CH1和CH2显示0.00**：表示未接入状态
- **变比计算正确**：设置变比后数值相应变化

### 3. 系统稳定性验证
- **数据稳定**：连续采样数据应该稳定
- **响应及时**：命令响应时间正常
- **格式正确**：所有输出符合测评要求

## 🔧 可能需要的调整

### 如果电压校准不准确
可能需要：
1. **更新校准表**：根据实际测量结果调整校准点
2. **检查硬件连接**：确认AIN0-GND连接正确
3. **验证参考电压**：确认GD30AD3344的参考电压稳定

### 如果需要切换回电流模式
只需要将采集通道改回：
```c
g_multi_channel_data.ch0_raw = 0.0f;
g_multi_channel_data.ch1_raw = sampling_board_read_channel(SAMPLING_BOARD_CH1);
g_multi_channel_data.ch2_raw = 0.0f;
```

## 🚀 测试准备完成

### 编译结果
- **状态**：✅ 编译成功
- **固件大小**：1,108,952字节
- **配置**：电压采集模式（CH0活跃）

### 系统特点
- **使用您的电压校准算法**：分段校准，精度优化
- **保持接口兼容**：所有测评命令正常工作
- **输出格式标准**：符合测评要求

**🎯 现在请连接电压源到AIN0-GND，然后测试电压采集功能！**

### 预期测试结果
```
command:get_data
report:ch0=1.23,ch1=0.00,ch2=0.00  // CH0显示校准后的电压值
```
