#ifndef __BINARY_PROTOCOL_H__
#define __BINARY_PROTOCOL_H__

#include "main.h"
#include <stdint.h>
#include <string.h>
#include <stdlib.h>

// 二进制协议相关定义
#define BINARY_PROTOCOL_VERSION     0x01        // 协议版本
#define BINARY_PROTOCOL_MIN_LENGTH  8           // 最小报文长度
#define BINARY_PROTOCOL_MAX_LENGTH  32          // 最大报文长度
#define BINARY_PROTOCOL_PAYLOAD_MAX 16          // 最大负载长度

// 设备ID定义
#define DEVICE_ID_BROADCAST         0xFFFF      // 广播设备ID

// 消息类型定义
typedef enum {
    MSG_TYPE_SET_DEVICE_ID = 0x01,      // 设置设备ID
    MSG_TYPE_GET_DEVICE_ID = 0x02,      // 获取设备ID
    MSG_TYPE_RESPONSE = 0x02,           // 应答消息
    MSG_TYPE_DATA = 0x01,               // 数据消息
    MSG_TYPE_SINGLE_READ = 0x21,        // 单次读取
    MSG_TYPE_CONTINUOUS_READ = 0x22,    // 连续读取
    MSG_TYPE_STOP_READ = 0x2F           // 停止读取
} msg_type_t;

// 二进制协议结构体
typedef struct binary_protocol_t {
    uint16_t device_id;         // 设备ID (2字节)
    uint8_t msg_type;           // 消息类型 (1字节)
    uint16_t msg_length;        // 消息长度 (2字节)
    uint8_t protocol_ver;       // 协议版本 (1字节)
    uint8_t payload[BINARY_PROTOCOL_PAYLOAD_MAX]; // 负载数据 (最大16字节)
    uint16_t crc;              // CRC校验 (2字节)
} binary_protocol_t;

// 协议解析结果
typedef enum {
    PROTOCOL_OK = 0,
    PROTOCOL_ERROR_INVALID_LENGTH,
    PROTOCOL_ERROR_INVALID_FORMAT,
    PROTOCOL_ERROR_CRC_MISMATCH,
    PROTOCOL_ERROR_INVALID_DEVICE_ID,
    PROTOCOL_ERROR_UNSUPPORTED_MSG_TYPE,
    PROTOCOL_ERROR_BUFFER_TOO_SMALL
} protocol_result_t;

// IEEE 754浮点数联合体
typedef union {
    float f;
    uint32_t i;
    struct {
        uint32_t mantissa : 23;
        uint32_t exponent : 8;
        uint32_t sign : 1;
    } parts;
} ieee754_float_t;

// CRC校验函数
uint16_t crc16_calculate(const uint8_t *data, size_t length);
uint16_t crc16_calculate_exam(const uint8_t *data, size_t length);
uint16_t crc16_calculate_set_device_id(const uint8_t *data, size_t length);
uint16_t crc16_calculate_with_table(const uint8_t *data, size_t length);
void crc16_init_table(void);

// 字节序转换函数
uint16_t uint16_to_little_endian(uint16_t value);
uint16_t little_endian_to_uint16(uint16_t value);
uint32_t uint32_to_little_endian(uint32_t value);
uint32_t little_endian_to_uint32(uint32_t value);

// IEEE 754浮点数处理函数
uint32_t ieee754_encode(float value);
float ieee754_decode(uint32_t encoded);
void ieee754_to_bytes(uint32_t encoded, uint8_t *bytes);
uint32_t bytes_to_ieee754(const uint8_t *bytes);

// 十六进制字符串转换函数
size_t hex_string_to_bytes(const char *hex_str, uint8_t *bytes, size_t max_bytes);
size_t bytes_to_hex_string(const uint8_t *bytes, size_t length, char *hex_str, size_t max_str_len);

// 二进制协议解析和生成函数
protocol_result_t binary_protocol_parse(const char *hex_string, binary_protocol_t *protocol);
protocol_result_t binary_protocol_generate(const binary_protocol_t *protocol, char *hex_string, size_t max_str_len);
protocol_result_t binary_protocol_validate(const binary_protocol_t *protocol);

// 协议数据打包和解包函数
void protocol_pack_timestamp_and_channels(uint8_t *payload, uint32_t timestamp, float ch0, float ch1, float ch2);
void protocol_unpack_device_id(const uint8_t *payload, uint16_t *device_id);
void protocol_pack_response(uint8_t *payload, uint16_t response_code);

// 协议处理辅助函数
uint8_t is_device_id_match(uint16_t target_id, uint16_t current_id);
const char* get_protocol_error_string(protocol_result_t result);

#endif /* __BINARY_PROTOCOL_H__ */
