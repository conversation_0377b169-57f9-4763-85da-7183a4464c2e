# 2.7 串口通信模块程序设计

## 必要性
满足竞赛要求的命令解析、参数配置、系统调试等人机交互需求。实现完整的调试命令体系，支持系统自检、配置管理、采样控制、数据加密等核心功能的串口控制。

## 核心设计
串口通信模块主要由`usart_app.h`和`usart_app.c`实现，负责系统的人机交互和调试控制。采用DMA+环形缓冲区的高效数据传输机制，结合状态机的智能命令解析系统。

## 核心功能
1. **DMA环形缓冲区通信**  
2. **智能命令解析系统**  
3. **状态机参数输入**  
4. **格式化输出控制**  
5. **竞赛命令支持**  
6. **系统调试接口**

## 通信类型
- **命令数据**：系统控制和配置命令
- **参数数据**：时间、变比、阈值等参数输入
- **状态数据**：系统状态查询和显示
- **调试数据**：错误信息和调试输出

## 主要函数

### 基础通信函数
- `my_printf()`：格式化串口输出，支持可变参数
- `uart_task()`：串口任务处理，环形缓冲区数据读取
- `HAL_UARTEx_RxEventCallback()`：DMA接收完成回调
- `parse_uart_command()`：命令解析和状态机处理

### 命令处理函数
- `handle_test_cmd()`：系统自检命令
- `handle_rtc_config_cmd()`：RTC时间配置
- `handle_rtc_now_cmd()`：当前时间显示
- `handle_conf_cmd()`：配置文件读取
- `handle_ratio_cmd()`：变比参数设置
- `handle_limit_cmd()`：阈值参数设置
- `handle_start_cmd()`：开始采样命令
- `handle_stop_cmd()`：停止采样命令
- `handle_hide_cmd()`：启用数据加密
- `handle_unhide_cmd()`：禁用数据加密

### 系统管理函数
- `handle_system_status_cmd()`：系统状态显示
- `handle_debug_state_cmd()`：详细调试状态
- `handle_reset_boot_cmd()`：重置启动次数
- `handle_recover_sd_cmd()`：SD卡故障恢复
- `handle_check_state_cmd()`：状态一致性检查

## 技术特点

### DMA环形缓冲区机制
```c
// DMA接收完成事件处理
void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
    if (huart->Instance == USART1) {
        HAL_UART_DMAStop(huart);                    // 停止当前DMA传输
        rt_ringbuffer_put(&uart_ringbuffer, uart_rx_dma_buffer, Size);  // 数据存入环形缓冲区
        memset(uart_rx_dma_buffer, 0, sizeof(uart_rx_dma_buffer));      // 清空DMA缓冲区
        HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));  // 重启DMA接收
        __HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);  // 禁用半传输中断
    }
}
```

### 智能命令解析系统
```c
// 命令表驱动的解析机制
static const cmd_entry_t cmd_table[] = {
    {"test", CMD_TEST, handle_test_cmd},                    // 完整系统自检
    {"test simple", CMD_TEST_SIMPLE, handle_test_simple_cmd},  // 简化自检
    {"RTC Config", CMD_RTC_CONFIG, handle_rtc_config_cmd},     // RTC时间设置
    {"RTC now", CMD_RTC_NOW, handle_rtc_now_cmd},             // 显示当前时间
    {"conf", CMD_CONF, handle_conf_cmd},                      // 配置文件读取
    {"ratio", CMD_RATIO, handle_ratio_cmd},                   // 变比设置
    {"limit", CMD_LIMIT, handle_limit_cmd},                   // 阈值设置
    {"start", CMD_START, handle_start_cmd},                   // 开始采样
    {"stop", CMD_STOP, handle_stop_cmd},                      // 停止采样
    {"hide", CMD_HIDE, handle_hide_cmd},                      // 启用加密
    {"unhide", CMD_UNHIDE, handle_unhide_cmd},                // 禁用加密
    {NULL, CMD_NONE, NULL}  // 结束标记
};
```

### 状态机参数输入
```c
// 多状态输入处理
typedef enum {
    UART_STATE_NORMAL,      // 正常命令模式
    UART_STATE_RTC_CONFIG,  // 等待时间输入
    UART_STATE_RATIO_INPUT, // 等待变比输入
    UART_STATE_LIMIT_INPUT  // 等待阈值输入
} uart_state_t;

// 状态机处理逻辑
if (uart_state == UART_STATE_RTC_CONFIG) {
    if (rtc_set_time_from_string(cmd_str) == HAL_OK) {
        my_printf(&huart1, "RTC Config success\r\n");
        sd_write_log_data("rtc config");
    } else {
        my_printf(&huart1, "RTC Config failed\r\n");
    }
    uart_state = UART_STATE_NORMAL;  // 重置为正常模式
}
```

### 格式化输出控制
```c
// 可变参数格式化输出
int my_printf(UART_HandleTypeDef *huart, const char *format, ...)
{
    char buffer[512];
    va_list arg;
    int len;
    va_start(arg, format);
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);
    HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF);
    return len;
}
```

## 竞赛命令支持

### RTC时间管理
- **RTC Config**：时间设置命令，格式"2025-01-01 12:00:30"
- **RTC now**：显示当前时间，输出"Current Time: 2025-01-01 12:00:30"

### 配置参数管理
- **conf**：读取config.ini文件，显示变比和阈值参数
- **ratio**：设置变比参数，支持直接设置和交互模式
- **limit**：设置阈值参数，支持参数验证和范围检查
- **config save**：保存参数到Flash和SD卡
- **config read**：从Flash读取已保存的参数

### 采样控制管理
- **start**：开始周期性采样，显示采样周期
- **stop**：停止采样，记录操作日志
- **hide**：启用数据加密，数据写入hideData目录
- **unhide**：禁用数据加密，恢复正常数据格式

### 系统调试管理
- **test**：完整系统自检，检测Flash、SD卡、RTC
- **system status**：显示系统运行状态和配置信息
- **debug state**：详细系统状态，用于问题定位
- **recover sd**：SD卡故障自动恢复
- **reset boot**：重置启动次数，清除Flash缓存

## 数据流控制

### 接收数据流
```
USART1 → DMA缓冲区 → 环形缓冲区 → 命令解析 → 功能执行
```

### 发送数据流
```
格式化数据 → 发送缓冲区 → USART1发送 → 串口输出
```

## 错误处理机制

### 命令解析错误
- 未知命令：输出"Error: Unknown command"
- 参数错误：显示有效范围和当前值
- 格式错误：提供正确格式示例

### 通信错误处理
- DMA传输错误：自动重启DMA接收
- 缓冲区溢出：环形缓冲区自动覆盖
- 超时处理：状态机自动重置为正常模式

## 性能优化

### 高效数据传输
- DMA零拷贝传输，减少CPU占用
- 环形缓冲区避免数据丢失
- 中断驱动处理，实时响应

### 内存管理优化
- 静态缓冲区分配，避免内存碎片
- 命令表常量存储，节省RAM空间
- 字符串处理优化，减少栈使用

通过这套完整的串口通信模块，系统实现了高效可靠的人机交互接口，满足竞赛要求的所有命令功能，为系统调试和参数配置提供了便捷的操作方式。
