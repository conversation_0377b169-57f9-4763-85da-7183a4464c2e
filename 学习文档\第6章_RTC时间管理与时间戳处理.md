# 第6章：RTC时间管理与时间戳处理

## 🎯 学习目标
- 深入理解RTC实时时钟的工作原理
- 掌握RTC_HandleTypeDef结构体配置
- 理解LSE/LSI时钟源的选择和影响
- 学会Unix时间戳的转换算法
- 掌握时间格式化和显示功能

## 📋 目录
1. [RTC基础概念](#1-rtc基础概念)
2. [RTC_HandleTypeDef结构体详解](#2-rtc_handletypedef结构体详解)
3. [时钟源配置分析](#3-时钟源配置分析)
4. [时间获取与设置](#4-时间获取与设置)
5. [Unix时间戳转换算法](#5-unix时间戳转换算法)
6. [时间格式化处理](#6-时间格式化处理)
7. [备份域管理](#7-备份域管理)
8. [实践练习](#8-实践练习)

---

## 1. RTC基础概念

### 1.1 什么是RTC？
RTC (Real-Time Clock) 实时时钟，是一个独立的时钟模块，即使主电源断开也能保持时间运行。

**RTC特点：**
- **独立供电**: 可使用备用电池供电
- **低功耗**: 微安级功耗，适合长期运行
- **高精度**: 配合外部晶振可达±20ppm精度
- **掉电保持**: 主电源断开时间不丢失

### 1.2 STM32F429 RTC特性
- **时钟源**: LSE(32.768kHz)、LSI(32kHz)、HSE/32
- **时间格式**: 24小时制或12小时制
- **日历功能**: 年、月、日、星期
- **闹钟功能**: 2个独立闹钟
- **备份寄存器**: 20个32位备份寄存器

### 1.3 RTC应用场景
```
数据采集系统中的RTC应用：
┌─────────────────────────────────────────────────────────┐
│ 数据记录 → 时间戳 → 文件命名 → 定时采样 → 日志管理 │
│    ↓         ↓         ↓         ↓         ↓      │
│  ADC数据   Unix戳   按日期分类  周期控制   事件记录  │
└─────────────────────────────────────────────────────────┘
```

---

## 2. RTC_HandleTypeDef结构体详解

### 2.1 结构体定义分析
```c
typedef struct {
    RTC_TypeDef *Instance;          // RTC外设基地址 ⭐
    RTC_InitTypeDef Init;           // 初始化参数 ⭐
    HAL_LockTypeDef Lock;           // 锁定状态
    HAL_RTCStateTypeDef State;      // RTC状态
} RTC_HandleTypeDef;
```

### 2.2 项目中的RTC配置
```c
// 来自rtc.c的配置
RTC_HandleTypeDef hrtc;

void MX_RTC_Init(void)
{
    hrtc.Instance = RTC;                            // RTC外设
    hrtc.Init.HourFormat = RTC_HOURFORMAT_24;       // 24小时制
    hrtc.Init.AsynchPrediv = 127;                   // 异步预分频器
    hrtc.Init.SynchPrediv = 255;                    // 同步预分频器
    hrtc.Init.OutPut = RTC_OUTPUT_DISABLE;          // 禁用输出
    hrtc.Init.OutPutPolarity = RTC_OUTPUT_POLARITY_HIGH;  // 输出极性
    hrtc.Init.OutPutType = RTC_OUTPUT_TYPE_OPENDRAIN;     // 开漏输出
    
    HAL_RTC_Init(&hrtc);
}
```

### 2.3 配置参数详解

#### 2.3.1 时间格式
```c
hrtc.Init.HourFormat = RTC_HOURFORMAT_24;  // 24小时制
```
**格式选择：**
- RTC_HOURFORMAT_24：24小时制 (0-23)
- RTC_HOURFORMAT_12：12小时制 (1-12 + AM/PM)

#### 2.3.2 预分频器配置
```c
hrtc.Init.AsynchPrediv = 127;    // 异步预分频器
hrtc.Init.SynchPrediv = 255;     // 同步预分频器
```

**分频计算：**
```
RTC时钟 = 时钟源 / ((AsynchPrediv + 1) × (SynchPrediv + 1))
        = 32768 / ((127 + 1) × (255 + 1))
        = 32768 / (128 × 256)
        = 32768 / 32768
        = 1 Hz
```

---

## 3. 时钟源配置分析

### 3.1 时钟源选择
| 时钟源 | 频率 | 精度 | 功耗 | 应用场景 |
|--------|------|------|------|----------|
| LSE | 32.768kHz | ±20ppm | 极低 | 高精度应用 ⭐ |
| LSI | ~32kHz | ±5% | 低 | 一般应用 |
| HSE/32 | 变化 | 高 | 高 | 特殊需求 |

### 3.2 LSE时钟源优势
```c
// LSE配置（在system_stm32f4xx.c中）
RCC_OscInitStruct.LSEState = RCC_LSE_ON;  // 使能LSE
```

**LSE优势：**
- ✅ 标准32.768kHz晶振，精度高
- ✅ 功耗极低，适合电池供电
- ✅ 温度稳定性好
- ✅ 长期运行可靠

### 3.3 时钟源切换
```c
// 时钟源选择
__HAL_RCC_RTC_CLKPRESCALER(RCC_RTCCLKSOURCE_LSE);  // 选择LSE
__HAL_RCC_RTC_ENABLE();  // 使能RTC时钟
```

---

## 4. 时间获取与设置

### 4.1 时间数据结构
```c
// 时间结构体
typedef struct {
    uint8_t Hours;          // 小时 (0-23)
    uint8_t Minutes;        // 分钟 (0-59)
    uint8_t Seconds;        // 秒 (0-59)
    uint8_t DayLightSaving; // 夏令时
    uint8_t StoreOperation; // 存储操作
} RTC_TimeTypeDef;

// 日期结构体
typedef struct {
    uint8_t WeekDay;        // 星期 (1-7)
    uint8_t Month;          // 月份 (1-12)
    uint8_t Date;           // 日期 (1-31)
    uint8_t Year;           // 年份 (0-99, 表示2000-2099)
} RTC_DateTypeDef;
```

### 4.2 时间获取函数
```c
// 项目中的时间获取封装
void rtc_get_time_info(RTC_TimeTypeDef *current_time, RTC_DateTypeDef *current_date)
{
    if (current_time != NULL && current_date != NULL) {
        HAL_RTC_GetTime(&hrtc, current_time, RTC_FORMAT_BIN);  // 先读时间
        HAL_RTC_GetDate(&hrtc, current_date, RTC_FORMAT_BIN);  // 再读日期
    }
}
```

**重要注意事项：**
- ⚠️ 必须先调用HAL_RTC_GetTime()再调用HAL_RTC_GetDate()
- ⚠️ 这是STM32 HAL库的要求，顺序不能颠倒

### 4.3 时间设置函数
```c
// 从字符串设置RTC时间
HAL_StatusTypeDef rtc_set_time_from_string(const char *time_str)
{
    RTC_TimeTypeDef sTime = {0};
    RTC_DateTypeDef sDate = {0};

    // 解析时间字符串 "2025-01-01 12:00:30"
    if (parse_time_string(time_str, &sTime, &sDate) != HAL_OK) {
        return HAL_ERROR;
    }

    // 设置RTC日期（必须先设置日期再设置时间）
    if (HAL_RTC_SetDate(&hrtc, &sDate, RTC_FORMAT_BIN) != HAL_OK) {
        return HAL_ERROR;
    }

    // 设置RTC时间
    if (HAL_RTC_SetTime(&hrtc, &sTime, RTC_FORMAT_BIN) != HAL_OK) {
        return HAL_ERROR;
    }

    HAL_Delay(10);  // 等待寄存器同步
    return HAL_OK;
}
```

### 4.4 字符串解析算法
```c
static HAL_StatusTypeDef parse_time_string(const char *time_str, 
                                          RTC_TimeTypeDef *sTime, 
                                          RTC_DateTypeDef *sDate)
{
    int year, month, day, hour, minute, second;
    int parsed = 0;

    // 尝试标准格式: "2025-01-01 12:00:30"
    parsed = sscanf(time_str, "%d-%d-%d %d:%d:%d", 
                   &year, &month, &day, &hour, &minute, &second);

    // 如果标准格式解析失败，尝试紧凑格式: "2025-01-01 01-30-10"
    if (parsed != 6) {
        parsed = sscanf(time_str, "%d-%d-%d %d-%d-%d", 
                       &year, &month, &day, &hour, &minute, &second);
    }

    // 参数验证
    if (parsed != 6 || year < 2000 || year > 2099 || 
        month < 1 || month > 12 || day < 1 || day > 31 ||
        hour > 23 || minute > 59 || second > 59) {
        return HAL_ERROR;
    }

    // 设置结构体
    sTime->Hours = hour;
    sTime->Minutes = minute;
    sTime->Seconds = second;
    
    sDate->Year = year - 2000;  // 转换为2位年份
    sDate->Month = month;
    sDate->Date = day;
    sDate->WeekDay = RTC_WEEKDAY_MONDAY;  // 默认周一

    return HAL_OK;
}
```

---

## 5. Unix时间戳转换算法

### 5.1 Unix时间戳概念
Unix时间戳是从1970年1月1日00:00:00 UTC开始的秒数。

**应用场景：**
- 数据记录的时间标记
- 文件命名和分类
- 时间比较和计算
- 跨平台时间交换

### 5.2 转换算法实现
```c
uint32_t rtc_convert_to_unix_timestamp(const RTC_TimeTypeDef *sTime, 
                                      const RTC_DateTypeDef *sDate)
{
    int year = sDate->Year + 2000;  // 转换为4位年份
    int month = sDate->Month;
    int day = sDate->Date;
    int hour = sTime->Hours;
    int minute = sTime->Minutes;
    int second = sTime->Seconds;

    // 计算从1970年到指定年份的天数
    uint32_t days = 0;

    // 计算年份贡献的天数
    for (int y = 1970; y < year; y++) {
        if ((y % 4 == 0 && y % 100 != 0) || (y % 400 == 0)) {
            days += 366;  // 闰年
        } else {
            days += 365;  // 平年
        }
    }

    // 每月天数表（平年）
    static const int days_in_month[] = {31, 28, 31, 30, 31, 30, 
                                       31, 31, 30, 31, 30, 31};

    // 计算月份贡献的天数
    for (int m = 1; m < month; m++) {
        days += days_in_month[m - 1];
        // 如果是闰年且已经过了2月，需要加1天
        if (m == 2 && ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0))) {
            days += 1;
        }
    }

    // 加上当月的天数（减1因为当天还没过完）
    days += (day - 1);

    // 转换为秒
    uint32_t timestamp = days * 24 * 3600 + hour * 3600 + minute * 60 + second;

    // 时区修正：减去8小时（28800秒）
    timestamp -= 28800;

    return timestamp;
}
```

### 5.3 闰年判断算法
```c
// 闰年判断规则
bool is_leap_year(int year) {
    return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
}

// 闰年规则：
// 1. 能被4整除且不能被100整除的年份是闰年
// 2. 能被400整除的年份是闰年
// 例如：2000年是闰年，1900年不是闰年，2024年是闰年
```

### 5.4 时区处理
```c
// 项目中的时区修正
timestamp -= 28800;  // 减去8小时（东八区）

// 原因：赛事方解码程序会增加8小时
// 所以我们预先减去8小时，解码后就是正确的本地时间
```

---

## 6. 时间格式化处理

### 6.1 标准格式化函数
```c
void rtc_format_current_time_string(char *buffer, size_t buffer_size)
{
    RTC_TimeTypeDef current_time = {0};
    RTC_DateTypeDef current_date = {0};

    // 获取当前时间和日期
    HAL_RTC_GetTime(&hrtc, &current_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_date, RTC_FORMAT_BIN);

    // 格式化为标准格式："YYYY-MM-DD HH:MM:SS"
    snprintf(buffer, buffer_size, "%04d-%02d-%02d %02d:%02d:%02d",
             current_date.Year + 2000,  // 转换为4位年份
             current_date.Month,
             current_date.Date,
             current_time.Hours,
             current_time.Minutes,
             current_time.Seconds);
}
```

### 6.2 多种格式支持
```c
// 不同的时间格式示例
void format_time_examples(void) {
    RTC_TimeTypeDef time = {14, 30, 25, 0, 0};  // 14:30:25
    RTC_DateTypeDef date = {1, 1, 15, 25};      // 2025-01-15 周一

    char buffer[64];

    // 格式1：标准格式
    snprintf(buffer, sizeof(buffer), "%04d-%02d-%02d %02d:%02d:%02d",
             date.Year + 2000, date.Month, date.Date,
             time.Hours, time.Minutes, time.Seconds);
    // 输出：2025-01-15 14:30:25

    // 格式2：紧凑格式
    snprintf(buffer, sizeof(buffer), "%04d%02d%02d_%02d%02d%02d",
             date.Year + 2000, date.Month, date.Date,
             time.Hours, time.Minutes, time.Seconds);
    // 输出：20250115_143025

    // 格式3：文件名格式
    snprintf(buffer, sizeof(buffer), "%04d-%02d-%02d_%02dh%02dm",
             date.Year + 2000, date.Month, date.Date,
             time.Hours, time.Minutes);
    // 输出：2025-01-15_14h30m
}
```

---

## 7. 备份域管理

### 7.1 备份域概念
备份域是STM32中的特殊区域，即使主电源断开也能保持数据。

**备份域包含：**
- RTC寄存器
- 备份寄存器 (20个32位)
- LSE振荡器

### 7.2 备份寄存器使用
```c
// 项目中的备份域检查
uint32_t backup_reg = HAL_RTCEx_BKUPRead(&hrtc, RTC_BKP_DR0);

if (backup_reg != 0x32F2) {
    // 备份域未初始化，设置默认时间
    // 设置时间...
    
    // 标记已初始化
    HAL_RTCEx_BKUPWrite(&hrtc, RTC_BKP_DR0, 0x32F2);
}
```

### 7.3 时间有效性检查
```c
// 检查时间是否合理
uint8_t time_is_valid = 0;
if (current_date.Year >= 24 && current_date.Year <= 30) {  // 2024-2030
    // 如果不是默认的2025-01-01 00:00:xx，认为时间有效
    if (!(current_date.Year == 25 && current_date.Month == 1 && 
          current_date.Date == 1 && current_time.Hours == 0 && 
          current_time.Minutes == 0)) {
        time_is_valid = 1;
    }
}
```

---

## 8. 实践练习

### 练习1：RTC基本配置
配置RTC并设置初始时间。

### 练习2：时间戳转换
实现Unix时间戳的转换和验证。

### 练习3：时间格式化
实现多种时间格式的输出。

---

## 📝 本章小结

通过本章学习，您应该掌握：

✅ **RTC基础概念** - 实时时钟原理、时钟源选择、精度影响
✅ **RTC_HandleTypeDef配置** - 24小时制、预分频器、时钟源设置
✅ **时间获取设置** - HAL库API使用、字符串解析、参数验证
✅ **Unix时间戳转换** - 算法实现、闰年处理、时区修正
✅ **时间格式化** - 多种格式支持、标准输出、文件命名

**下一章预告：** 我们将学习ADC数据采集与滤波算法的实现。

---

## 🔗 相关文件
- `sysFunction/rtc_app.c` - RTC时间管理实现
- `sysFunction/rtc_app.h` - RTC应用层头文件
- `Core/Src/rtc.c` - RTC底层配置
