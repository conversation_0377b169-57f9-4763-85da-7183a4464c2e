# GD30AD3344采样板移植总结

## 移植完成情况

✅ **已完成的移植工作**

### 1. 驱动层移植
- **gd30ad3344.h** - 头文件定义，包含所有必要的宏定义和函数声明
- **gd30ad3344.c** - 驱动实现，将GD32标准库API转换为STM32 HAL库API

### 2. 应用层集成
- **sampling_board_app.h** - 采样板应用层头文件
- **sampling_board_app.c** - 采样板应用层实现，包含多通道采样和数据处理

### 3. 系统集成
- **调度器集成** - 在scheduler.c中添加采样板任务和初始化
- **串口命令** - 在usart_app.c中添加sb系列命令支持
- **头文件管理** - 在mydefine.h中添加必要的包含

## 主要移植变更

### 1. DMA操作移植
**原GD32代码:**
```c
dma_single_data_mode_init(DMA_GD30AD3344, DMA_GD30_CHANNEL_TX, &dma_init_struct);
dma_channel_enable(DMA_GD30AD3344, DMA_GD30_CHANNEL_TX);
spi_dma_enable(SPI_GD30AD3344, SPI_DMA_TRANSMIT);
```

**移植后STM32代码:**
```c
HAL_SPI_TransmitReceive_DMA(&hspi1, gd30_send_array, gd30_receive_array, size);
while (HAL_SPI_GetState(&hspi1) != HAL_SPI_STATE_READY);
```

### 2. 配置结构移植
保持了原有的配置结构体定义，确保兼容性：
```c
typedef struct {
    uint16_t SS         : 1;
    uint16_t MUX        : 3;
    uint16_t PGA        : 3;
    uint16_t MODE       : 1;
    uint16_t DR         : 3;
    uint16_t RESERVED_1 : 1;
    uint16_t PULL_UP_EN : 1;
    uint16_t NOP        : 2;
    uint16_t RESERVED   : 1;
} GD30AD3344;
```

### 3. 硬件抽象层
**CS控制:**
```c
#define SPI_GD30AD3344_CS_LOW()  HAL_GPIO_WritePin(GPIOA, GPIO_PIN_4, GPIO_PIN_RESET)
#define SPI_GD30AD3344_CS_HIGH() HAL_GPIO_WritePin(GPIOA, GPIO_PIN_4, GPIO_PIN_SET)
```

## 新增功能

### 1. 多通道管理
- 4通道轮询采集
- 独立滤波处理
- 数据有效性检查

### 2. 测量模式
- 电压测量模式
- 电流测量模式
- 电阻测量模式

### 3. 串口命令系统
- `sb start` - 开始采样
- `sb stop` - 停止采样
- `sb mode <0|1|2>` - 设置测量模式
- `sb read` - 读取当前数据

### 4. 数据处理
- 8点滑动平均滤波
- 电流计算（基于分流电阻）
- 电阻计算（基于分压原理）

## 配置要求

### 1. 硬件配置
- SPI1已配置为主模式，8位数据，DMA支持
- PA4配置为输出模式作为CS信号
- DMA2_Stream3(TX)和DMA2_Stream0(RX)已配置

### 2. 软件配置
- 任务调度器中添加50ms周期的采样板任务
- 串口命令解析器中添加sb系列命令
- 系统初始化中添加采样板初始化

## 使用方法

### 1. 基本使用
```
sb start          # 开始采样
sb mode 0         # 设置为电压测量模式
sb read           # 读取当前数据
sb stop           # 停止采样
```

### 2. 数据输出示例
```
2025-01-15 10:30:25 CH0=1.234V CH1=2.345V CH2=3.456V CH3=0.789V
```

## 技术特点

### 1. 兼容性
- 保持了原有的API接口
- 配置参数完全兼容
- 数据格式保持一致

### 2. 可靠性
- HAL库标准API，稳定可靠
- DMA传输，CPU占用率低
- 错误处理和状态检查

### 3. 扩展性
- 模块化设计，易于扩展
- 支持多种测量模式
- 可配置的滤波参数

## 注意事项

### 1. 硬件连接
- 确保SPI信号线连接正确
- CS信号(PA4)必须连接到GD30AD3344的CS引脚
- 电源和地线连接稳定

### 2. 软件配置
- SPI1的DMA配置不能修改
- 任务调度周期影响采样率
- 滤波参数可根据需要调整

### 3. 调试建议
- 使用示波器检查SPI时序
- 监控串口输出的原始数据
- 检查CS信号的时序关系

## 移植验证

建议进行以下验证测试：

1. **基本通信测试**
   ```
   sb read  # 检查是否能读取数据
   ```

2. **模式切换测试**
   ```
   sb mode 0  # 电压模式
   sb mode 1  # 电流模式
   sb mode 2  # 电阻模式
   ```

3. **连续采样测试**
   ```
   sb start  # 开始连续采样
   # 观察数据输出
   sb stop   # 停止采样
   ```

4. **数据一致性测试**
   - 输入已知电压，检查读数准确性
   - 多次读取，检查数据稳定性
   - 不同通道输入，检查通道独立性

## 总结

本次移植成功将GD32标准库的GD30AD3344驱动移植到STM32 HAL库环境，保持了原有功能的同时增加了更多实用特性。移植后的代码结构清晰，功能完整，可以直接投入使用。
