# 连续采样功能使用指南

## 功能概述
系统现在支持真正的连续自动数据采集和发送功能。启动连续采样后，系统会按设定的时间间隔自动发送采集数据，无需手动发送命令。

## 核心功能

### 1. 连续采样控制
- **启动**: `command:start_sample` - 开始连续采样
- **停止**: `command:stop_sample` - 停止连续采样
- **间隔设置**: `command:set_interval=X` - 设置采样间隔(X秒)

### 2. 自动数据发送
启动连续采样后，系统会：
- ✅ 按设定间隔自动采集3通道数据
- ✅ 自动应用变比计算
- ✅ 自动检查超限状态
- ✅ 自动发送格式化数据到串口
- ✅ 自动记录数据到日志文件

## 使用示例

### 基础使用流程
```
1. 设置采样间隔 (可选，默认5秒)
   输入: command:set_interval=3
   输出: report:Sampling interval set to 3 seconds

2. 启动连续采样
   输入: command:start_sample
   输出: report:2025-06-15 00:01:44 ch0=3.25,ch1=20.15,ch2=10573.67

3. 系统自动发送数据 (每3秒一次)
   自动输出: report:2025-06-15 00:01:47 ch0=3.28,ch1=19.87,ch2=10489.23
   自动输出: report:2025-06-15 00:01:50 ch0=3.31,ch1=20.45,ch2=10612.89
   自动输出: report:2025-06-15 00:01:53 ch0=3.29,ch1=19.92,ch2=10534.56
   ... (持续自动发送)

4. 停止连续采样
   输入: command:stop_sample
   输出: report:ok
   (自动发送停止)
```

### 高级配置示例
```
# 设置1秒快速采样
command:set_interval=1
command:start_sample

# 设置10秒慢速采样  
command:set_interval=10
command:start_sample

# 设置30秒长间隔采样
command:set_interval=30
command:start_sample
```

## 技术实现

### 1. 连续采样机制
```c
void multi_channel_start_continuous_sampling(void)
{
    g_multi_channel_data.sampling_active = 1;  // 启用连续采样标志
    g_adc_control.state = SAMPLING_ACTIVE;     // 设置采样状态
    
    // 设置默认间隔 (如果未设置)
    if (g_adc_control.cycle_ms == 0) {
        g_adc_control.cycle_ms = 5000; // 默认5秒
    }
    
    // 重置时间戳，立即开始
    g_adc_control.last_sample_time = HAL_GetTick();
}
```

### 2. 自动数据发送
```c
// 在adc_process_sample函数中
if (g_multi_channel_data.sampling_active) {
    // 多通道连续采样模式：自动发送数据
    multi_channel_read_data();        // 读取3通道数据
    multi_channel_apply_ratios();     // 应用变比计算
    multi_channel_check_limits();     // 检查超限状态
    multi_channel_update_timestamp(); // 更新时间戳
    
    // 格式化并发送数据
    char output_buffer[128] = {0};
    multi_channel_format_output(output_buffer, sizeof(output_buffer), 1);
    my_printf(&huart1, "report:%s\r\n", output_buffer);
}
```

### 3. 时间间隔控制
```c
void handle_set_interval_cmd(char *params)
{
    // 解析间隔参数 (1-60秒)
    int interval_seconds = atoi(interval_str);
    
    // 设置采样间隔
    g_adc_control.cycle_ms = interval_seconds * 1000;
    
    // 保存到配置
    config_set_sample_cycle(interval_ms);
    config_save_to_flash();
}
```

## 输出格式

### 连续采样数据格式
```
report:YYYY-MM-DD HH:MM:SS ch0=xx.xx,ch1=xx.xx,ch2=xx.xx
```

**示例**:
```
report:2025-06-15 00:01:44 ch0=3.25,ch1=20.15,ch2=10573.67
report:2025-06-15 00:01:47 ch0=3.28,ch1=19.87,ch2=10489.23
report:2025-06-15 00:01:50 ch0=3.31,ch1=20.45,ch2=10612.89
```

### 超限数据格式
如果数据超过阈值，通道后会添加`*`标记：
```
report:2025-06-15 00:01:53 ch0*=5.25,ch1*=30.15,ch2=8573.67
```

## 配置选项

### 采样间隔设置
- **最小间隔**: 1秒
- **最大间隔**: 60秒  
- **默认间隔**: 5秒
- **精度**: 秒级

### 数据格式
- **时间戳**: 包含完整的日期和时间
- **数值精度**: 保留2位小数
- **超限标记**: 自动添加`*`号
- **变比应用**: 自动应用配置的变比

## 系统行为

### 启动连续采样时
1. ✅ 立即返回一次数据作为确认
2. ✅ 设置内部连续采样标志
3. ✅ 开始定时自动发送数据
4. ✅ 记录操作到日志文件

### 连续采样期间
1. ✅ 按设定间隔自动采集数据
2. ✅ 自动应用变比和检查超限
3. ✅ 自动发送格式化数据
4. ✅ 持续记录到日志文件
5. ✅ 响应其他命令 (如get_data, stop_sample)

### 停止连续采样时
1. ✅ 立即停止自动发送
2. ✅ 清除连续采样标志
3. ✅ 返回确认消息
4. ✅ 记录停止操作到日志

## 与其他命令的兼容性

### 连续采样期间可用的命令
- ✅ `command:get_data` - 立即获取当前数据
- ✅ `command:stop_sample` - 停止连续采样
- ✅ `command:set_interval=X` - 修改采样间隔
- ✅ `command:get_ratio` - 查看当前变比
- ✅ `command:get_limit` - 查看当前阈值
- ✅ 所有配置命令 (set_ratio, set_limit等)

### 命令优先级
1. **stop_sample** - 最高优先级，立即停止
2. **set_interval** - 立即生效，影响后续采样
3. **get_data** - 立即返回，不影响连续采样
4. **配置命令** - 立即生效，影响后续数据处理

## 性能特点

### 时间精度
- ✅ 基于系统时钟，精度约±10ms
- ✅ 自动补偿处理时间
- ✅ 长期运行稳定

### 资源使用
- ✅ 低CPU占用 (定时检查机制)
- ✅ 合理内存使用
- ✅ 不影响其他功能

### 数据一致性
- ✅ 每次采样都是最新数据
- ✅ 变比和阈值实时应用
- ✅ 时间戳精确同步

## 故障排除

### 问题1: 连续采样不自动发送数据
**可能原因**: 
- 采样间隔设置过大
- 系统时钟异常

**解决方案**:
```
command:set_interval=5    # 重新设置间隔
command:stop_sample       # 停止后重新启动
command:start_sample
```

### 问题2: 数据发送间隔不准确
**可能原因**:
- 系统负载过高
- 处理时间过长

**解决方案**:
- 增加采样间隔
- 检查系统性能

### 问题3: 无法停止连续采样
**解决方案**:
```
command:stop_sample       # 发送停止命令
# 如果无效，可以重启系统
```

## 日志记录

### 连续采样日志
系统会自动记录以下信息到SD卡：
- 连续采样启动/停止操作
- 采样间隔设置变更
- 每次采样的数据 (可选)

### 日志格式示例
```
continuous sampling started with 5s interval
continuous sample: 2025-06-15 00:01:44 ch0=3.25,ch1=20.15,ch2=10573.67
sampling interval set to 3 seconds
continuous sampling stopped
```

## 总结

连续采样功能现在完全支持：

### ✅ 核心功能
- 自动连续数据采集和发送
- 灵活的时间间隔设置 (1-60秒)
- 完整的启动/停止控制

### ✅ 数据质量
- 实时3通道数据
- 自动变比计算
- 自动超限检测
- 精确时间戳

### ✅ 系统集成
- 与现有命令完全兼容
- 不影响其他功能
- 完整的日志记录

**使用方法**: 发送 `command:start_sample` 后，系统会自动按间隔发送数据，无需手动操作！