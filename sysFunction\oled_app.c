#include "oled_app.h"
#include "adc_app.h"
#include "rtc_app.h"
#include "btn_app.h"
#include "config_app.h"

int Oled_Printf(uint8_t x, uint8_t y, const char *format, ...)
{
	char buffer[128];
	va_list arg;
	int len;

	va_start(arg, format);
	len = vsnprintf(buffer, sizeof(buffer), format, arg);
	va_end(arg);

	OLED_ShowStr(x, y, buffer, 16); 
	return len;
}

void oled_task(void)
{
    static display_mode_t last_display_mode = DISPLAY_MODE_MAX; // 记录上次显示模式
    static float last_display_values[3] = {-1.0f, -1.0f, -1.0f}; // 记录上次显示的3通道数值

    display_mode_t current_mode = display_mode_get();

    // 检查显示模式是否发生变化，如果变化则清屏
    if (current_mode != last_display_mode) {
        OLED_Clear();  // 清屏操作
        last_display_mode = current_mode;
        // 重置数值记录，强制刷新
        last_display_values[0] = -1.0f;
        last_display_values[1] = -1.0f;
        last_display_values[2] = -1.0f;
    }

    // 获取多通道数据
    multi_channel_read_data();
    multi_channel_apply_ratios();

    // 显示模式标题
    const char* mode_name = display_mode_get_name(current_mode);
    Oled_Printf(0, 0, "%s", mode_name);

    // 根据当前显示模式显示对应的数据
    float display_value = 0.0f;
    uint8_t channel_index = 0;
    const char* unit = "";

    switch (current_mode) {
        case DISPLAY_CH0_RAW:
            display_value = g_multi_channel_data.ch0_raw;
            channel_index = 0;
            unit = "V";
            break;
        case DISPLAY_CH1_RAW:
            display_value = g_multi_channel_data.ch1_raw;
            channel_index = 1;
            unit = "mA";
            break;
        case DISPLAY_CH2_RAW:
            display_value = g_multi_channel_data.ch2_raw;
            channel_index = 2;
            unit = "Ohm";
            break;
        case DISPLAY_CH0_RATIO:
            display_value = g_multi_channel_data.ch0_processed;
            channel_index = 0;
            unit = "V";
            break;
        case DISPLAY_CH1_RATIO:
            display_value = g_multi_channel_data.ch1_processed;
            channel_index = 1;
            unit = "mA";
            break;
        case DISPLAY_CH2_RATIO:
            display_value = g_multi_channel_data.ch2_processed;
            channel_index = 2;
            unit = "Ohm";
            break;
        default:
            display_value = 0.0f;
            unit = "";
            break;
    }

    // 检查数值是否发生变化，如果变化则清除数值显示区域
    if (last_display_values[channel_index] != display_value) {
        // 清除数值显示区域 (第2-3行)
        for (uint8_t page = 2; page <= 3; page++) {
            OLED_Set_Position(0, page);
            for (uint8_t x = 0; x < 128; x++) {
                OLED_Write_data(0);
            }
        }
        last_display_values[channel_index] = display_value;
    }

    // 显示数值
    if (display_value >= 1000.0f) {
        Oled_Printf(10, 2, "%.1f%s", display_value, unit);
    } else {
        Oled_Printf(20, 2, "%.2f%s", display_value, unit);
    }
}






