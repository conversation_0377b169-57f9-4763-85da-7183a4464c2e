#ifndef __SAMPLING_TYPES_H_
#define __SAMPLING_TYPES_H_

/**
 * @file    sampling_types.h
 * @brief   采样相关类型定义头文件
 * @details 定义采样系统中使用的公共类型和枚举
 *          避免循环依赖问题，为多个模块提供统一的类型定义
 * <AUTHOR> CIMC西门子杯竞赛项目组
 * @date    2025-01-01
 * @version 1.0
 */

// 采样控制状态枚举
typedef enum {
    SAMPLING_IDLE = 0,      // 空闲状态
    SAMPLING_ACTIVE,        // 采样活动状态
    SAMPLING_PAUSED         // 采样暂停状态
} sampling_state_t;

// 采样周期枚举
typedef enum {
    CYCLE_5S = 0,           // 5秒周期
    CYCLE_10S,              // 10秒周期
    CYCLE_15S               // 15秒周期
} sampling_cycle_t;

#endif /* __SAMPLING_TYPES_H_ */
