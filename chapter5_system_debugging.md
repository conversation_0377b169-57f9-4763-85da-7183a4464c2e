# 第五章 系统功能调试

## 5.1 日志文件生成错误的调试定位

### 5.1.1 为了定位log文件序号不正确的问题
当发现log文件序号跳跃或不连续时，我们设计了详细的状态跟踪机制。在执行`debug state`命令时，串口会打印一系列关键状态信息：

<augment_code_snippet path="sysFunction/sd_app.c" mode="EXCERPT">
````c
void debug_print_system_state(void)
{
    my_printf(&huart1, "[DEBUG] === DETAILED SYSTEM STATE ===\r\n");
    
    // Log ID管理器状态
    my_printf(&huart1, "[DEBUG] Log ID Manager:\r\n");
    my_printf(&huart1, "[DEBUG]   Current Log ID: %lu\r\n", g_log_id_manager.log_id);
    my_printf(&huart1, "[DEBUG]   Initialized: %d\r\n", g_log_id_manager.initialized);
    my_printf(&huart1, "[DEBUG]   Boot Count: %lu\r\n", g_boot_count);
    
    // 测试阶段状态
    my_printf(&huart1, "[DEBUG] Test Stage Manager:\r\n");
    my_printf(&huart1, "[DEBUG]   Current Stage: %d\r\n", test_stage_get_current_stage());
    my_printf(&huart1, "[DEBUG]   First Test Done: %d\r\n", g_test_stage_manager.first_test_done);
    my_printf(&huart1, "[DEBUG]   First Limit Done: %d\r\n", g_test_stage_manager.first_limit_done);
}
````
</augment_code_snippet>

### 5.1.2 为了定位Flash缓存同步失败的问题
当SD卡插拔后数据丢失时，我们设计了Flash缓存状态检测。在执行`system status`命令时，串口会显示：

```
=== System Status ===
Current Log ID: 2
State Consistency: ERROR
=== Log Files Status ===
log/log0.txt: 3 records
log/log1.txt: 0 records
log/log2.txt: 0 records
log/log3.txt: 0 records
```

### 5.1.3 为了定位状态不一致错误的问题
当系统状态出现不一致时，我们设计了自动检测和修复机制。在执行`check state`命令时，串口会打印：

<augment_code_snippet path="sysFunction/usart_app.c" mode="EXCERPT">
````c
void handle_check_state_cmd(char *params)
{
    my_printf(&huart1, "Checking system state consistency...\r\n");
    
    uint8_t is_consistent = check_test_stage_consistency();
    
    if (is_consistent) {
        my_printf(&huart1, "State consistency check: PASSED\r\n");
        my_printf(&huart1, "Current stage: %d\r\n", test_stage_get_current_stage());
    } else {
        my_printf(&huart1, "State consistency check: FAILED\r\n");
        my_printf(&huart1, "Attempting automatic repair...\r\n");
        
        if (auto_repair_inconsistent_state()) {
            my_printf(&huart1, "Automatic repair: SUCCESS\r\n");
        } else {
            my_printf(&huart1, "Automatic repair: FAILED\r\n");
            my_printf(&huart1, "Manual intervention required\r\n");
        }
    }
}
````
</augment_code_snippet>

## 5.2 SD卡存储错误的调试定位

### 5.2.1 为了定位SD卡检测失败的问题
当SD卡无法正常工作时，我们设计了分步检测流程。在执行`sd detect`命令时，串口会逐步显示：

<augment_code_snippet path="sysFunction/usart_app.c" mode="EXCERPT">
````c
void handle_sd_detect_cmd(char *params)
{
    my_printf(&huart1, "======SD Card Detection======\r\n");
    
    // 步骤1：重新初始化SDIO硬件
    my_printf(&huart1, "Step 1: Reinitializing SDIO hardware\r\n");
    if (HAL_SD_Init(&hsd) != HAL_OK) {
        my_printf(&huart1, "SDIO init........error\r\n");
        my_printf(&huart1, "Hardware initialization failed\r\n");
        return;
    }
    my_printf(&huart1, "SDIO init........ok\r\n");
    
    // 步骤5：检查BSP检测函数
    if (BSP_SD_IsDetected() == SD_PRESENT) {
        my_printf(&huart1, "BSP detect.......ok (SD card present)\r\n");
    } else {
        my_printf(&huart1, "BSP detect.......error (SD card not detected)\r\n");
    }
}
````
</augment_code_snippet>

### 5.2.2 为了定位SD卡文件操作失败的问题
当文件读写出现错误时，我们设计了详细的错误代码显示。在执行`sd test`命令时，串口会显示具体的错误信息：

<augment_code_snippet path="sysFunction/usart_app.c" mode="EXCERPT">
````c
void handle_sd_test_cmd(char *params)
{
    // 测试采样数据写入
    fr = sd_write_sample_data("", 1.5f);
    if (fr == FR_OK) {
        my_printf(&huart1, "Sample data......ok\r\n");
    } else {
        my_printf(&huart1, "Sample data......error (code: %d)\r\n", fr);
    }
    
    // 测试超限数据写入
    fr = sd_write_overlimit_data("", 30.0f, 10.0f);
    if (fr == FR_OK) {
        my_printf(&huart1, "OverLimit data...ok\r\n");
    } else {
        my_printf(&huart1, "OverLimit data...error (code: %d)\r\n", fr);
    }
}
````
</augment_code_snippet>

### 5.2.3 为了定位SD卡格式化失败的问题
当SD卡格式化出现问题时，我们设计了详细的错误分类。在执行`sd format`命令时，串口会根据不同错误类型显示：

<augment_code_snippet path="sysFunction/usart_app.c" mode="EXCERPT">
````c
void handle_sd_format_cmd(char *params)
{
    fr = f_mkfs("0:", sfd, au, bpData, 512);
    if (fr != FR_OK) {
        my_printf(&huart1, "Format failed! Error code: %d\r\n", fr);
        switch(fr) {
            case FR_NOT_READY:
                my_printf(&huart1, "SD card not ready\r\n");
                break;
            case FR_WRITE_PROTECTED:
                my_printf(&huart1, "SD card is write protected\r\n");
                break;
            case FR_DISK_ERR:
                my_printf(&huart1, "Disk I/O error\r\n");
                break;
            case FR_MKFS_ABORTED:
                my_printf(&huart1, "Format aborted\r\n");
                break;
            default:
                my_printf(&huart1, "Unknown error\r\n");
                break;
        }
    }
}
````
</augment_code_snippet>

## 5.3 Flash存储错误的调试定位

### 5.3.1 为了定位Flash缓存写入失败的问题
当Flash写入出现错误时，我们设计了重试机制和详细的错误跟踪。在Flash写入过程中，串口会显示：

<augment_code_snippet path="sysFunction/sd_app.c" mode="EXCERPT">
````c
flash_result_t flash_write_with_retry(uint32_t addr, const void *data, size_t size)
{
    const int MAX_RETRY_ATTEMPTS = 4;
    const uint32_t BASE_DELAY_MS = 50;

    for (int attempt = 0; attempt < MAX_RETRY_ATTEMPTS; attempt++) {
        // 尝试写入Flash
        flash_result_t result = flash_direct_write(addr, data, size);

        if (result == FLASH_OK) {
            if (attempt > 0) {
                my_printf(&huart1, "[ERROR_RECOVERY] Flash write succeeded on attempt %d\r\n", attempt + 1);
            }
            return FLASH_OK;  // 写入成功
        }

        // 写入失败，记录错误并准备重试
        my_printf(&huart1, "[ERROR_RECOVERY] Flash write failed, attempt %d/%d\r\n",
                  attempt + 1, MAX_RETRY_ATTEMPTS);
    }
}
````
</augment_code_snippet>

### 5.3.2 为了定位Flash缓存数据恢复错误的问题
当系统启动时Flash缓存恢复失败，我们设计了详细的恢复状态跟踪。在系统启动过程中，串口会显示：

```
[FLASH_CACHE] Force caching: system init
[FLASH_CACHE] Caching to Flash: rtc config
[FLASH_RESTORE] Restoring 5 cached log entries from Flash
[FLASH_RESTORE] Successfully restored log entry: system init
[FLASH_RESTORE] Successfully restored log entry: rtc config
```

## 5.4 配置参数错误的调试定位

### 5.4.1 为了定位配置文件读取失败的问题
当config.ini文件无法正常读取时，我们设计了分步检测机制。在执行`conf`命令时，串口会显示：

<augment_code_snippet path="sysFunction/usart_app.c" mode="EXCERPT">
````c
void handle_conf_cmd(char *params)
{
    config_status_t status = config_display_from_sd();

    if (status == CONFIG_OK) {
        // 竞赛要求：读取成功后显示"config read success"
    } else if (status == CONFIG_FILE_NOT_FOUND) {
        // 竞赛要求：文件不存在时的精确输出格式
        my_printf(&huart1, "config.ini file not found.\r\n");
    } else {
        // 其他错误情况
        my_printf(&huart1, "config read error\r\n");
    }
}
````
</augment_code_snippet>

### 5.4.2 为了定位配置参数保存失败的问题
当配置参数无法保存到Flash时，我们设计了验证机制。在执行`config read`命令时，串口会显示：

<augment_code_snippet path="sysFunction/usart_app.c" mode="EXCERPT">
````c
void handle_config_read_cmd(char *params)
{
    // 直接从Flash读取参数，不覆盖内存中的g_config_params
    config_params_t flash_params;
    if (flash_direct_read(CONFIG_FLASH_ADDR, &flash_params, sizeof(flash_params)) == FLASH_OK) {
        my_printf(&huart1, "read parameters from flash\r\n");
        my_printf(&huart1, "ratio:%.1f\r\n", flash_params.ratio);
        my_printf(&huart1, "limit:%.2f\r\n", flash_params.limit);
    } else {
        my_printf(&huart1, "read parameters from flash failed\r\n");
    }
}
````
</augment_code_snippet>

### 5.4.3 为了定位参数范围验证错误的问题
当设置的参数超出有效范围时，我们设计了详细的错误提示。在执行`ratio`命令时，串口会显示：

<augment_code_snippet path="sysFunction/usart_app.c" mode="EXCERPT">
````c
void handle_ratio_cmd(char *params)
{
    if (strlen(params) > 0) {
        float ratio = atof(params);
        if (config_set_ratio(ratio) == CONFIG_OK) {
            my_printf(&huart1, "ratio set to %.1f\r\n", config_get_ratio());
        } else {
            // 设置失败时提供明确的错误信息
            my_printf(&huart1, "ratio set failed (valid range: 0-100)\r\n");
            my_printf(&huart1, "Current Ratio = %.1f\r\n", config_get_ratio());
        }
    }
}
````
</augment_code_snippet>

## 5.5 硬件自检错误的调试定位

### 5.5.1 为了定位Flash芯片检测失败的问题
当Flash芯片无法正常识别时，我们设计了多重检测机制。在执行`test`命令时，串口会显示详细的检测过程：

<augment_code_snippet path="sysFunction/selftest_app.c" mode="EXCERPT">
````c
selftest_result_t selftest_check_flash_simple(selftest_info_t *info)
{
    if (info == NULL) return SELFTEST_FLASH_ERROR;

    // 步骤1：读取Flash ID验证硬件连接
    info->flash_id = spi_flash_read_id();
    if (info->flash_id == 0x000000 || info->flash_id == 0xFFFFFF) {
        info->flash_id = spi_flash_read_id_alt(); // 备用读取方法
    }

    // 如果队伍ID等于默认值，说明Flash读取失败
    if (strcmp(g_device_info.team_number, DEFAULT_TEAM_NUMBER) == 0) {
        info->flash_ok = 0;
        return SELFTEST_FLASH_ERROR;
    }

    // Flash ID有效且能读取到队伍ID，Flash检测通过
    info->flash_ok = 1;
    return SELFTEST_OK;
}
````
</augment_code_snippet>

### 5.5.2 为了定位RTC时钟错误的问题
当RTC时钟工作异常时，我们设计了时间有效性检测。在执行`test`命令时，串口会显示RTC状态：

<augment_code_snippet path="sysFunction/selftest_app.c" mode="EXCERPT">
````c
selftest_result_t selftest_check_rtc(selftest_info_t *info)
{
    if (info == NULL) return SELFTEST_RTC_ERROR;

    RTC_TimeTypeDef current_time = {0};
    RTC_DateTypeDef current_date = {0};

    // 获取当前RTC时间
    HAL_RTC_GetTime(&hrtc, &current_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_date, RTC_FORMAT_BIN);

    // 检查时间是否有效（年份应该在合理范围内）
    if (current_date.Year < 20 || current_date.Year > 50) {  // 2020-2050年
        info->rtc_ok = 0;
        return SELFTEST_RTC_ERROR;
    }

    info->rtc_ok = 1;
    return SELFTEST_OK;
}
````
</augment_code_snippet>

### 5.5.3 为了定位系统自检结果输出错误的问题
当自检结果显示异常时，我们设计了统一的结果输出格式。在执行`test`命令时，串口会按照固定格式显示：

<augment_code_snippet path="sysFunction/selftest_app.c" mode="EXCERPT">
````c
void selftest_print_results(const selftest_info_t *info)
{
    extern UART_HandleTypeDef huart1;

    my_printf(&huart1, "======System Self-Test======\r\n");

    // Flash检测结果
    my_printf(&huart1, "Flash ID: 0x%06X\r\n", info->flash_id);
    if (info->flash_ok) {
        my_printf(&huart1, "Flash............ok\r\n");
    } else {
        my_printf(&huart1, "Flash............error\r\n");
    }

    // SD卡检测结果
    if (info->sd_ok) {
        my_printf(&huart1, "TF card..........ok\r\n");
    } else {
        my_printf(&huart1, "TF card..........error\r\n");
    }

    // RTC检测结果
    if (info->rtc_ok) {
        my_printf(&huart1, "RTC..............ok\r\n");
    } else {
        my_printf(&huart1, "RTC..............error\r\n");
    }
}
````
</augment_code_snippet>

## 5.6 采样控制错误的调试定位

### 5.6.1 为了定位采样启停控制失败的问题
当采样无法正常启停时，我们设计了状态跟踪机制。在执行`start`或`stop`命令时，串口会显示状态变化：

<augment_code_snippet path="sysFunction/usart_app.c" mode="EXCERPT">
````c
void handle_start_cmd(char *params)
{
    if (g_adc_control.state == SAMPLING_IDLE) {
        adc_start_sampling();
        my_printf(&huart1, "Periodic Sampling\r\n");
        my_printf(&huart1, "sample cycle: %ds\r\n", g_adc_control.cycle_ms / 1000);

        // 记录操作日志
        char log_buffer[64] = {0};
        snprintf(log_buffer, sizeof(log_buffer), "sample start - cycle %ds", g_adc_control.cycle_ms / 1000);
        sd_write_log_data(log_buffer);
    } else {
        my_printf(&huart1, "Sampling already active\r\n");
    }
}
````
</augment_code_snippet>

### 5.6.2 为了定位ADC数据采集异常的问题
当ADC采集数据异常时，我们设计了数据验证机制。在ADC任务执行过程中，串口会显示采样状态：

<augment_code_snippet path="sysFunction/adc_app.c" mode="EXCERPT">
````c
void adc_task(void) //电压测量主函数，每0.1秒跑一次
{
    uint32_t adc_sum = 0; //累加测量结果

    for (uint16_t i = 0; i < ADC_DMA_BUFFER_SIZE; i++) { //把所有测量值加起来
        adc_sum += adc_dma_buffer[i];
    }

    adc_val = adc_sum / ADC_DMA_BUFFER_SIZE; //算平均值

    float raw_voltage = ((float)adc_val * 3.3f) / 4096.0f; //转成电压值

    float median_filtered = voltage_median_filter(raw_voltage); //去异常值
    voltage = voltage_sliding_average_filter(median_filtered); //平滑处理

    // 应用变比计算最终电压值
    g_adc_control.processed_voltage = voltage * config_get_ratio();
    g_adc_control.display_voltage = g_adc_control.processed_voltage;

    // 检查是否超限
    adc_check_over_limit();
}
````
</augment_code_snippet>

### 5.6.3 为了定位超限检测错误的问题
当超限检测逻辑异常时，我们设计了阈值比较跟踪。在超限检测过程中，系统会记录比较结果：

<augment_code_snippet path="sysFunction/adc_app.c" mode="EXCERPT">
````c
void adc_check_over_limit(void) //检查是否超限
{
    float limit = config_get_limit(); //用户设的限制值
    extern uint8_t ucLed[6]; //LED数组

    if (g_adc_control.processed_voltage > limit) {
        g_adc_control.over_limit = 1; //超限了
        ucLed[1] = 1; //亮LED2

        // 记录超限数据到SD卡
        char timestamp[32] = {0};
        sd_write_overlimit_data(timestamp, g_adc_control.processed_voltage, limit);
    } else {
        g_adc_control.over_limit = 0; //正常
        ucLed[1] = 0; //关LED2
    }
}
````
</augment_code_snippet>

## 5.7 数据加密错误的调试定位

### 5.7.1 为了定位加密状态切换失败的问题
当加密功能无法正常切换时，我们设计了状态验证机制。在执行`hide`或`unhide`命令时，串口会显示状态变化：

<augment_code_snippet path="sysFunction/usart_app.c" mode="EXCERPT">
````c
void handle_hide_cmd(char *params)
{
    adc_enable_encryption();
    my_printf(&huart1, "Data encryption enabled\r\n");

    // 记录加密操作日志
    sd_write_log_data("encryption enabled");
}

void handle_unhide_cmd(char *params)
{
    adc_disable_encryption();
    my_printf(&huart1, "Data encryption disabled\r\n");

    // 记录解密操作日志
    sd_write_log_data("encryption disabled");
}
````
</augment_code_snippet>

### 5.7.2 为了定位加密数据格式错误的问题
当加密数据格式异常时，我们设计了数据格式验证。在写入hideData目录时，系统会验证数据格式：

<augment_code_snippet path="sysFunction/sd_app.c" mode="EXCERPT">
````c
static FRESULT format_hidedata(float voltage, uint8_t is_overlimit, char *formatted_data)
{
    if (formatted_data == NULL) return FR_INVALID_PARAMETER;

    // 获取当前RTC时间
    RTC_TimeTypeDef current_rtc_time = {0};
    RTC_DateTypeDef current_rtc_date = {0};
    HAL_RTC_GetTime(&hrtc, &current_rtc_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_rtc_date, RTC_FORMAT_BIN);

    // 格式化加密数据：时间戳 + 电压值 + 超限标志
    snprintf(formatted_data, 128,
             "%04d-%02d-%02d %02d:%02d:%02d,%.2f,%s\r\n",
             current_rtc_date.Year + 2000,
             current_rtc_date.Month,
             current_rtc_date.Date,
             current_rtc_time.Hours,
             current_rtc_time.Minutes,
             current_rtc_time.Seconds,
             voltage,
             is_overlimit ? "OVER" : "NORMAL");

    return FR_OK;
}
````
</augment_code_snippet>

## 5.8 系统恢复错误的调试定位

### 5.8.1 为了定位SD卡恢复失败的问题
当SD卡故障恢复失败时，我们设计了分步恢复流程。在执行`recover sd`命令时，串口会显示恢复过程：

<augment_code_snippet path="sysFunction/usart_app.c" mode="EXCERPT">
````c
void handle_recover_sd_cmd(char *params)
{
    my_printf(&huart1, "Starting SD card recovery...\r\n");

    // 步骤1：重新初始化SD卡
    my_printf(&huart1, "Step 1: Reinitializing SD card\r\n");
    sd_app_init();

    // 步骤2：检查文件系统状态
    my_printf(&huart1, "Step 2: Checking filesystem status\r\n");
    if (g_filesystem_mounted) {
        my_printf(&huart1, "Filesystem mounted successfully\r\n");
    } else {
        my_printf(&huart1, "Filesystem mount failed\r\n");
        return;
    }

    // 步骤3：恢复Flash缓存数据
    my_printf(&huart1, "Step 3: Restoring cached data from Flash\r\n");
    if (restore_flash_cache_to_sd() == FR_OK) {
        my_printf(&huart1, "Flash cache restored successfully\r\n");
    } else {
        my_printf(&huart1, "Flash cache restore failed\r\n");
    }

    my_printf(&huart1, "SD card recovery completed\r\n");
}
````
</augment_code_snippet>

### 5.8.2 为了定位启动次数重置错误的问题
当启动次数重置失败时，我们设计了验证机制。在执行`reset boot`命令时，串口会显示重置过程：

<augment_code_snippet path="sysFunction/usart_app.c" mode="EXCERPT">
````c
void handle_reset_boot_cmd(char *params)
{
    my_printf(&huart1, "Resetting boot count and clearing Flash cache...\r\n");

    // 清除Flash缓存
    clear_flash_cache();

    // 重置boot_count为0
    g_boot_count = 0;
    g_boot_count_incremented = 0;

    // 保存到Flash
    if (sd_save_log_id_to_flash() == FR_OK) {
        my_printf(&huart1, "Boot count reset to 0 and saved to Flash\r\n");
    } else {
        my_printf(&huart1, "Failed to save boot count to Flash\r\n");
    }

    // 重置测试阶段
    test_stage_reset();

    my_printf(&huart1, "System ready for next competition test\r\n");
}
````
</augment_code_snippet>

## 5.9 调试命令体系总结

### 5.9.1 问题定位命令分类
我们设计的调试命令体系按照问题类型进行分类，每类命令都有特定的错误定位目标：

**状态检查类命令**：
- `debug state` - 定位系统状态不一致问题
- `system status` - 定位系统运行状态异常问题
- `check state` - 定位状态一致性错误问题

**硬件检测类命令**：
- `test` - 定位硬件自检失败问题
- `sd detect` - 定位SD卡检测失败问题
- `sd test` - 定位SD卡功能异常问题

**配置管理类命令**：
- `conf` - 定位配置文件读取失败问题
- `config read` - 定位Flash配置读取失败问题
- `config save` - 定位配置保存失败问题

**故障恢复类命令**：
- `recover sd` - 定位SD卡故障恢复失败问题
- `reset boot` - 定位启动状态重置失败问题
- `sd sync` - 定位文件系统同步失败问题

### 5.9.2 调试输出信息标准化
为了便于问题定位，我们对所有调试输出信息进行了标准化处理：

**错误信息格式**：
```
[ERROR_RECOVERY] Flash write failed, attempt 1/4
[DEBUG] Current Log ID: 2
[FLASH_CACHE] Caching to Flash: rtc config
[FLASH_RESTORE] Successfully restored log entry: system init
```

**状态信息格式**：
```
State consistency check: FAILED
SDIO init........ok
Sample data......error (code: 3)
Flash............ok
```

### 5.9.3 典型问题调试流程示例

#### 问题1：log文件序号跳跃
**现象**：发现log0.txt存在，但log1.txt缺失，直接生成了log2.txt
**调试步骤**：
1. 执行`debug state`查看Log ID管理器状态
2. 检查Boot Count和测试阶段状态
3. 执行`check state`进行状态一致性检查
4. 如果发现不一致，执行自动修复或手动`reset boot`

#### 问题2：SD卡写入失败
**现象**：采样数据无法写入SD卡，LED指示异常
**调试步骤**：
1. 执行`sd detect`检查SD卡硬件连接
2. 执行`sd test`测试文件操作功能
3. 如果检测失败，执行`recover sd`进行恢复
4. 检查Flash缓存是否正常工作

#### 问题3：配置参数丢失
**现象**：系统重启后变比和阈值恢复为默认值
**调试步骤**：
1. 执行`config read`检查Flash中的配置
2. 执行`conf`检查SD卡配置文件
3. 重新设置参数并执行`config save`
4. 验证参数是否正确保存

通过这套完整的调试定位体系，我们能够快速识别和解决系统运行过程中出现的各种问题，确保系统稳定可靠地完成竞赛任务。每个调试命令都有明确的问题定位目标，串口输出信息标准化且易于理解，大大提高了问题排查的效率。
