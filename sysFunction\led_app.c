#include "led_app.h"
#include "gpio.h"          
#include "math.h"          


uint8_t ucLed[6] = {0,0,0,0,0,0};


void led_disp(uint8_t *ucLed)
{

    uint8_t temp = 0x00;

    static uint8_t temp_old = 0xff;

    for (int i = 0; i < 6; i++)
    {
              if (ucLed[i]) temp |= (1<<i);
    }


    if (temp_old != temp)
    {
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_8, (temp & 0x01) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 0
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_9, (temp & 0x02) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 1
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_10, (temp & 0x04) ? GPIO_PIN_SET : GP<PERSON>_PIN_RESET); // LED 2
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_11, (temp & 0x08) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 3
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_12,  (temp & 0x10) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 4
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_13,  (temp & 0x20) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 5
        

        temp_old = temp;
    }
}

void led_task(void)
{
  
    led_disp(ucLed);
}




