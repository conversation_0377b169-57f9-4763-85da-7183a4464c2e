#ifndef __RTC_APP_H__
#define __RTC_APP_H__

#include "mydefine.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @file rtc_app.h
 * @brief RTC应用层头文件 - 2025 CIMC西门子杯智能制造挑战赛
 * @details 提供RTC时间设置和查询功能，专门用于竞赛要求的时间管理
 */

// === 基础RTC功能 ===
void rtc_proc(void);  // 原有的RTC处理函数

// === 竞赛要求的核心功能 ===
/**
 * @brief 从字符串设置RTC时间（用于"RTC Config"指令）
 * @param time_str 时间字符串，格式："2025-01-01 12:00:30"
 * @retval HAL_OK 设置成功，HAL_ERROR 设置失败
 */
HAL_StatusTypeDef rtc_set_time_from_string(const char *time_str);

/**
 * @brief 打印当前时间到串口（用于调试）
 * @retval None
 */
void rtc_print_current_time(void);

/**
 * @brief 格式化当前时间为字符串（用于"RTC now"指令）
 * @param buffer 输出缓冲区
 * @param buffer_size 缓冲区大小
 * @retval None
 */
void rtc_format_current_time_string(char *buffer, size_t buffer_size);

// === 扩展功能（为其他模块预留接口） ===
/**
 * @brief 获取当前时间信息
 * @param current_time 时间结构体指针
 * @param current_date 日期结构体指针
 * @retval None
 */
void rtc_get_time_info(RTC_TimeTypeDef *current_time, RTC_DateTypeDef *current_date);

/**
 * @brief 格式化指定时间为字符串（扩展功能）
 * @param sTime RTC时间结构体指针
 * @param sDate RTC日期结构体指针
 * @param buffer 输出缓冲区
 * @param buffer_size 缓冲区大小
 * @retval None
 */
void format_time_output(const RTC_TimeTypeDef *sTime, const RTC_DateTypeDef *sDate, char *buffer, size_t buffer_size);

// === Unix时间戳功能（用于数据隐藏功能） ===
/**
 * @brief 将RTC时间转换为Unix时间戳（用于hide指令）
 * @param sTime RTC时间结构体指针
 * @param sDate RTC日期结构体指针
 * @retval Unix时间戳（秒）
 */
uint32_t rtc_convert_to_unix_timestamp(const RTC_TimeTypeDef *sTime, const RTC_DateTypeDef *sDate);

/**
 * @brief 获取当前RTC时间的Unix时间戳（用于hide指令）
 * @retval 当前Unix时间戳（秒）
 */
uint32_t rtc_get_unix_timestamp_now(void);

// 简化函数名，用于多通道数据采集
#define rtc_get_unix_timestamp() rtc_get_unix_timestamp_now()

#ifdef __cplusplus
}
#endif

#endif /* __RTC_APP_H__ */
