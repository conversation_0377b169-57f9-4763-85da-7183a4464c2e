# 多通道硬件接口配置指南

## 概述
本文档说明如何配置和使用多通道数据采集的硬件接口，支持真实硬件和测试模式的无缝切换。

## 当前状态
- **测试模式**: ✅ 已启用 (用于串口测试)
- **真实硬件**: 🔄 接口已预留，待启用
- **数据输出**: ✅ 3通道独立数据

## 硬件接口配置

### 1. 启用真实硬件模式

**步骤1**: 在 `sysFunction/adc_app.h` 中取消注释以下行：
```c
// 取消注释下面的宏定义来启用真实硬件接口
#define USE_REAL_HARDWARE_ADC
```

**步骤2**: 根据实际硬件连接调整通道映射：
```c
// 硬件通道映射 (可根据实际硬件连接调整)
#define HARDWARE_CH0_CHANNEL    GD30AD3344_CH_AIN0_GND  // 通道0映射到AIN0
#define HARDWARE_CH1_CHANNEL    GD30AD3344_CH_AIN1_GND  // 通道1映射到AIN1
#define HARDWARE_CH2_CHANNEL    GD30AD3344_CH_AIN2_GND  // 通道2映射到AIN2
```

**步骤3**: 调整增益设置：
```c
// 硬件增益设置 (可根据信号范围调整)
#define HARDWARE_CH0_GAIN       GD30AD3344_PGA_6V144    // 通道0增益
#define HARDWARE_CH1_GAIN       GD30AD3344_PGA_6V144    // 通道1增益
#define HARDWARE_CH2_GAIN       GD30AD3344_PGA_6V144    // 通道2增益
```

**步骤4**: 设置校准系数：
```c
// 硬件校准系数 (在启用真实硬件时使用)
#define CH0_CALIBRATION_FACTOR  1.0f    // 通道0校准系数
#define CH1_CALIBRATION_FACTOR  1.0f    // 通道1校准系数  
#define CH2_CALIBRATION_FACTOR  1.0f    // 通道2校准系数
```

### 2. 测试模式配置 (当前使用)

测试模式会生成变化的模拟数据，便于验证系统功能：

```c
// 通道0: 电压 (基础值 + 小幅波动)
g_multi_channel_data.ch0_raw = base_voltage + 0.1f * sinf(sample_counter * 0.1f);

// 通道1: 电流 (模拟电流传感器，有不同的变化模式)
g_multi_channel_data.ch1_raw = (base_voltage * 6.06f) + 0.5f * cosf(sample_counter * 0.15f);

// 通道2: 电阻 (模拟电阻测量，有更大的变化范围)
g_multi_channel_data.ch2_raw = (base_voltage * 3030.0f) + 100.0f * sinf(sample_counter * 0.08f);
```

## 可用的硬件接口函数

### 1. 初始化函数
```c
void multi_channel_hardware_init(void);
```
- 根据配置初始化真实硬件或测试模式
- 自动记录初始化状态到日志

### 2. 状态查询函数
```c
const char* multi_channel_get_hardware_status(void);
```
- 返回当前硬件状态字符串
- 真实硬件: "Real Hardware (GD30AD3344)"
- 测试模式: "Test Mode (Simulated Data)"

### 3. 运行时切换函数
```c
void multi_channel_enable_real_hardware(void);  // 切换到真实硬件
void multi_channel_enable_test_mode(void);      // 切换到测试模式
```
- 支持运行时动态切换数据源
- 自动记录切换操作到日志

## GD30AD3344硬件接口

### 支持的通道
- `GD30AD3344_CH_AIN0_GND`: 模拟输入通道0
- `GD30AD3344_CH_AIN1_GND`: 模拟输入通道1  
- `GD30AD3344_CH_AIN2_GND`: 模拟输入通道2
- `GD30AD3344_CH_AIN3_GND`: 模拟输入通道3

### 支持的增益
- `GD30AD3344_PGA_6V144`: ±6.144V 量程
- `GD30AD3344_PGA_4V096`: ±4.096V 量程
- `GD30AD3344_PGA_2V048`: ±2.048V 量程
- `GD30AD3344_PGA_1V024`: ±1.024V 量程
- `GD30AD3344_PGA_0V512`: ±0.512V 量程
- `GD30AD3344_PGA_0V256`: ±0.256V 量程

### 核心读取函数
```c
float GD30AD3344_AD_Read(uint8_t channel, uint8_t gain);
```

## 测试验证

### 当前测试模式输出示例
```
输入: command:start_sample
输出: report:2025-06-15 00:01:44 ch0=3.25,ch1=20.15,ch2=10573.67

输入: command:get_data  
输出: report:ch0=3.28,ch1=19.87,ch2=10489.23
```

**特点**:
- ✅ 3个通道输出不同的数值
- ✅ 数值会随时间变化 (模拟真实传感器)
- ✅ 保持在合理的数值范围内

### 真实硬件模式输出示例
启用真实硬件后，输出将反映实际的传感器读数：
```
输入: command:start_sample
输出: report:2025-06-15 00:01:44 ch0=3.301,ch1=0.025,ch2=1024.56
```

## 校准和配置

### 1. 硬件校准
如果发现硬件读数有偏差，可以调整校准系数：
```c
#define CH0_CALIBRATION_FACTOR  1.05f   // 通道0校准 (+5%)
#define CH1_CALIBRATION_FACTOR  0.98f   // 通道1校准 (-2%)
#define CH2_CALIBRATION_FACTOR  1.00f   // 通道2校准 (无调整)
```

### 2. 增益选择
根据信号幅度选择合适的增益：
- **高精度小信号**: 使用 `GD30AD3344_PGA_0V256` 或 `GD30AD3344_PGA_0V512`
- **一般信号**: 使用 `GD30AD3344_PGA_2V048` 或 `GD30AD3344_PGA_4V096`
- **大信号**: 使用 `GD30AD3344_PGA_6V144`

### 3. 通道映射
根据实际硬件连接调整通道映射：
```c
// 示例：如果CH1连接到AIN3
#define HARDWARE_CH1_CHANNEL    GD30AD3344_CH_AIN3_GND
```

## 集成步骤

### 阶段1: 当前状态 (测试模式)
- ✅ 串口命令测试
- ✅ 3通道数据输出验证
- ✅ 变比和阈值功能测试

### 阶段2: 硬件准备
1. 连接GD30AD3344硬件
2. 验证硬件连接
3. 测试单通道读取

### 阶段3: 硬件集成
1. 启用 `USE_REAL_HARDWARE_ADC` 宏
2. 调整通道映射和增益
3. 校准硬件读数

### 阶段4: 验证测试
1. 对比测试模式和硬件模式输出
2. 验证数据精度和稳定性
3. 长时间运行测试

## 故障排除

### 问题1: 硬件读数异常
**解决方案**:
1. 检查硬件连接
2. 验证增益设置
3. 调整校准系数

### 问题2: 数据不更新
**解决方案**:
1. 检查 `GD30AD3344_Init()` 是否成功
2. 验证SPI通信
3. 检查电源供应

### 问题3: 精度不足
**解决方案**:
1. 选择更高精度的增益
2. 添加软件滤波
3. 调整采样频率

## 性能优化

### 1. 采样频率优化
```c
// 可以在multi_channel_read_data中添加采样频率控制
static uint32_t last_sample_time = 0;
uint32_t current_time = HAL_GetTick();
if (current_time - last_sample_time < MIN_SAMPLE_INTERVAL) {
    return; // 跳过本次采样
}
last_sample_time = current_time;
```

### 2. 数据滤波
```c
// 可以添加移动平均滤波
#define FILTER_SIZE 8
static float ch0_filter[FILTER_SIZE] = {0};
static uint8_t filter_index = 0;

// 添加新数据到滤波器
ch0_filter[filter_index] = raw_value;
filter_index = (filter_index + 1) % FILTER_SIZE;

// 计算平均值
float filtered_value = 0;
for (int i = 0; i < FILTER_SIZE; i++) {
    filtered_value += ch0_filter[i];
}
filtered_value /= FILTER_SIZE;
```

## 总结

当前系统已经完全支持多通道数据采集，具备以下特点：

### ✅ 已实现功能
- 3通道独立数据采集
- 测试模式和真实硬件模式切换
- 完整的硬件接口预留
- 灵活的配置选项

### 🔄 待完善功能
- 真实硬件模式的最终验证
- 硬件校准参数调优
- 高级滤波算法集成

### 🎯 使用建议
1. **当前阶段**: 继续使用测试模式进行串口功能验证
2. **硬件就绪后**: 启用 `USE_REAL_HARDWARE_ADC` 宏
3. **生产部署**: 根据实际硬件调整配置参数

**系统已完全准备好硬件集成，接口预留完整！** 🚀