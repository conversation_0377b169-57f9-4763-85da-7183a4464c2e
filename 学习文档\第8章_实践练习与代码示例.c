/**
 * @file    第8章_实践练习与代码示例.c
 * @brief   Flash与SD卡存储系统实现 - 实践练习代码
 * @details 通过实际代码示例帮助理解SPI Flash操作、FATFS文件系统、多文件夹管理的实现原理
 * <AUTHOR>
 * @date    2025-01-07
 */

#include "main.h"
#include "stdio.h"
#include "string.h"
#include "flash_app.h"
#include "sd_app.h"

// ============================================================================
// 练习1：SPI Flash基本操作演示
// ============================================================================

/**
 * @brief 演示SPI Flash基本信息
 */
void practice_flash_info_demo(void)
{
    printf("=== SPI Flash基本信息演示 ===\r\n");
    
    // 获取Flash信息
    flash_info_t flash_info;
    flash_result_t result = flash_get_info(&flash_info);
    
    if (result == FLASH_OK) {
        printf("Flash信息:\r\n");
        printf("  总容量: %d MB (%d 字节)\r\n", 
               flash_info.total_size / (1024 * 1024), flash_info.total_size);
        printf("  已用空间: %d 字节\r\n", flash_info.used_size);
        printf("  可用空间: %d 字节\r\n", flash_info.free_size);
        printf("  块大小: %d 字节\r\n", flash_info.block_size);
        printf("  块数量: %d 个\r\n", flash_info.block_count);
        printf("  挂载状态: %s\r\n", flash_info.mounted ? "已挂载" : "未挂载");
    } else {
        printf("获取Flash信息失败: %d\r\n", result);
    }
    
    printf("\nFlash存储层次结构:\r\n");
    printf("芯片 (16MB)\r\n");
    printf("├── 块 (64KB) × 256个\r\n");
    printf("│   ├── 扇区 (4KB) × 16个\r\n");
    printf("│   │   ├── 页 (256B) × 16个\r\n");
    printf("│   │   │   └── 字节 × 256个\r\n");
    
    printf("\nFlash操作特点:\r\n");
    printf("1. 必须先擦除再写入\r\n");
    printf("2. 擦除以扇区(4KB)为单位\r\n");
    printf("3. 写入以页(256B)为单位\r\n");
    printf("4. 读取可以任意字节对齐\r\n");
}

/**
 * @brief 演示Flash读写操作
 */
void practice_flash_read_write_demo(void)
{
    printf("=== Flash读写操作演示 ===\r\n");
    
    // 测试地址和数据
    uint32_t test_addr = 0x10000;  // 64KB地址，避免与系统数据冲突
    char write_data[] = "Hello Flash Storage!";
    char read_data[64] = {0};
    
    printf("测试地址: 0x%08X\r\n", test_addr);
    printf("写入数据: \"%s\"\r\n", write_data);
    printf("数据长度: %d 字节\r\n", strlen(write_data));
    
    // 写入数据
    printf("\n执行Flash写入操作...\r\n");
    flash_result_t write_result = flash_direct_write(test_addr, write_data, strlen(write_data));
    
    if (write_result == FLASH_OK) {
        printf("Flash写入成功\r\n");
    } else {
        printf("Flash写入失败: %d\r\n", write_result);
        return;
    }
    
    // 读取数据
    printf("\n执行Flash读取操作...\r\n");
    flash_result_t read_result = flash_direct_read(test_addr, read_data, strlen(write_data));
    
    if (read_result == FLASH_OK) {
        read_data[strlen(write_data)] = '\0';  // 确保字符串结束
        printf("Flash读取成功\r\n");
        printf("读取数据: \"%s\"\r\n", read_data);
        
        // 验证数据一致性
        if (strcmp(write_data, read_data) == 0) {
            printf("✓ 数据验证成功，读写一致\r\n");
        } else {
            printf("✗ 数据验证失败，读写不一致\r\n");
        }
    } else {
        printf("Flash读取失败: %d\r\n", read_result);
    }
    
    printf("\nFlash操作流程:\r\n");
    printf("1. flash_direct_erase_sector() - 擦除扇区\r\n");
    printf("2. spi_flash_buffer_write() - 写入数据\r\n");
    printf("3. spi_flash_buffer_read() - 读取数据\r\n");
}

/**
 * @brief 演示Flash扇区对齐
 */
void practice_flash_sector_alignment_demo(void)
{
    printf("=== Flash扇区对齐演示 ===\r\n");
    
    // 测试不同的地址
    uint32_t test_addresses[] = {0x10000, 0x10100, 0x10500, 0x10FFF, 0x11000};
    uint8_t addr_count = sizeof(test_addresses) / sizeof(uint32_t);
    
    printf("扇区大小: 4KB (4096字节)\r\n");
    printf("扇区对齐掩码: 0xFFFFF000\r\n\r\n");
    
    printf("地址\t\t扇区起始\t是否对齐\r\n");
    printf("--------------------------------------------\r\n");
    
    for (uint8_t i = 0; i < addr_count; i++) {
        uint32_t addr = test_addresses[i];
        uint32_t sector_addr = addr & 0xFFFFF000;  // 4KB扇区对齐
        uint8_t is_aligned = (addr == sector_addr) ? 1 : 0;
        
        printf("0x%08X\t0x%08X\t%s\r\n", 
               addr, sector_addr, is_aligned ? "是" : "否");
    }
    
    printf("\n扇区对齐的重要性:\r\n");
    printf("1. Flash擦除必须以扇区为单位\r\n");
    printf("2. 写入前必须先擦除整个扇区\r\n");
    printf("3. 扇区内的其他数据会被清除\r\n");
    printf("4. 合理规划地址避免数据冲突\r\n");
}

// ============================================================================
// 练习2：FATFS文件系统演示
// ============================================================================

/**
 * @brief 演示FATFS基本概念
 */
void practice_fatfs_concept_demo(void)
{
    printf("=== FATFS文件系统概念演示 ===\r\n");
    
    printf("FATFS特点:\r\n");
    printf("1. 轻量级 - 代码量小，RAM占用少\r\n");
    printf("2. 可移植 - 支持多种存储介质\r\n");
    printf("3. 兼容性 - 与PC的FAT32完全兼容\r\n");
    printf("4. 功能完整 - 支持长文件名、目录操作\r\n");
    
    printf("\nFATFS核心API:\r\n");
    printf("f_mount()   - 挂载/卸载文件系统\r\n");
    printf("f_open()    - 打开文件\r\n");
    printf("f_read()    - 读取文件\r\n");
    printf("f_write()   - 写入文件\r\n");
    printf("f_close()   - 关闭文件\r\n");
    printf("f_mkdir()   - 创建目录\r\n");
    printf("f_opendir() - 打开目录\r\n");
    printf("f_readdir() - 读取目录项\r\n");
    
    printf("\n文件打开模式:\r\n");
    printf("FA_READ        - 只读模式\r\n");
    printf("FA_WRITE       - 写入模式\r\n");
    printf("FA_OPEN_APPEND - 追加模式\r\n");
    printf("FA_CREATE_NEW  - 创建新文件\r\n");
    printf("FA_CREATE_ALWAYS - 总是创建\r\n");
    printf("FA_OPEN_ALWAYS - 总是打开\r\n");
    
    // 检查SD卡状态
    extern uint8_t g_filesystem_mounted;
    printf("\n当前SD卡状态: %s\r\n", g_filesystem_mounted ? "已挂载" : "未挂载");
    
    if (g_filesystem_mounted) {
        // 获取SD卡信息
        FATFS *pfs;
        DWORD free_clusters;
        FRESULT res = f_getfree("0:", &free_clusters, &pfs);
        
        if (res == FR_OK) {
            DWORD total_sectors = (pfs->n_fatent - 2) * pfs->csize;
            DWORD free_sectors = free_clusters * pfs->csize;
            
            printf("SD卡信息:\r\n");
            printf("  总扇区数: %lu\r\n", total_sectors);
            printf("  可用扇区数: %lu\r\n", free_sectors);
            printf("  扇区大小: %d 字节\r\n", 512);  // 通常为512字节
            printf("  总容量: %.2f MB\r\n", (float)(total_sectors * 512) / (1024 * 1024));
            printf("  可用容量: %.2f MB\r\n", (float)(free_sectors * 512) / (1024 * 1024));
        }
    }
}

/**
 * @brief 演示文件操作
 */
void practice_file_operations_demo(void)
{
    printf("=== 文件操作演示 ===\r\n");
    
    extern uint8_t g_filesystem_mounted;
    if (!g_filesystem_mounted) {
        printf("SD卡未挂载，无法演示文件操作\r\n");
        return;
    }
    
    // 测试文件路径和内容
    const char *test_file = "test_demo.txt";
    const char *test_content = "This is a test file for FATFS demo.";
    
    printf("测试文件: %s\r\n", test_file);
    printf("测试内容: %s\r\n", test_content);
    
    // 写入文件
    printf("\n1. 创建并写入文件...\r\n");
    FIL file;
    UINT bytes_written;
    FRESULT res = f_open(&file, test_file, FA_WRITE | FA_CREATE_ALWAYS);
    
    if (res == FR_OK) {
        res = f_write(&file, test_content, strlen(test_content), &bytes_written);
        f_close(&file);
        
        if (res == FR_OK) {
            printf("✓ 文件写入成功，写入 %d 字节\r\n", bytes_written);
        } else {
            printf("✗ 文件写入失败: %d\r\n", res);
            return;
        }
    } else {
        printf("✗ 文件创建失败: %d\r\n", res);
        return;
    }
    
    // 读取文件
    printf("\n2. 读取文件内容...\r\n");
    char read_buffer[128] = {0};
    UINT bytes_read;
    res = f_open(&file, test_file, FA_READ);
    
    if (res == FR_OK) {
        res = f_read(&file, read_buffer, sizeof(read_buffer) - 1, &bytes_read);
        f_close(&file);
        
        if (res == FR_OK) {
            read_buffer[bytes_read] = '\0';
            printf("✓ 文件读取成功，读取 %d 字节\r\n", bytes_read);
            printf("读取内容: %s\r\n", read_buffer);
            
            // 验证内容
            if (strcmp(test_content, read_buffer) == 0) {
                printf("✓ 内容验证成功\r\n");
            } else {
                printf("✗ 内容验证失败\r\n");
            }
        } else {
            printf("✗ 文件读取失败: %d\r\n", res);
        }
    } else {
        printf("✗ 文件打开失败: %d\r\n", res);
    }
    
    // 获取文件信息
    printf("\n3. 获取文件信息...\r\n");
    FILINFO fno;
    res = f_stat(test_file, &fno);
    
    if (res == FR_OK) {
        printf("文件信息:\r\n");
        printf("  文件名: %s\r\n", fno.fname);
        printf("  文件大小: %lu 字节\r\n", fno.fsize);
        printf("  文件属性: 0x%02X\r\n", fno.fattrib);
        printf("  修改日期: %04d-%02d-%02d\r\n", 
               (fno.fdate >> 9) + 1980,  // 年
               (fno.fdate >> 5) & 15,    // 月
               fno.fdate & 31);          // 日
        printf("  修改时间: %02d:%02d:%02d\r\n",
               fno.ftime >> 11,          // 时
               (fno.ftime >> 5) & 63,    // 分
               (fno.ftime & 31) * 2);    // 秒
    }
    
    // 删除测试文件
    printf("\n4. 删除测试文件...\r\n");
    res = f_unlink(test_file);
    if (res == FR_OK) {
        printf("✓ 文件删除成功\r\n");
    } else {
        printf("✗ 文件删除失败: %d\r\n", res);
    }
}

// ============================================================================
// 练习3：多文件夹管理演示
// ============================================================================

/**
 * @brief 演示项目文件夹结构
 */
void practice_folder_structure_demo(void)
{
    printf("=== 项目文件夹结构演示 ===\r\n");
    
    extern uint8_t g_filesystem_mounted;
    if (!g_filesystem_mounted) {
        printf("SD卡未挂载，无法演示文件夹操作\r\n");
        return;
    }
    
    // 项目文件夹结构
    const char *folders[4] = {"sample", "overLimit", "log", "hideData"};
    const char *descriptions[4] = {
        "普通采样数据",
        "超限数据", 
        "操作日志",
        "加密数据"
    };
    
    printf("项目文件夹结构:\r\n");
    printf("SD卡根目录/\r\n");
    
    for (uint8_t i = 0; i < 4; i++) {
        printf("├── %s/\t\t# %s\r\n", folders[i], descriptions[i]);
        
        // 检查文件夹是否存在
        DIR dir;
        FRESULT res = f_opendir(&dir, folders[i]);
        if (res == FR_OK) {
            printf("│   └── [文件夹存在]\r\n");
            
            // 统计文件数量
            FILINFO fno;
            uint16_t file_count = 0;
            while (f_readdir(&dir, &fno) == FR_OK && fno.fname[0] != 0) {
                if (!(fno.fattrib & AM_DIR)) {  // 不是目录
                    file_count++;
                }
            }
            printf("│   └── [包含 %d 个文件]\r\n", file_count);
            f_closedir(&dir);
        } else {
            printf("│   └── [文件夹不存在或无法访问]\r\n");
        }
    }
    
    printf("\n文件命名规则:\r\n");
    printf("sample/    - sampleData{YYYYMMDDHHMMSS}.txt\r\n");
    printf("overLimit/ - overLimit{YYYYMMDDHHMMSS}.txt\r\n");
    printf("log/       - log{序号}.txt (如log0.txt, log1.txt)\r\n");
    printf("hideData/  - hideData{YYYYMMDDHHMMSS}.txt\r\n");
    
    printf("\n数据格式:\r\n");
    printf("sample:    YYYY-MM-DD HH:MM:SS XX.XXV\r\n");
    printf("overLimit: YYYY-MM-DD HH:MM:SS XX.XXV limit XX.XXV\r\n");
    printf("log:       YYYY-MM-DD HH:MM:SS 操作描述\r\n");
    printf("hideData:  原始数据行 + hide: HEX数据行\r\n");
}

/**
 * @brief 演示文件管理器状态
 */
void practice_file_manager_demo(void)
{
    printf("=== 文件管理器状态演示 ===\r\n");
    
    // 文件管理器结构体定义
    printf("文件管理器结构体:\r\n");
    printf("typedef struct {\r\n");
    printf("    char current_filename[64];  // 当前文件名\r\n");
    printf("    uint8_t record_count;       // 当前文件记录数\r\n");
    printf("    uint32_t file_creation_time; // 文件创建时间\r\n");
    printf("} file_manager_t;\r\n");
    
    printf("\n文件管理器数组:\r\n");
    printf("file_manager_t g_file_managers[4];  // 四种数据类型\r\n");
    
    // 模拟文件管理器状态
    typedef struct {
        char current_filename[64];
        uint8_t record_count;
        uint32_t file_creation_time;
    } demo_file_manager_t;
    
    demo_file_manager_t demo_managers[4] = {
        {"sampleData20250107143025.txt", 5, HAL_GetTick()},
        {"overLimit20250107143030.txt", 2, HAL_GetTick()},
        {"log1.txt", 15, HAL_GetTick()},
        {"hideData20250107143035.txt", 8, HAL_GetTick()}
    };
    
    const char *type_names[4] = {"SAMPLE", "OVERLIMIT", "LOG", "HIDEDATA"};
    
    printf("\n当前文件管理器状态:\r\n");
    printf("类型\t\t当前文件\t\t\t记录数\r\n");
    printf("------------------------------------------------------------\r\n");
    
    for (uint8_t i = 0; i < 4; i++) {
        printf("%s\t%s\t%d\r\n", 
               type_names[i], 
               demo_managers[i].current_filename,
               demo_managers[i].record_count);
    }
    
    printf("\n文件切换规则:\r\n");
    printf("1. sample/overLimit/hideData: 达到10条记录时切换\r\n");
    printf("2. log: 基于log_id切换，不限制记录数\r\n");
    printf("3. 文件名为空时自动创建新文件\r\n");
    printf("4. 系统重启时恢复未满文件状态\r\n");
}

// ============================================================================
// 练习4：Flash缓存机制演示
// ============================================================================

/**
 * @brief 演示Flash缓存概念
 */
void practice_flash_cache_concept_demo(void)
{
    printf("=== Flash缓存机制概念演示 ===\r\n");
    
    printf("Flash缓存设计目的:\r\n");
    printf("1. 无SD卡启动时保存重要日志\r\n");
    printf("2. SD卡插入后自动恢复日志\r\n");
    printf("3. 确保系统日志的完整性\r\n");
    printf("4. 提供数据备份机制\r\n");
    
    printf("\nFlash缓存数据结构:\r\n");
    printf("typedef struct {\r\n");
    printf("    uint32_t magic;                    // 魔数标识 0x4C4F4743\r\n");
    printf("    uint32_t count;                    // 缓存的日志数量\r\n");
    printf("    uint32_t next_index;               // 下一个写入位置\r\n");
    printf("    char logs[20][128];                // 日志内容数组\r\n");
    printf("    uint32_t checksum;                 // 校验和\r\n");
    printf("} flash_log_cache_t;\r\n");
    
    printf("\n缓存参数:\r\n");
    printf("最大缓存日志数: 20条\r\n");
    printf("每条日志大小: 128字节\r\n");
    printf("总缓存大小: %d 字节\r\n", 20 * 128 + 16);  // 数据 + 控制信息
    printf("Flash地址: 0x20000 (128KB偏移)\r\n");
    
    printf("\n工作流程:\r\n");
    printf("1. 系统启动检查SD卡状态\r\n");
    printf("2. SD卡不可用时日志写入Flash缓存\r\n");
    printf("3. SD卡插入后检查Flash缓存\r\n");
    printf("4. 有缓存时恢复到log0.txt\r\n");
    printf("5. 恢复完成后清空Flash缓存\r\n");
    
    // 检查当前Flash缓存状态
    uint32_t cached_count = sd_get_cached_log_count();
    printf("\n当前Flash缓存状态:\r\n");
    printf("缓存日志数量: %lu 条\r\n", cached_count);
    
    if (cached_count > 0) {
        printf("状态: 有缓存数据，等待恢复\r\n");
    } else {
        printf("状态: 无缓存数据\r\n");
    }
}

/**
 * @brief 演示数据完整性验证
 */
void practice_data_integrity_demo(void)
{
    printf("=== 数据完整性验证演示 ===\r\n");
    
    printf("校验和算法:\r\n");
    printf("static uint32_t calculate_checksum(const void *data, size_t size)\r\n");
    printf("{\r\n");
    printf("    const uint8_t *bytes = (const uint8_t *)data;\r\n");
    printf("    uint32_t checksum = 0;\r\n");
    printf("    for (size_t i = 0; i < size; i++) {\r\n");
    printf("        checksum += bytes[i];\r\n");
    printf("    }\r\n");
    printf("    return checksum;\r\n");
    printf("}\r\n");
    
    // 演示校验和计算
    const char *test_data = "Hello World!";
    uint32_t checksum = 0;
    for (size_t i = 0; i < strlen(test_data); i++) {
        checksum += (uint8_t)test_data[i];
    }
    
    printf("\n校验和计算示例:\r\n");
    printf("测试数据: \"%s\"\r\n", test_data);
    printf("数据长度: %d 字节\r\n", strlen(test_data));
    printf("计算校验和: 0x%08X (%u)\r\n", checksum, checksum);
    
    printf("\n数据完整性保证措施:\r\n");
    printf("1. 魔数验证 - 确认数据结构有效性\r\n");
    printf("2. 校验和验证 - 检测数据是否损坏\r\n");
    printf("3. 立即同步 - 重要数据立即写入存储\r\n");
    printf("4. 错误恢复 - SD卡故障时自动重新初始化\r\n");
    printf("5. 备份机制 - Flash缓存作为数据备份\r\n");
    
    printf("\n文件同步策略:\r\n");
    printf("普通数据: 写入后调用f_sync(file)\r\n");
    printf("重要日志: 写入后调用f_sync(NULL) - 同步整个文件系统\r\n");
    printf("系统启动: 所有操作完成后统一同步\r\n");
}

// ============================================================================
// 主练习函数
// ============================================================================

/**
 * @brief 第8章所有练习的入口函数
 */
void chapter8_practice_all(void)
{
    printf("\r\n");
    printf("========================================\r\n");
    printf("    第8章：Flash与SD卡存储系统实现\r\n");
    printf("           实践练习开始\r\n");
    printf("========================================\r\n");
    
    practice_flash_info_demo();
    printf("\r\n");
    
    practice_flash_read_write_demo();
    printf("\r\n");
    
    practice_flash_sector_alignment_demo();
    printf("\r\n");
    
    practice_fatfs_concept_demo();
    printf("\r\n");
    
    practice_file_operations_demo();
    printf("\r\n");
    
    practice_folder_structure_demo();
    printf("\r\n");
    
    practice_file_manager_demo();
    printf("\r\n");
    
    practice_flash_cache_concept_demo();
    printf("\r\n");
    
    practice_data_integrity_demo();
    printf("\r\n");
    
    printf("========================================\r\n");
    printf("           实践练习结束\r\n");
    printf("========================================\r\n");
    printf("\r\n");
}
