/**
 * @file    第2章_实践练习与代码示例.c
 * @brief   任务调度器原理与实现详解 - 实践练习代码
 * @details 通过实际代码示例帮助理解时间片轮转调度器的工作原理
 * <AUTHOR>
 * @date    2025-01-07
 */

#include "main.h"
#include "stdio.h"

// ============================================================================
// 练习1：理解task_t结构体和函数指针
// ============================================================================

/**
 * @brief 演示task_t结构体的定义和使用
 */
void practice_task_structure_demo(void)
{
    printf("=== task_t结构体演示 ===\r\n");
    
    // 重新定义task_t结构体（用于演示）
    typedef struct {
        void (*task_func)(void);    // 任务函数指针
        uint32_t rate_ms;           // 任务执行周期（毫秒）
        uint32_t last_run;          // 上次执行时间戳（毫秒）
    } demo_task_t;
    
    // 创建一个任务实例
    demo_task_t my_task;
    
    printf("结构体大小: %d 字节\r\n", sizeof(demo_task_t));
    printf("函数指针大小: %d 字节\r\n", sizeof(my_task.task_func));
    printf("周期变量大小: %d 字节\r\n", sizeof(my_task.rate_ms));
    printf("时间戳大小: %d 字节\r\n", sizeof(my_task.last_run));
    
    // 演示函数指针的使用
    printf("\n--- 函数指针演示 ---\r\n");
    printf("函数指针地址: 0x%08X\r\n", (uint32_t)&my_task.task_func);
    printf("函数指针值: 0x%08X\r\n", (uint32_t)my_task.task_func);
}

/**
 * @brief 示例任务函数1
 */
void demo_task1(void)
{
    static uint32_t counter = 0;
    counter++;
    printf("任务1执行，计数器: %d\r\n", counter);
}

/**
 * @brief 示例任务函数2
 */
void demo_task2(void)
{
    static uint32_t counter = 0;
    counter++;
    printf("任务2执行，计数器: %d\r\n", counter);
}

/**
 * @brief 演示函数指针的赋值和调用
 */
void practice_function_pointer_demo(void)
{
    printf("=== 函数指针使用演示 ===\r\n");
    
    // 定义函数指针变量
    void (*task_ptr)(void);
    
    // 方法1：直接赋值函数名
    task_ptr = demo_task1;
    printf("调用demo_task1: ");
    task_ptr();  // 通过函数指针调用
    
    // 方法2：使用取地址符（效果相同）
    task_ptr = &demo_task2;
    printf("调用demo_task2: ");
    task_ptr();  // 通过函数指针调用
    
    // 演示函数地址
    printf("demo_task1地址: 0x%08X\r\n", (uint32_t)demo_task1);
    printf("demo_task2地址: 0x%08X\r\n", (uint32_t)demo_task2);
}

// ============================================================================
// 练习2：分析调度器执行逻辑
// ============================================================================

/**
 * @brief 简化版调度器演示
 */
typedef struct {
    void (*task_func)(void);
    uint32_t rate_ms;
    uint32_t last_run;
    const char *name;  // 添加任务名称便于调试
} simple_task_t;

// 简化的任务数组
static simple_task_t demo_tasks[] = {
    {demo_task1, 100, 0, "任务1"},   // 100ms周期
    {demo_task2, 200, 0, "任务2"},   // 200ms周期
};

#define DEMO_TASK_NUM (sizeof(demo_tasks) / sizeof(simple_task_t))

/**
 * @brief 演示调度器执行逻辑（带调试信息）
 */
void practice_scheduler_logic_demo(void)
{
    printf("=== 调度器执行逻辑演示 ===\r\n");
    printf("任务数量: %d\r\n", DEMO_TASK_NUM);
    
    // 模拟调度器运行10次
    for (uint8_t loop = 0; loop < 10; loop++) {
        uint32_t now_time = HAL_GetTick();  // 获取当前时间
        
        printf("\n--- 调度循环 %d，当前时间: %d ms ---\r\n", loop + 1, now_time);
        
        // 遍历所有任务
        for (uint8_t i = 0; i < DEMO_TASK_NUM; i++) {
            uint32_t time_since_last = now_time - demo_tasks[i].last_run;
            
            printf("%s: 距离上次执行 %d ms，周期 %d ms", 
                   demo_tasks[i].name, time_since_last, demo_tasks[i].rate_ms);
            
            // 检查是否到了执行时间
            if (now_time >= demo_tasks[i].rate_ms + demo_tasks[i].last_run) {
                printf(" -> 执行\r\n");
                demo_tasks[i].last_run = now_time;  // 更新执行时间
                demo_tasks[i].task_func();          // 执行任务
            } else {
                printf(" -> 跳过\r\n");
            }
        }
        
        HAL_Delay(50);  // 模拟50ms延时
    }
}

// ============================================================================
// 练习3：HAL_GetTick()时间基准分析
// ============================================================================

/**
 * @brief 演示HAL_GetTick()的使用和特性
 */
void practice_hal_gettick_demo(void)
{
    printf("=== HAL_GetTick()时间基准演示 ===\r\n");
    
    // 测试HAL_GetTick()的精度
    uint32_t start_tick = HAL_GetTick();
    printf("开始时间: %d ms\r\n", start_tick);
    
    // 延时测试
    for (uint8_t i = 1; i <= 5; i++) {
        HAL_Delay(100);  // 延时100ms
        uint32_t current_tick = HAL_GetTick();
        uint32_t elapsed = current_tick - start_tick;
        printf("延时 %d 次后，时间: %d ms，累计: %d ms\r\n", i, current_tick, elapsed);
    }
    
    // 演示时间差计算
    printf("\n--- 时间差计算演示 ---\r\n");
    uint32_t time1 = HAL_GetTick();
    HAL_Delay(50);
    uint32_t time2 = HAL_GetTick();
    
    printf("时间1: %d ms\r\n", time1);
    printf("时间2: %d ms\r\n", time2);
    printf("时间差: %d ms\r\n", time2 - time1);
    
    // 演示溢出情况（理论分析）
    printf("\n--- 溢出分析 ---\r\n");
    printf("uint32_t最大值: %u\r\n", UINT32_MAX);
    printf("最大时间: %u ms = %.1f 天\r\n", UINT32_MAX, (float)UINT32_MAX / (1000 * 60 * 60 * 24));
}

// ============================================================================
// 练习4：任务周期设计分析
// ============================================================================

/**
 * @brief 分析不同任务的执行频率
 */
void practice_task_frequency_analysis(void)
{
    printf("=== 任务频率分析 ===\r\n");
    
    // 定义任务周期（来自实际项目）
    uint32_t task_periods[] = {1, 5, 5, 100, 1000, 100};  // ms
    const char *task_names[] = {"LED", "按键", "串口", "ADC", "LED闪烁", "OLED"};
    uint8_t task_count = sizeof(task_periods) / sizeof(uint32_t);
    
    printf("1秒内各任务执行次数分析:\r\n");
    printf("任务名称\t周期(ms)\t1秒执行次数\tCPU占用估算\r\n");
    printf("----------------------------------------------------\r\n");
    
    for (uint8_t i = 0; i < task_count; i++) {
        uint32_t executions_per_second = 1000 / task_periods[i];
        float cpu_usage = (float)executions_per_second * 0.1f;  // 假设每次执行0.1ms
        
        printf("%s\t\t%d\t\t%d\t\t%.1f%%\r\n", 
               task_names[i], task_periods[i], executions_per_second, cpu_usage);
    }
    
    // 计算总CPU占用
    float total_cpu = 0;
    for (uint8_t i = 0; i < task_count; i++) {
        total_cpu += (float)(1000 / task_periods[i]) * 0.1f;
    }
    printf("----------------------------------------------------\r\n");
    printf("总CPU占用估算: %.1f%%\r\n", total_cpu);
}

// ============================================================================
// 练习5：调度器优化技巧
// ============================================================================

/**
 * @brief 演示任务执行时间监控
 */
void practice_task_monitoring_demo(void)
{
    printf("=== 任务执行时间监控演示 ===\r\n");
    
    // 模拟一个耗时任务
    uint32_t start_time = HAL_GetTick();
    
    // 模拟任务执行（延时）
    HAL_Delay(15);  // 模拟15ms的任务执行时间
    
    uint32_t end_time = HAL_GetTick();
    uint32_t execution_time = end_time - start_time;
    
    printf("任务开始时间: %d ms\r\n", start_time);
    printf("任务结束时间: %d ms\r\n", end_time);
    printf("任务执行时间: %d ms\r\n", execution_time);
    
    // 检查是否超时（假设任务周期为100ms，警告阈值为50ms）
    uint32_t task_period = 100;
    uint32_t warning_threshold = task_period / 2;
    
    if (execution_time > warning_threshold) {
        printf("⚠ 警告：任务执行时间过长！\r\n");
        printf("  执行时间: %d ms\r\n", execution_time);
        printf("  警告阈值: %d ms\r\n", warning_threshold);
        printf("  建议：优化任务代码或增加任务周期\r\n");
    } else {
        printf("✓ 任务执行时间正常\r\n");
    }
}

/**
 * @brief 演示动态任务控制
 */
typedef struct {
    void (*task_func)(void);
    uint32_t rate_ms;
    uint32_t last_run;
    uint8_t enabled;        // 任务使能标志
    const char *name;
} enhanced_task_t;

static enhanced_task_t enhanced_tasks[] = {
    {demo_task1, 100, 0, 1, "任务1"},  // 初始使能
    {demo_task2, 200, 0, 0, "任务2"},  // 初始禁用
};

#define ENHANCED_TASK_NUM (sizeof(enhanced_tasks) / sizeof(enhanced_task_t))

/**
 * @brief 使能/禁用任务
 */
void task_enable(uint8_t task_id, uint8_t enable)
{
    if (task_id < ENHANCED_TASK_NUM) {
        enhanced_tasks[task_id].enabled = enable;
        printf("%s %s\r\n", enhanced_tasks[task_id].name, enable ? "已使能" : "已禁用");
    }
}

/**
 * @brief 演示增强型调度器
 */
void practice_enhanced_scheduler_demo(void)
{
    printf("=== 增强型调度器演示 ===\r\n");
    
    // 显示初始状态
    printf("初始任务状态:\r\n");
    for (uint8_t i = 0; i < ENHANCED_TASK_NUM; i++) {
        printf("%s: %s\r\n", enhanced_tasks[i].name, 
               enhanced_tasks[i].enabled ? "使能" : "禁用");
    }
    
    // 运行几个周期
    printf("\n运行调度器...\r\n");
    for (uint8_t loop = 0; loop < 3; loop++) {
        uint32_t now_time = HAL_GetTick();
        
        for (uint8_t i = 0; i < ENHANCED_TASK_NUM; i++) {
            if (enhanced_tasks[i].enabled &&  // 检查任务是否使能
                now_time >= enhanced_tasks[i].rate_ms + enhanced_tasks[i].last_run) {
                
                enhanced_tasks[i].last_run = now_time;
                printf("执行 %s\r\n", enhanced_tasks[i].name);
                enhanced_tasks[i].task_func();
            }
        }
        HAL_Delay(150);
    }
    
    // 动态控制任务
    printf("\n动态控制演示:\r\n");
    task_enable(1, 1);  // 使能任务2
    task_enable(0, 0);  // 禁用任务1
}

// ============================================================================
// 主练习函数
// ============================================================================

/**
 * @brief 第2章所有练习的入口函数
 */
void chapter2_practice_all(void)
{
    printf("\r\n");
    printf("========================================\r\n");
    printf("    第2章：任务调度器原理与实现详解\r\n");
    printf("           实践练习开始\r\n");
    printf("========================================\r\n");
    
    practice_task_structure_demo();
    printf("\r\n");
    
    practice_function_pointer_demo();
    printf("\r\n");
    
    practice_scheduler_logic_demo();
    printf("\r\n");
    
    practice_hal_gettick_demo();
    printf("\r\n");
    
    practice_task_frequency_analysis();
    printf("\r\n");
    
    practice_task_monitoring_demo();
    printf("\r\n");
    
    practice_enhanced_scheduler_demo();
    printf("\r\n");
    
    printf("========================================\r\n");
    printf("           实践练习结束\r\n");
    printf("========================================\r\n");
    printf("\r\n");
}
