#ifndef __SAMPLING_BOARD_APP_H_
#define __SAMPLING_BOARD_APP_H_

#include "stdint.h"
#include "mydefine.h"
#include "gd30ad3344.h"
#include "sampling_types.h"

// 采样板通道定义
typedef enum {
    SAMPLING_BOARD_CH0 = 0,         // 通道0 - 电压测量
    SAMPLING_BOARD_CH1 = 1,         // 通道1 - 电流测量
    SAMPLING_BOARD_CH2 = 2,         // 通道2 - 电阻测量
    SAMPLING_BOARD_CH3 = 3,         // 通道3 - 备用通道
    SAMPLING_BOARD_CH_MAX
} sampling_board_channel_t;

// 测量类型定义
typedef enum {
    MEASURE_TYPE_VOLTAGE = 0,       // 电压测量
    MEASURE_TYPE_CURRENT = 1,       // 电流测量
    MEASURE_TYPE_RESISTANCE = 2     // 电阻测量
} measure_type_t;

// 采样板数据结构
typedef struct {
    float voltage_ch0;              // 通道0电压值
    float voltage_ch1;              // 通道1电压值
    float voltage_ch2;              // 通道2电压值
    float voltage_ch3;              // 通道3电压值
    float current;                  // 计算得出的电流值
    float resistance;               // 计算得出的电阻值
    uint32_t last_update_time;      // 最后更新时间
    uint8_t data_valid;             // 数据有效标志
} sampling_board_data_t;

// 采样板控制结构
typedef struct {
    sampling_state_t state;         // 采样状态
    sampling_cycle_t cycle;         // 采样周期
    uint32_t last_sample_time;      // 上次采样时间
    uint32_t cycle_ms;              // 当前周期毫秒数
    sampling_board_data_t data;     // 采样数据
    uint8_t current_channel;        // 当前采样通道
    measure_type_t measure_mode;    // 测量模式
} sampling_board_control_t;

// 滤波缓冲区大小定义
#define SAMPLING_BOARD_FILTER_SIZE  8

// 分段校准参数 - 基于您的实测数据
typedef struct {
    float input_voltage;    // 输入电压
    float raw_voltage;      // 对应的采集电压
} calibration_point_t;

// 电压校准数据表（已完成，保留备用）
#define FINE_CALIBRATION_POINTS 15    // 精细段：0.00V-0.14V
#define COARSE_CALIBRATION_POINTS 101 // 粗糙段：0.0V-10.0V
extern const calibration_point_t fine_calibration_table[FINE_CALIBRATION_POINTS];
extern const calibration_point_t coarse_calibration_table[COARSE_CALIBRATION_POINTS];

// 电流校准数据表（4-20mA工业标准）
#define CURRENT_CALIBRATION_POINTS 25  // 0mA-24mA，1mA间隔
extern const calibration_point_t current_calibration_table[CURRENT_CALIBRATION_POINTS];

// 电流滤波器配置
#define CURRENT_FILTER_SIZE 8          // 滑动平均滤波器大小
typedef struct {
    float buffer[CURRENT_FILTER_SIZE]; // 滤波缓冲区
    int index;                         // 当前索引
    int count;                         // 有效数据数量
    float sum;                         // 数据总和
} current_filter_t;



// 通道滤波数据结构
typedef struct {
    float buffer[SAMPLING_BOARD_FILTER_SIZE];  // 滤波缓冲区
    uint8_t index;                             // 当前索引
    uint8_t filled;                            // 缓冲区是否填满
} channel_filter_t;

// 全局变量声明
extern sampling_board_control_t g_sampling_board_control;
extern channel_filter_t g_channel_filters[SAMPLING_BOARD_CH_MAX];


// 函数声明
void sampling_board_init(void);                                    // 初始化采样板
void sampling_board_task(void);                                    // 采样板任务
void sampling_board_start_sampling(void);                          // 开始采样
void sampling_board_stop_sampling(void);                           // 停止采样
void sampling_board_set_cycle(sampling_cycle_t cycle);             // 设置采样周期
void sampling_board_process_sample(void);                          // 处理采样数据
float sampling_board_read_channel(sampling_board_channel_t channel); // 读取指定通道
float sampling_board_filter_channel(sampling_board_channel_t channel, float new_value); // 通道滤波
void sampling_board_calculate_derived_values(void);                // 计算衍生值（电流、电阻）
void sampling_board_output_data(void);                             // 输出采样数据
sampling_board_data_t* sampling_board_get_data(void);              // 获取采样数据指针

// 测量模式控制
void sampling_board_set_measure_mode(measure_type_t mode);          // 设置测量模式
measure_type_t sampling_board_get_measure_mode(void);              // 获取测量模式

// 校准和配置
void sampling_board_calibrate_channel(sampling_board_channel_t channel); // 校准通道
void sampling_board_reset_filters(void);                           // 重置滤波器
float voltage_calibrate_segmented(float raw_voltage);              // 电压分段校准函数
float current_calibrate_linear(float raw_voltage);                 // 电流线性校准函数
float current_filter_update(float new_value);                      // 电流滤波函数

#endif /* __SAMPLING_BOARD_APP_H_ */
