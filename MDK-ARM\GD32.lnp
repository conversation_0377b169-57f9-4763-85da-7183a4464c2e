--cpu=Cortex-M4.fp.sp
".\startup_stm32f429xx.o"
".\main.o"
".\gpio.o"
".\adc.o"
".\dma.o"
".\i2c.o"
".\rtc.o"
".\sdio.o"
".\spi.o"
".\tim.o"
".\usart.o"
".\stm32f4xx_it.o"
".\stm32f4xx_hal_msp.o"
".\stm32f4xx_hal_adc.o"
".\stm32f4xx_hal_adc_ex.o"
".\stm32f4xx_ll_adc.o"
".\stm32f4xx_hal_rcc.o"
".\stm32f4xx_hal_rcc_ex.o"
".\stm32f4xx_hal_flash.o"
".\stm32f4xx_hal_flash_ex.o"
".\stm32f4xx_hal_flash_ramfunc.o"
".\stm32f4xx_hal_gpio.o"
".\stm32f4xx_hal_dma_ex.o"
".\stm32f4xx_hal_dma.o"
".\stm32f4xx_hal_pwr.o"
".\stm32f4xx_hal_pwr_ex.o"
".\stm32f4xx_hal_cortex.o"
".\stm32f4xx_hal.o"
".\stm32f4xx_hal_exti.o"
".\stm32f4xx_hal_i2c.o"
".\stm32f4xx_hal_i2c_ex.o"
".\stm32f4xx_hal_rtc.o"
".\stm32f4xx_hal_rtc_ex.o"
".\stm32f4xx_ll_sdmmc.o"
".\stm32f4xx_hal_sd.o"
".\stm32f4xx_hal_mmc.o"
".\stm32f4xx_hal_spi.o"
".\stm32f4xx_hal_tim.o"
".\stm32f4xx_hal_tim_ex.o"
".\stm32f4xx_hal_uart.o"
".\system_stm32f4xx.o"
".\ebtn.o"
".\ringbuffer.o"
".\oled.o"
".\gd25qxx.o"
".\adc_app.o"
".\btn_app.o"
".\flash_app.o"
".\led_app.o"
".\oled_app.o"
".\scheduler.o"
".\usart_app.o"
".\rtc_app.o"
".\config_app.o"
".\selftest_app.o"
".\sd_app.o"
".\gd30ad3344.o"
".\sampling_board_app.o"
".\bsp_driver_sd.o"
".\sd_diskio.o"
".\fatfs.o"
".\diskio.o"
".\ff.o"
".\ff_gen_drv.o"
".\syscall.o"
".\cc936.o"
"..\Middlewares\ST\ARM\DSP\Lib\arm_cortexM4l_math.lib"
--library_type=microlib --strict --scatter ".\GD32.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "GD32.map" -o .\GD32.axf