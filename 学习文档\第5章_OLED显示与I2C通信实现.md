# 第5章：OLED显示与I2C通信实现

## 🎯 学习目标
- 深入理解I2C通信协议的工作原理
- 掌握I2C_HandleTypeDef结构体配置
- 理解OLED显示控制器SSD1306的工作原理
- 学会OLED页面坐标系统和像素映射
- 掌握显示优化技巧减少闪烁

## 📋 目录
1. [I2C基础概念](#1-i2c基础概念)
2. [I2C_HandleTypeDef结构体详解](#2-i2c_handletypedef结构体详解)
3. [OLED显示原理分析](#3-oled显示原理分析)
4. [SSD1306控制器详解](#4-ssd1306控制器详解)
5. [OLED页面坐标系统](#5-oled页面坐标系统)
6. [字符显示算法实现](#6-字符显示算法实现)
7. [显示优化技巧](#7-显示优化技巧)
8. [实践练习](#8-实践练习)

---

## 1. I2C基础概念

### 1.1 什么是I2C？
I2C (Inter-Integrated Circuit) 是一种串行通信协议，由飞利浦公司开发。

**I2C特点：**
- **两线制**: 只需SDA(数据线)和SCL(时钟线)
- **多主多从**: 支持多个主设备和从设备
- **地址寻址**: 通过7位或10位地址识别设备
- **开漏输出**: 需要上拉电阻

### 1.2 I2C信号线
```
主控制器 ←→ 从设备1 ←→ 从设备2 ←→ ... ←→ 从设备N
    ↑           ↑           ↑                 ↑
    └─── SDA ───┴─── SDA ───┴─── ... ─── SDA ─┘
    └─── SCL ───┴─── SCL ───┴─── ... ─── SCL ─┘
         ↑           ↑
      上拉电阻   上拉电阻
```

### 1.3 I2C通信时序
```
起始条件 → 地址字节 → 读写位 → ACK → 数据字节 → ACK → 停止条件
   S    →  7位地址  →  R/W  → A  →  8位数据 → A  →    P

S  = Start (起始条件): SDA从高到低，SCL保持高
P  = Stop (停止条件): SDA从低到高，SCL保持高
A  = ACK (应答): SDA为低电平表示应答
```

---

## 2. I2C_HandleTypeDef结构体详解

### 2.1 结构体定义分析
```c
typedef struct {
    I2C_TypeDef *Instance;          // I2C外设基地址 ⭐
    I2C_InitTypeDef Init;           // 初始化参数 ⭐
    uint8_t *pBuffPtr;             // 数据缓冲区指针
    uint16_t XferSize;             // 传输数据大小
    uint16_t XferCount;            // 传输计数器
    HAL_I2C_StateTypeDef State;     // I2C状态
    uint32_t ErrorCode;            // 错误代码
} I2C_HandleTypeDef;
```

### 2.2 项目中的I2C配置
```c
// 来自i2c.c的配置
I2C_HandleTypeDef hi2c1;

void MX_I2C1_Init(void)
{
    hi2c1.Instance = I2C1;                          // 使用I2C1外设
    hi2c1.Init.ClockSpeed = 400000;                 // 时钟速度400kHz
    hi2c1.Init.DutyCycle = I2C_DUTYCYCLE_2;         // 占空比2:1
    hi2c1.Init.OwnAddress1 = 0;                     // 主机地址(不重要)
    hi2c1.Init.AddressingMode = I2C_ADDRESSINGMODE_7BIT; // 7位地址模式
    hi2c1.Init.DualAddressMode = I2C_DUALADDRESS_DISABLE; // 禁用双地址
    hi2c1.Init.OwnAddress2 = 0;                     // 第二地址
    hi2c1.Init.GeneralCallMode = I2C_GENERALCALL_DISABLE; // 禁用广播
    hi2c1.Init.NoStretchMode = I2C_NOSTRETCH_DISABLE;     // 允许时钟拉伸
    
    HAL_I2C_Init(&hi2c1);
}
```

### 2.3 配置参数详解

#### 2.3.1 时钟速度
```c
hi2c1.Init.ClockSpeed = 400000;  // 400kHz快速模式
```
**I2C速度模式：**
- 标准模式：100kHz
- 快速模式：400kHz ⭐ (项目使用)
- 快速模式+：1MHz
- 高速模式：3.4MHz

#### 2.3.2 占空比
```c
hi2c1.Init.DutyCycle = I2C_DUTYCYCLE_2;  // 2:1占空比
```
**占空比说明：**
- I2C_DUTYCYCLE_2：SCL低电平:高电平 = 2:1
- I2C_DUTYCYCLE_16_9：SCL低电平:高电平 = 16:9

#### 2.3.3 地址模式
```c
hi2c1.Init.AddressingMode = I2C_ADDRESSINGMODE_7BIT;  // 7位地址
```
**地址格式：**
- 7位地址：0x00 ~ 0x7F (128个地址)
- 10位地址：0x000 ~ 0x3FF (1024个地址)

---

## 3. OLED显示原理分析

### 3.1 OLED基本概念
OLED (Organic Light-Emitting Diode) 有机发光二极管显示器。

**OLED特点：**
- **自发光**: 无需背光，对比度高
- **低功耗**: 黑色像素不发光
- **快响应**: 响应时间极短
- **宽视角**: 几乎无视角限制

### 3.2 项目OLED规格
```c
// 来自oled.h的定义
#define OLED_ADDR   0x78    // I2C地址
#define OLED_WIDTH  128     // 宽度128像素
#define OLED_HEIGHT 32      // 高度32像素
```

**硬件规格：**
- 尺寸：0.91英寸
- 分辨率：128×32像素
- 控制器：SSD1306
- 接口：I2C
- 颜色：单色(白色)

### 3.3 OLED内存映射
```
OLED显示内存布局 (128×32像素)
┌─────────────────────────────────────────────────────────┐
│ Page 0 (8像素高) │ 0,0  0,1  0,2  ... 0,127 │ 第1行字符 │
├─────────────────────────────────────────────────────────┤
│ Page 1 (8像素高) │ 1,0  1,1  1,2  ... 1,127 │ 第2行字符 │
├─────────────────────────────────────────────────────────┤
│ Page 2 (8像素高) │ 2,0  2,1  2,2  ... 2,127 │ 第3行字符 │
├─────────────────────────────────────────────────────────┤
│ Page 3 (8像素高) │ 3,0  3,1  3,2  ... 3,127 │ 第4行字符 │
└─────────────────────────────────────────────────────────┘
```

---

## 4. SSD1306控制器详解

### 4.1 SSD1306初始化序列
```c
// 来自oled.c的初始化命令
uint8_t initcmd1[] = {
    0xAE,       // 关闭显示
    0xD5, 0x80, // 设置显示时钟分频比/振荡器频率
    0xA8, 0x1F, // 设置多路复用比(32-1)
    0xD3, 0x00, // 设置显示偏移
    0x40,       // 设置显示起始行
    0x8D, 0x14, // 设置电荷泵
    0xA1,       // 设置段重映射
    0xC8,       // 设置COM输出扫描方向
    0xDA, 0x00, // 设置COM引脚硬件配置
    0x81, 0x80, // 设置对比度控制
    0xD9, 0x1F, // 设置预充电周期
    0xDB, 0x40, // 设置VCOM取消选择级别
    0xA4,       // 设置整个显示开/关
    0xAF,       // 开启显示
};
```

### 4.2 关键命令解析

#### 4.2.1 显示控制命令
```c
0xAE  // 关闭显示 (Display OFF)
0xAF  // 开启显示 (Display ON)
0xA4  // 正常显示 (Normal Display)
0xA5  // 全屏点亮 (Entire Display ON)
```

#### 4.2.2 地址设置命令
```c
0x40  // 设置显示起始行 (0x40-0x7F)
0xA1  // 设置段重映射 (0xA0/0xA1)
0xC8  // 设置COM扫描方向 (0xC0/0xC8)
```

#### 4.2.3 对比度控制
```c
0x81, 0x80  // 设置对比度 (0x81 + 0x00~0xFF)
```

---

## 5. OLED页面坐标系统

### 5.1 坐标系统说明
```
OLED坐标系统 (128×32)
    X轴 →
Y   0   1   2   3   ...  127
轴  ┌───┬───┬───┬───┬─────┬───┐
↓ 0 │   │   │   │   │     │   │ ← Page 0 (像素行 0-7)
  1 │   │   │   │   │     │   │ ← Page 1 (像素行 8-15)
  2 │   │   │   │   │     │   │ ← Page 2 (像素行 16-23)
  3 │   │   │   │   │     │   │ ← Page 3 (像素行 24-31)
    └───┴───┴───┴───┴─────┴───┘
```

### 5.2 位置设置函数
```c
void OLED_Set_Position(uint8_t x, uint8_t y)
{
    OLED_Write_cmd(0xb0 + y);                    // 设置页地址 (0xB0-0xB3)
    OLED_Write_cmd(((x & 0xf0) >> 4) | 0x10);   // 设置列地址高4位
    OLED_Write_cmd((x & 0x0f) | 0x01);          // 设置列地址低4位
}
```

**命令解析：**
- `0xB0 + y`：设置页地址 (y=0~3)
- `0x10 + (x>>4)`：设置列地址高4位
- `0x01 + (x&0x0F)`：设置列地址低4位

### 5.3 数据写入函数
```c
// 写入命令
void OLED_Write_cmd(uint8_t cmd)
{
    HAL_I2C_Mem_Write(&hi2c1, 0x78, 0x00, I2C_MEMADD_SIZE_8BIT, &cmd, 1, 0x100);
}

// 写入数据
void OLED_Write_data(uint8_t data)
{
    HAL_I2C_Mem_Write(&hi2c1, 0x78, 0x40, I2C_MEMADD_SIZE_8BIT, &data, 1, 0x100);
}
```

**参数说明：**
- `0x78`：OLED的I2C地址
- `0x00`：命令寄存器地址
- `0x40`：数据寄存器地址
- `0x100`：超时时间

---

## 6. 字符显示算法实现

### 6.1 字符显示函数
```c
void OLED_ShowChar(uint8_t x, uint8_t y, uint8_t ch, uint8_t fontsize)
{
    uint8_t c = ch - ' ';  // 计算字符在字库中的索引
    
    if (fontsize == 16) {  // 16号字体 (8×16像素)
        OLED_Set_Position(x, y);
        for (uint8_t i = 0; i < 8; i++) {
            OLED_Write_data(F8X16[c * 16 + i]);  // 写入上半部分
        }
        OLED_Set_Position(x, y + 1);
        for (uint8_t i = 0; i < 8; i++) {
            OLED_Write_data(F8X16[c * 16 + i + 8]);  // 写入下半部分
        }
    }
}
```

### 6.2 字符串显示函数
```c
void OLED_ShowStr(uint8_t x, uint8_t y, char *ch, uint8_t fontsize)
{
    uint8_t c = 0, i = 0, j = 0;
    
    switch (fontsize) {
        case 16:  // 16号字体
            while (ch[j] != '\0') {
                c = ch[j] - 32;  // ASCII码转换
                if (x > 120) {   // 换行处理
                    x = 0;
                    y += 2;      // 16号字体占2页
                }
                OLED_Set_Position(x, y);
                for (i = 0; i < 8; i++) {
                    OLED_Write_data(F8X16[c * 16 + i]);
                }
                OLED_Set_Position(x, y + 1);
                for (i = 0; i < 8; i++) {
                    OLED_Write_data(F8X16[c * 16 + i + 8]);
                }
                x += 8;  // 移动到下一个字符位置
                j++;
            }
            break;
    }
}
```

### 6.3 格式化输出函数
```c
// 项目中的自定义printf函数
int Oled_Printf(uint8_t x, uint8_t y, const char *format, ...)
{
    char buffer[128];
    va_list arg;
    int len;

    va_start(arg, format);
    len = vsnprintf(buffer, sizeof(buffer), format, arg);  // 格式化字符串
    va_end(arg);

    OLED_ShowStr(x, y, buffer, 16);  // 显示到OLED
    return len;
}
```

---

## 7. 显示优化技巧

### 7.1 状态检测优化
```c
void oled_task(void)
{
    static sampling_state_t last_state = SAMPLING_IDLE;     // 记录上次状态
    static uint32_t last_cycle_ms = 0;                      // 记录上次采样周期
    static float last_display_voltage = -1.0f;             // 记录上次显示的电压值

    // 检查状态或采样周期是否发生变化，如果变化则清屏
    if (g_adc_control.state != last_state || g_adc_control.cycle_ms != last_cycle_ms) {
        OLED_Clear();  // 清屏操作
        last_state = g_adc_control.state;      // 更新状态记录
        last_cycle_ms = g_adc_control.cycle_ms; // 更新周期记录
        last_display_voltage = -1.0f;          // 重置电压记录，强制刷新
    }
}
```

**优化要点：**
- ✅ 只有状态改变时才清屏
- ✅ 避免不必要的全屏刷新
- ✅ 减少闪烁现象

### 7.2 局部刷新优化
```c
// 检查电压值是否发生变化，如果变化则清除第二行区域
if (last_display_voltage != g_adc_control.display_voltage) {
    // 清除第二行显示区域 (Page 2-3)
    for (uint8_t page = 2; page <= 3; page++) {
        OLED_Set_Position(0, page);
        for (uint8_t x = 0; x < 128; x++) {
            OLED_Write_data(0);  // 写入0清除像素
        }
    }
    last_display_voltage = g_adc_control.display_voltage;
}
```

**优化要点：**
- ✅ 只清除需要更新的区域
- ✅ 保留不变的显示内容
- ✅ 提高刷新效率

### 7.3 显示内容设计
```c
if (g_adc_control.state == SAMPLING_ACTIVE) {
    // 采样状态：显示时间和电压值
    Oled_Printf(32, 0, "%02d:%02d:%02d",    // 时间显示在第1行
               current_time.Hours,
               current_time.Minutes,
               current_time.Seconds);

    Oled_Printf(36, 2, "%.2fV", g_adc_control.display_voltage);  // 电压显示在第3行

} else {
    Oled_Printf(20, 1, "system idle");     // 空闲状态显示
}
```

**设计要点：**
- ✅ 合理布局显示内容
- ✅ 重要信息突出显示
- ✅ 状态信息清晰明确

---

## 8. 实践练习

### 练习1：I2C通信测试
测试I2C通信的基本功能。

### 练习2：OLED基本显示
实现字符和数字的显示功能。

### 练习3：动态显示优化
实现高效的动态内容更新。

---

## 📝 本章小结

通过本章学习，您应该掌握：

✅ **I2C通信原理** - 两线制协议、地址寻址、时序要求
✅ **I2C_HandleTypeDef配置** - 400kHz快速模式、7位地址等参数
✅ **OLED显示原理** - SSD1306控制器、页面坐标系统
✅ **字符显示算法** - 字库映射、像素写入、格式化输出
✅ **显示优化技巧** - 状态检测、局部刷新、减少闪烁

**下一章预告：** 我们将学习RTC时间管理与时间戳处理的实现。

---

## 🔗 相关文件
- `sysFunction/oled_app.c` - OLED显示实现
- `Components/oled/oled.h` - OLED驱动头文件
- `Components/oled/oled.c` - OLED驱动实现
- `Core/Src/i2c.c` - I2C底层配置
