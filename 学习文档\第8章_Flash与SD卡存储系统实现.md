# 第8章：Flash与SD卡存储系统实现

## 🎯 学习目标
- 深入理解SPI Flash的工作原理和操作方法
- 掌握FATFS文件系统的使用和配置
- 理解多文件夹管理和数据分类存储策略
- 学会Flash缓存机制和数据备份方案
- 掌握文件命名规则和数据格式设计

## 📋 目录
1. [SPI Flash基础概念](#1-spi-flash基础概念)
2. [GD25QXX芯片驱动分析](#2-gd25qxx芯片驱动分析)
3. [FATFS文件系统详解](#3-fatfs文件系统详解)
4. [多文件夹管理策略](#4-多文件夹管理策略)
5. [数据分类存储设计](#5-数据分类存储设计)
6. [Flash缓存机制实现](#6-flash缓存机制实现)
7. [文件命名与格式规范](#7-文件命名与格式规范)
8. [数据完整性保证](#8-数据完整性保证)
9. [实践练习](#9-实践练习)

---

## 1. SPI Flash基础概念

### 1.1 什么是SPI Flash？
SPI Flash是一种使用SPI接口的非易失性存储器，具有擦写次数多、速度快、功耗低的特点。

**SPI Flash特点：**
- **非易失性**: 断电后数据不丢失
- **块擦除**: 必须先擦除再写入
- **页编程**: 按页(256字节)写入数据
- **高速度**: SPI接口支持高速传输

### 1.2 项目中的GD25QXX规格
```c
// 来自项目配置
#define SPI_FLASH_PAGE_SIZE 0x100        // 256字节页大小
总容量: 16MB (16 * 1024 * 1024)
扇区大小: 4KB (4096字节)
块大小: 64KB
页大小: 256字节
```

### 1.3 SPI Flash存储层次
```
芯片 (16MB)
├── 块 (64KB) × 256个
│   ├── 扇区 (4KB) × 16个
│   │   ├── 页 (256B) × 16个
│   │   │   └── 字节 × 256个
```

---

## 2. GD25QXX芯片驱动分析

### 2.1 SPI接口配置
```c
// CS片选信号控制
#define SPI_FLASH_CS_LOW()  HAL_GPIO_WritePin(GPIOB, GPIO_PIN_12, GPIO_PIN_RESET)
#define SPI_FLASH_CS_HIGH() HAL_GPIO_WritePin(GPIOB, GPIO_PIN_12, GPIO_PIN_SET)
```

**SPI信号线：**
- **CS (PB12)**: 片选信号，低电平有效
- **SCK**: 时钟信号
- **MOSI**: 主机输出从机输入
- **MISO**: 主机输入从机输出

### 2.2 基本操作函数

#### 2.2.1 扇区擦除
```c
void spi_flash_sector_erase(uint32_t sector_addr)
{
    // 1. 使能写操作
    spi_flash_write_enable();
    
    // 2. 发送扇区擦除命令
    SPI_FLASH_CS_LOW();
    spi_flash_send_byte(0x20);  // 扇区擦除命令
    spi_flash_send_byte((sector_addr >> 16) & 0xFF);  // 地址高字节
    spi_flash_send_byte((sector_addr >> 8) & 0xFF);   // 地址中字节
    spi_flash_send_byte(sector_addr & 0xFF);          // 地址低字节
    SPI_FLASH_CS_HIGH();
    
    // 3. 等待擦除完成
    spi_flash_wait_for_write_end();
}
```

#### 2.2.2 页编程
```c
void spi_flash_page_write(uint8_t *pbuffer, uint32_t write_addr, uint16_t num_byte_to_write)
{
    // 1. 使能写操作
    spi_flash_write_enable();
    
    // 2. 发送页编程命令
    SPI_FLASH_CS_LOW();
    spi_flash_send_byte(0x02);  // 页编程命令
    spi_flash_send_byte((write_addr >> 16) & 0xFF);
    spi_flash_send_byte((write_addr >> 8) & 0xFF);
    spi_flash_send_byte(write_addr & 0xFF);
    
    // 3. 写入数据
    for (uint16_t i = 0; i < num_byte_to_write; i++) {
        spi_flash_send_byte(pbuffer[i]);
    }
    SPI_FLASH_CS_HIGH();
    
    // 4. 等待写入完成
    spi_flash_wait_for_write_end();
}
```

#### 2.2.3 数据读取
```c
void spi_flash_buffer_read(uint8_t *pbuffer, uint32_t read_addr, uint16_t num_byte_to_read)
{
    SPI_FLASH_CS_LOW();
    
    // 发送读取命令
    spi_flash_send_byte(0x03);  // 读取命令
    spi_flash_send_byte((read_addr >> 16) & 0xFF);
    spi_flash_send_byte((read_addr >> 8) & 0xFF);
    spi_flash_send_byte(read_addr & 0xFF);
    
    // 读取数据
    for (uint16_t i = 0; i < num_byte_to_read; i++) {
        pbuffer[i] = spi_flash_send_byte(0xFF);  // 发送虚拟字节读取数据
    }
    
    SPI_FLASH_CS_HIGH();
}
```

### 2.3 项目中的Flash应用层封装
```c
// Flash直接操作接口
flash_result_t flash_direct_write(uint32_t addr, const void* data, size_t size)
{
    flash_direct_erase_sector(addr);  // 先擦除扇区
    spi_flash_buffer_write((uint8_t*)data, addr, (uint16_t)size);  // 再写入数据
    return FLASH_OK;
}

flash_result_t flash_direct_read(uint32_t addr, void* data, size_t size)
{
    spi_flash_buffer_read((uint8_t*)data, addr, (uint16_t)size);
    return FLASH_OK;
}

flash_result_t flash_direct_erase_sector(uint32_t addr)
{
    uint32_t sector_addr = addr & 0xFFFFF000;  // 4KB扇区对齐
    spi_flash_sector_erase(sector_addr);
    return FLASH_OK;
}
```

---

## 3. FATFS文件系统详解

### 3.1 FATFS基础概念
FATFS是一个专为小型嵌入式系统设计的FAT文件系统模块。

**FATFS特点：**
- **轻量级**: 代码量小，RAM占用少
- **可移植**: 支持多种存储介质
- **兼容性**: 与PC的FAT32完全兼容
- **功能完整**: 支持长文件名、目录操作

### 3.2 FATFS核心API

#### 3.2.1 文件系统挂载
```c
FATFS g_fs;  // 文件系统对象

// 挂载文件系统
FRESULT f_mount(FATFS* fs, const TCHAR* path, BYTE opt);

// 项目中的使用
FRESULT res = f_mount(&g_fs, "0:", 1);  // 立即挂载SD卡
if (res == FR_OK) {
    g_filesystem_mounted = 1;
} else if (res == FR_NO_FILESYSTEM) {
    // 文件系统不存在，尝试格式化
    BYTE sfd = 1;
    UINT au = 0;
    BYTE bpData[512];
    res = f_mkfs("0:", sfd, au, bpData, 512);
}
```

#### 3.2.2 文件操作
```c
// 打开文件
FRESULT f_open(FIL* fp, const TCHAR* path, BYTE mode);

// 写入文件
FRESULT f_write(FIL* fp, const void* buff, UINT btw, UINT* bw);

// 读取文件
FRESULT f_read(FIL* fp, void* buff, UINT btr, UINT* br);

// 关闭文件
FRESULT f_close(FIL* fp);

// 项目中的使用示例
FIL file;
UINT bytes_written;
FRESULT res = f_open(&file, "sample/data.txt", FA_WRITE | FA_OPEN_APPEND);
if (res == FR_OK) {
    res = f_write(&file, data, strlen(data), &bytes_written);
    f_close(&file);
}
```

#### 3.2.3 目录操作
```c
// 创建目录
FRESULT f_mkdir(const TCHAR* path);

// 打开目录
FRESULT f_opendir(DIR* dp, const TCHAR* path);

// 读取目录项
FRESULT f_readdir(DIR* dp, FILINFO* fno);

// 项目中的目录创建
static const char *g_folder_names[4] = {
    "sample",      // 普通测量数据
    "overLimit",   // 超限数据
    "log",         // 操作日志
    "hideData"     // 加密数据
};

for (uint8_t i = 0; i < 4; i++) {
    f_mkdir(g_folder_names[i]);
}
```

---

## 4. 多文件夹管理策略

### 4.1 文件夹结构设计
```
SD卡根目录/
├── sample/          # 普通采样数据
│   ├── sampleData20250107143025.txt
│   └── sampleData20250107143125.txt
├── overLimit/       # 超限数据
│   ├── overLimit20250107143030.txt
│   └── overLimit20250107143130.txt
├── log/            # 操作日志
│   ├── log0.txt
│   ├── log1.txt
│   └── log2.txt
└── hideData/       # 加密数据
    ├── hideData20250107143035.txt
    └── hideData20250107143135.txt
```

### 4.2 数据类型枚举
```c
typedef enum {
    DATA_TYPE_SAMPLE = 0,    // 普通采样数据
    DATA_TYPE_OVERLIMIT,     // 超限数据
    DATA_TYPE_LOG,           // 操作日志
    DATA_TYPE_HIDEDATA       // 加密数据
} data_type_t;
```

### 4.3 文件管理器设计
```c
typedef struct {
    char current_filename[MAX_FILENAME_LEN];  // 当前文件名
    uint8_t record_count;                     // 当前文件记录数
    uint32_t file_creation_time;              // 文件创建时间
} file_manager_t;

file_manager_t g_file_managers[4];  // 四种数据类型的文件管理器
```

### 4.4 文件切换逻辑
```c
FRESULT sd_check_and_create_new_file(data_type_t type)
{
    file_manager_t *manager = &g_file_managers[type];
    uint8_t need_new_file = 0;

    if (type == DATA_TYPE_LOG) {
        // log文件：基于log_id切换
        char expected_filename[MAX_FILENAME_LEN];
        snprintf(expected_filename, sizeof(expected_filename), "log%lu.txt", g_log_id_manager.log_id);
        if (strcmp(manager->current_filename, expected_filename) != 0) {
            need_new_file = 1;
        }
    } else {
        // 其他文件：达到10条记录时切换
        need_new_file = (manager->record_count >= MAX_RECORDS_PER_FILE || 
                        strlen(manager->current_filename) == 0);
    }

    if (need_new_file) {
        // 生成新文件名并更新管理器
        sd_generate_filename(type, manager->current_filename, MAX_FILENAME_LEN);
        manager->record_count = 0;
        manager->file_creation_time = HAL_GetTick();
    }

    return FR_OK;
}
```

---

## 5. 数据分类存储设计

### 5.1 数据格式定义

#### 5.1.1 普通采样数据格式
```
格式：YYYY-MM-DD HH:MM:SS XX.XXV
示例：2025-01-07 14:30:25 3.25V
```

#### 5.1.2 超限数据格式
```
格式：YYYY-MM-DD HH:MM:SS XX.XXV limit XX.XXV
示例：2025-01-07 14:30:25 5.50V limit 3.00V
```

#### 5.1.3 日志数据格式
```
格式：YYYY-MM-DD HH:MM:SS 操作描述
示例：2025-01-07 10:00:01 system init
```

#### 5.1.4 加密数据格式（双行）
```
格式：原始数据行 + 加密数据行
示例：
2025-01-07 14:30:25 3.25V
hide: 677A5B1A0D0C0000
```

### 5.2 数据格式化函数

#### 5.2.1 采样数据格式化
```c
static FRESULT format_sample_data(float voltage, char *formatted_data)
{
    RTC_TimeTypeDef current_rtc_time = {0};
    RTC_DateTypeDef current_rtc_date = {0};
    HAL_RTC_GetTime(&hrtc, &current_rtc_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_rtc_date, RTC_FORMAT_BIN);

    sprintf(formatted_data, "%04d-%02d-%02d %02d:%02d:%02d %.2fV",
            current_rtc_date.Year + 2000,
            current_rtc_date.Month,
            current_rtc_date.Date,
            current_rtc_time.Hours,
            current_rtc_time.Minutes,
            current_rtc_time.Seconds,
            voltage);

    return FR_OK;
}
```

#### 5.2.2 加密数据格式化
```c
static FRESULT format_hidedata(float voltage, uint8_t is_overlimit, char *formatted_data)
{
    // 第一行：原始数据
    char original_line[128];
    sprintf(original_line, "%04d-%02d-%02d %02d:%02d:%02d %.2fV", ...);

    // 第二行：HEX加密数据
    uint32_t timestamp = rtc_convert_to_unix_timestamp(&current_rtc_time, &current_rtc_date);
    hex_data_t hex_data;
    hex_data.timestamp = timestamp;
    hex_data.voltage_integer = (uint16_t)voltage;
    hex_data.voltage_decimal = (uint16_t)((voltage - hex_data.voltage_integer) * 65536.0f);

    char hex_output[32];
    adc_format_hex_string(&hex_data, hex_output, sizeof(hex_output));

    // 组合双行格式
    sprintf(formatted_data, "%s\r\nhide: %s", original_line, hex_output);
    return FR_OK;
}
```

---

## 6. Flash缓存机制实现

### 6.1 Flash缓存设计原理
当SD卡不可用时，将重要日志缓存到Flash中，SD卡插入后自动恢复。

### 6.2 Flash缓存数据结构
```c
#define MAX_CACHED_LOGS 20
#define LOG_ENTRY_SIZE 128
#define FLASH_LOG_CACHE_MAGIC 0x4C4F4743  // "LOGC"

typedef struct {
    uint32_t magic;                                    // 魔数标识
    uint32_t count;                                    // 缓存的日志数量
    uint32_t next_index;                               // 下一个写入位置
    char logs[MAX_CACHED_LOGS][LOG_ENTRY_SIZE];        // 日志内容
    uint32_t checksum;                                 // 校验和
} flash_log_cache_t;
```

### 6.3 Flash缓存操作

#### 6.3.1 缓存日志到Flash
```c
FRESULT sd_cache_log_to_flash(const char *log_data)
{
    flash_log_cache_t cache;

    // 读取现有缓存
    if (flash_direct_read(FLASH_LOG_CACHE_ADDR, &cache, sizeof(cache)) != FLASH_OK) {
        // 初始化新缓存
        cache.magic = FLASH_LOG_CACHE_MAGIC;
        cache.count = 0;
        cache.next_index = 0;
        memset(cache.logs, 0, sizeof(cache.logs));
    }

    // 验证魔数
    if (cache.magic != FLASH_LOG_CACHE_MAGIC) {
        cache.magic = FLASH_LOG_CACHE_MAGIC;
        cache.count = 0;
        cache.next_index = 0;
        memset(cache.logs, 0, sizeof(cache.logs));
    }

    // 检查空间
    if (cache.count >= MAX_CACHED_LOGS) {
        return FR_DENIED;  // 缓存已满
    }

    // 添加新日志
    strncpy(cache.logs[cache.next_index], log_data, LOG_ENTRY_SIZE - 1);
    cache.logs[cache.next_index][LOG_ENTRY_SIZE - 1] = '\0';

    cache.count++;
    cache.next_index = (cache.next_index + 1) % MAX_CACHED_LOGS;

    // 计算校验和
    cache.checksum = calculate_checksum(&cache, sizeof(cache) - sizeof(cache.checksum));

    // 写入Flash
    return (flash_direct_write(FLASH_LOG_CACHE_ADDR, &cache, sizeof(cache)) == FLASH_OK) ? 
           FR_OK : FR_DISK_ERR;
}
```

#### 6.3.2 从Flash恢复日志
```c
FRESULT sd_restore_logs_from_flash(void)
{
    flash_log_cache_t cache;

    // 读取Flash缓存
    if (flash_direct_read(FLASH_LOG_CACHE_ADDR, &cache, sizeof(cache)) != FLASH_OK) {
        return FR_DISK_ERR;
    }

    // 验证数据完整性
    if (cache.magic != FLASH_LOG_CACHE_MAGIC) {
        return FR_OK;  // 无有效缓存
    }

    uint32_t expected_checksum = calculate_checksum(&cache, sizeof(cache) - sizeof(cache.checksum));
    if (cache.checksum != expected_checksum) {
        return FR_DISK_ERR;  // 数据损坏
    }

    // 逐条恢复日志到SD卡
    for (uint32_t i = 0; i < cache.count; i++) {
        uint32_t log_index = (cache.next_index - cache.count + i + MAX_CACHED_LOGS) % MAX_CACHED_LOGS;
        
        if (strlen(cache.logs[log_index]) > 0) {
            FRESULT result = sd_write_data(DATA_TYPE_LOG, cache.logs[log_index]);
            if (result != FR_OK) {
                return result;
            }
        }
    }

    return FR_OK;
}
```

---

## 7. 文件命名与格式规范

### 7.1 文件命名规则

#### 7.1.1 时间戳命名
```c
void sd_get_datetime_string(char *datetime_str, size_t str_size)
{
    RTC_TimeTypeDef current_rtc_time = {0};
    RTC_DateTypeDef current_rtc_date = {0};
    HAL_RTC_GetTime(&hrtc, &current_rtc_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_rtc_date, RTC_FORMAT_BIN);

    // 格式：YYYYMMDDHHMMSS
    snprintf(datetime_str, str_size, "%04d%02d%02d%02d%02d%02d",
            current_rtc_date.Year + 2000,
            current_rtc_date.Month,
            current_rtc_date.Date,
            current_rtc_time.Hours,
            current_rtc_time.Minutes,
            current_rtc_time.Seconds);
}
```

#### 7.1.2 文件名生成
```c
void sd_generate_filename(data_type_t type, char *filename, size_t filename_size)
{
    static const char *prefixes[4] = {
        "sampleData",   // 普通数据
        "overLimit",    // 超限数据
        "log",          // 日志
        "hideData"      // 加密数据
    };

    if (type == DATA_TYPE_LOG) {
        // log文件使用序号命名
        snprintf(filename, filename_size, "%s%lu.txt", 
                prefixes[type], g_log_id_manager.log_id);
    } else {
        // 其他文件使用时间戳命名
        char datetime_str[16];
        sd_get_datetime_string(datetime_str, sizeof(datetime_str));
        snprintf(filename, filename_size, "%s%s.txt", 
                prefixes[type], datetime_str);
    }
}
```

### 7.2 HEX数据格式
```c
typedef struct {
    uint32_t timestamp;        // Unix时间戳
    uint16_t voltage_integer;  // 电压整数部分
    uint16_t voltage_decimal;  // 电压小数部分
} hex_data_t;

void adc_format_hex_string(const hex_data_t *hex_data, char *buffer, size_t buffer_size)
{
    // 格式化为16进制字符串：TTTTTTTTVIIIDD00
    snprintf(buffer, buffer_size, "%08X%04X%04X0000",
             hex_data->timestamp,
             hex_data->voltage_integer,
             hex_data->voltage_decimal);
}
```

---

## 8. 数据完整性保证

### 8.1 校验和机制
```c
static uint32_t calculate_checksum(const void *data, size_t size)
{
    const uint8_t *bytes = (const uint8_t *)data;
    uint32_t checksum = 0;
    for (size_t i = 0; i < size; i++) {
        checksum += bytes[i];
    }
    return checksum;
}
```

### 8.2 文件同步策略
```c
// 重要数据立即同步
if (strstr(log_content, "system init") != NULL ||
    strstr(log_content, "test ok") != NULL) {
    f_sync(NULL);  // 同步整个文件系统
}
```

### 8.3 错误恢复机制
```c
uint8_t sd_check_and_reinit_if_needed(void)
{
    // 检查文件系统状态
    if (g_filesystem_mounted) {
        FATFS *pfs;
        DWORD free_clusters;
        if (f_getfree("0:", &free_clusters, &pfs) == FR_OK) {
            return 1;  // 正常工作
        }
    }

    // 重新初始化SD卡
    for (int attempt = 0; attempt < 5; attempt++) {
        HAL_Delay(300);
        
        if (HAL_SD_Init(&hsd) == HAL_OK) {
            if (f_mount(&g_fs, "0:", 1) == FR_OK) {
                g_filesystem_mounted = 1;
                return 1;  // 恢复成功
            }
        }
    }
    
    return 0;  // 恢复失败
}
```

---

## 9. 实践练习

### 练习1：SPI Flash基本操作
实现Flash的读写和擦除功能。

### 练习2：FATFS文件操作
创建文件夹和文件，实现数据读写。

### 练习3：Flash缓存系统
设计并实现Flash缓存机制。

---

## 📝 本章小结

通过本章学习，您应该掌握：

✅ **SPI Flash操作** - 扇区擦除、页编程、数据读取的实现方法
✅ **FATFS文件系统** - 挂载、文件操作、目录管理的使用技巧
✅ **多文件夹管理** - 数据分类存储和文件切换策略
✅ **Flash缓存机制** - 无SD卡时的数据缓存和恢复方案
✅ **数据完整性** - 校验和验证、错误恢复、文件同步机制

**下一章预告：** 我们将学习系统集成与调试技巧的实现。

---

## 🔗 相关文件
- `sysFunction/flash_app.c` - Flash存储实现
- `sysFunction/sd_app.c` - SD卡存储实现
- `Components/GD25QXX/gd25qxx.h` - Flash驱动头文件
