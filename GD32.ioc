#MicroXplorer Configuration settings - do not modify
ADC1.Channel-3\#ChannelRegularConversion=ADC_CHANNEL_10
ADC1.Channel-5\#ChannelRegularConversion=ADC_CHANNEL_5
ADC1.ContinuousConvMode=DISABLE
ADC1.DMAContinuousRequests=DISABLE
ADC1.EnableAnalogWatchDog=false
ADC1.ExternalTrigConv=ADC_EXTERNALTRIGCONV_T3_TRGO
ADC1.IPParameters=Rank-3\#ChannelRegularConversion,master,Channel-3\#ChannelRegularConversion,SamplingTime-3\#ChannelRegularConversion,NbrOfConversionFlag,DMAContinuousRequests,ContinuousConvMode,ExternalTrigConv,ScanConvMode,NbrOfConversion,Rank-5\#ChannelRegularConversion,Channel-5\#ChannelRegularConversion,SamplingTime-5\#ChannelRegularConversion,EnableAnalogWatchDog
ADC1.NbrOfConversion=2
ADC1.NbrOfConversionFlag=1
ADC1.Rank-3\#ChannelRegularConversion=1
ADC1.Rank-5\#ChannelRegularConversion=2
ADC1.SamplingTime-3\#ChannelRegularConversion=ADC_SAMPLETIME_3CYCLES
ADC1.SamplingTime-5\#ChannelRegularConversion=ADC_SAMPLETIME_3CYCLES
ADC1.ScanConvMode=ENABLE
ADC1.master=1
CAD.formats=
CAD.pinconfig=
CAD.provider=
Dma.ADC1.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC1.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.ADC1.1.Instance=DMA2_Stream4
Dma.ADC1.1.MemDataAlignment=DMA_MDATAALIGN_WORD
Dma.ADC1.1.MemInc=DMA_MINC_ENABLE
Dma.ADC1.1.Mode=DMA_NORMAL
Dma.ADC1.1.PeriphDataAlignment=DMA_PDATAALIGN_WORD
Dma.ADC1.1.PeriphInc=DMA_PINC_DISABLE
Dma.ADC1.1.Priority=DMA_PRIORITY_LOW
Dma.ADC1.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.Request0=USART1_RX
Dma.Request1=ADC1
Dma.Request2=SPI1_TX
Dma.Request3=SPI1_RX
Dma.Request4=USART2_RX
Dma.Request5=USART2_TX
Dma.RequestsNb=6
Dma.SPI1_RX.3.Direction=DMA_PERIPH_TO_MEMORY
Dma.SPI1_RX.3.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.SPI1_RX.3.Instance=DMA2_Stream0
Dma.SPI1_RX.3.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.SPI1_RX.3.MemInc=DMA_MINC_ENABLE
Dma.SPI1_RX.3.Mode=DMA_NORMAL
Dma.SPI1_RX.3.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.SPI1_RX.3.PeriphInc=DMA_PINC_DISABLE
Dma.SPI1_RX.3.Priority=DMA_PRIORITY_LOW
Dma.SPI1_RX.3.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.SPI1_TX.2.Direction=DMA_MEMORY_TO_PERIPH
Dma.SPI1_TX.2.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.SPI1_TX.2.Instance=DMA2_Stream3
Dma.SPI1_TX.2.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.SPI1_TX.2.MemInc=DMA_MINC_ENABLE
Dma.SPI1_TX.2.Mode=DMA_NORMAL
Dma.SPI1_TX.2.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.SPI1_TX.2.PeriphInc=DMA_PINC_DISABLE
Dma.SPI1_TX.2.Priority=DMA_PRIORITY_LOW
Dma.SPI1_TX.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART1_RX.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART1_RX.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART1_RX.0.Instance=DMA2_Stream2
Dma.USART1_RX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_RX.0.MemInc=DMA_MINC_ENABLE
Dma.USART1_RX.0.Mode=DMA_NORMAL
Dma.USART1_RX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_RX.0.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_RX.0.Priority=DMA_PRIORITY_LOW
Dma.USART1_RX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART2_RX.4.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART2_RX.4.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART2_RX.4.Instance=DMA1_Stream5
Dma.USART2_RX.4.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART2_RX.4.MemInc=DMA_MINC_ENABLE
Dma.USART2_RX.4.Mode=DMA_NORMAL
Dma.USART2_RX.4.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART2_RX.4.PeriphInc=DMA_PINC_DISABLE
Dma.USART2_RX.4.Priority=DMA_PRIORITY_LOW
Dma.USART2_RX.4.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART2_TX.5.Direction=DMA_MEMORY_TO_PERIPH
Dma.USART2_TX.5.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART2_TX.5.Instance=DMA1_Stream6
Dma.USART2_TX.5.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART2_TX.5.MemInc=DMA_MINC_ENABLE
Dma.USART2_TX.5.Mode=DMA_NORMAL
Dma.USART2_TX.5.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART2_TX.5.PeriphInc=DMA_PINC_DISABLE
Dma.USART2_TX.5.Priority=DMA_PRIORITY_LOW
Dma.USART2_TX.5.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
FATFS.IPParameters=_CODE_PAGE,_USE_LFN
FATFS._CODE_PAGE=936
FATFS._USE_LFN=3
File.Version=6
GPIO.groupedBy=Group By Peripherals
I2C1.I2C_Mode=I2C_Fast
I2C1.IPParameters=I2C_Mode
KeepUserPlacement=false
Mcu.CPN=STM32F429ZGT6
Mcu.Family=STM32F4
Mcu.IP0=ADC1
Mcu.IP1=DMA
Mcu.IP10=SYS
Mcu.IP11=TIM3
Mcu.IP12=TIM6
Mcu.IP13=TIM14
Mcu.IP14=USART1
Mcu.IP15=USART2
Mcu.IP2=FATFS
Mcu.IP3=I2C1
Mcu.IP4=NVIC
Mcu.IP5=RCC
Mcu.IP6=RTC
Mcu.IP7=SDIO
Mcu.IP8=SPI1
Mcu.IP9=SPI2
Mcu.IPNb=16
Mcu.Name=STM32F429Z(E-G)Tx
Mcu.Package=LQFP144
Mcu.Pin0=PE2
Mcu.Pin1=PC14/OSC32_IN
Mcu.Pin10=PA5
Mcu.Pin11=PA6
Mcu.Pin12=PA7
Mcu.Pin13=PB0
Mcu.Pin14=PE7
Mcu.Pin15=PE9
Mcu.Pin16=PE11
Mcu.Pin17=PE13
Mcu.Pin18=PE15
Mcu.Pin19=PB12
Mcu.Pin2=PC15/OSC32_OUT
Mcu.Pin20=PB13
Mcu.Pin21=PB14
Mcu.Pin22=PB15
Mcu.Pin23=PD8
Mcu.Pin24=PD9
Mcu.Pin25=PD10
Mcu.Pin26=PD11
Mcu.Pin27=PD12
Mcu.Pin28=PD13
Mcu.Pin29=PC8
Mcu.Pin3=PH0/OSC_IN
Mcu.Pin30=PC9
Mcu.Pin31=PA9
Mcu.Pin32=PA10
Mcu.Pin33=PA13
Mcu.Pin34=PA14
Mcu.Pin35=PC10
Mcu.Pin36=PC11
Mcu.Pin37=PC12
Mcu.Pin38=PD2
Mcu.Pin39=PB8
Mcu.Pin4=PH1/OSC_OUT
Mcu.Pin40=PB9
Mcu.Pin41=VP_FATFS_VS_SDIO
Mcu.Pin42=VP_RTC_VS_RTC_Activate
Mcu.Pin43=VP_RTC_VS_RTC_Calendar
Mcu.Pin44=VP_SYS_VS_Systick
Mcu.Pin45=VP_TIM3_VS_ClockSourceINT
Mcu.Pin46=VP_TIM6_VS_ClockSourceINT
Mcu.Pin47=VP_TIM14_VS_ClockSourceINT
Mcu.Pin48=VP_STMicroelectronics.X-CUBE-ALGOBUILD_VS_DSPOoLibraryJjLibrary_1.3.0_1.3.0
Mcu.Pin5=PC0
Mcu.Pin6=PA1
Mcu.Pin7=PA2
Mcu.Pin8=PA3
Mcu.Pin9=PA4
Mcu.PinsNb=49
Mcu.ThirdParty0=STMicroelectronics.X-CUBE-ALGOBUILD.1.3.0
Mcu.ThirdPartyNb=1
Mcu.UserConstants=
Mcu.UserName=STM32F429ZGTx
MxCube.Version=6.14.1
MxDb.Version=DB.6.0.141
NVIC.ADC_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA1_Stream5_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA1_Stream6_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream0_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream2_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream3_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream4_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SPI2_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:0\:0\:true\:false\:true\:false\:true\:false
NVIC.TIM8_TRG_COM_TIM14_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART2_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA1.Locked=true
PA1.Signal=GPIO_Output
PA10.Locked=true
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA2.Locked=true
PA2.Mode=Asynchronous
PA2.Signal=USART2_TX
PA3.Locked=true
PA3.Mode=Asynchronous
PA3.Signal=USART2_RX
PA4.Locked=true
PA4.Signal=GPIO_Output
PA5.Locked=true
PA5.Mode=Full_Duplex_Master
PA5.Signal=SPI1_SCK
PA6.Locked=true
PA6.Mode=Full_Duplex_Master
PA6.Signal=SPI1_MISO
PA7.Locked=true
PA7.Mode=Full_Duplex_Master
PA7.Signal=SPI1_MOSI
PA9.Locked=true
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB0.Locked=true
PB0.Signal=GPIO_Input
PB12.Locked=true
PB12.Signal=GPIO_Output
PB13.Locked=true
PB13.Mode=Full_Duplex_Master
PB13.Signal=SPI2_SCK
PB14.Locked=true
PB14.Mode=Full_Duplex_Master
PB14.Signal=SPI2_MISO
PB15.Locked=true
PB15.Mode=Full_Duplex_Master
PB15.Signal=SPI2_MOSI
PB8.Locked=true
PB8.Mode=I2C
PB8.Signal=I2C1_SCL
PB9.Locked=true
PB9.Mode=I2C
PB9.Signal=I2C1_SDA
PC0.Locked=true
PC0.Signal=ADCx_IN10
PC10.Mode=SD_4_bits_Wide_bus
PC10.Signal=SDIO_D2
PC11.Mode=SD_4_bits_Wide_bus
PC11.Signal=SDIO_D3
PC12.Mode=SD_4_bits_Wide_bus
PC12.Signal=SDIO_CK
PC14/OSC32_IN.Mode=LSE-External-Oscillator
PC14/OSC32_IN.Signal=RCC_OSC32_IN
PC15/OSC32_OUT.Mode=LSE-External-Oscillator
PC15/OSC32_OUT.Signal=RCC_OSC32_OUT
PC8.Mode=SD_4_bits_Wide_bus
PC8.Signal=SDIO_D0
PC9.Mode=SD_4_bits_Wide_bus
PC9.Signal=SDIO_D1
PD10.GPIOParameters=PinState,GPIO_PuPd,GPIO_ModeDefaultOutputPP
PD10.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PD10.GPIO_PuPd=GPIO_PULLUP
PD10.Locked=true
PD10.PinState=GPIO_PIN_SET
PD10.Signal=GPIO_Output
PD11.GPIOParameters=PinState,GPIO_PuPd,GPIO_ModeDefaultOutputPP
PD11.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PD11.GPIO_PuPd=GPIO_PULLUP
PD11.Locked=true
PD11.PinState=GPIO_PIN_SET
PD11.Signal=GPIO_Output
PD12.GPIOParameters=PinState,GPIO_PuPd,GPIO_ModeDefaultOutputPP
PD12.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PD12.GPIO_PuPd=GPIO_PULLUP
PD12.Locked=true
PD12.PinState=GPIO_PIN_SET
PD12.Signal=GPIO_Output
PD13.GPIOParameters=PinState,GPIO_PuPd,GPIO_ModeDefaultOutputPP
PD13.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PD13.GPIO_PuPd=GPIO_PULLUP
PD13.Locked=true
PD13.PinState=GPIO_PIN_SET
PD13.Signal=GPIO_Output
PD2.Mode=SD_4_bits_Wide_bus
PD2.Signal=SDIO_CMD
PD8.GPIOParameters=PinState,GPIO_PuPd,GPIO_ModeDefaultOutputPP
PD8.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PD8.GPIO_PuPd=GPIO_PULLUP
PD8.Locked=true
PD8.PinState=GPIO_PIN_SET
PD8.Signal=GPIO_Output
PD9.GPIOParameters=PinState,GPIO_PuPd,GPIO_ModeDefaultOutputPP
PD9.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PD9.GPIO_PuPd=GPIO_PULLUP
PD9.Locked=true
PD9.PinState=GPIO_PIN_SET
PD9.Signal=GPIO_Output
PE11.Locked=true
PE11.Signal=GPIO_Input
PE13.Locked=true
PE13.Signal=GPIO_Input
PE15.Locked=true
PE15.Signal=GPIO_Input
PE2.Locked=true
PE2.Signal=SPI4_SCK
PE7.Locked=true
PE7.Signal=GPIO_Input
PE9.Locked=true
PE9.Signal=GPIO_Input
PH0/OSC_IN.Mode=HSE-External-Oscillator
PH0/OSC_IN.Signal=RCC_OSC_IN
PH1/OSC_OUT.Mode=HSE-External-Oscillator
PH1/OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F429ZGTx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.1
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x1000
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=false
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=GD32.ioc
ProjectManager.ProjectName=GD32
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x2000
ProjectManager.TargetToolchain=MDK-ARM V5
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_USART1_UART_Init-USART1-false-HAL-true,5-MX_ADC1_Init-ADC1-false-HAL-true,6-MX_TIM3_Init-TIM3-false-HAL-true,7-MX_TIM6_Init-TIM6-false-HAL-true,8-MX_TIM14_Init-TIM14-false-HAL-true,9-MX_I2C1_Init-I2C1-false-HAL-true,10-MX_SPI2_Init-SPI2-false-HAL-true,11-MX_SDIO_SD_Init-SDIO-false-HAL-true,12-MX_FATFS_Init-FATFS-false-HAL-false,13-MX_RTC_Init-RTC-false-HAL-true,14-MX_SPI1_Init-SPI1-false-HAL-true,15-MX_USART2_UART_Init-USART2-false-HAL-true
RCC.48MHZClocksFreq_Value=48000000
RCC.AHBFreq_Value=120000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=30000000
RCC.APB1TimFreq_Value=60000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=60000000
RCC.APB2TimFreq_Value=120000000
RCC.CortexFreq_Value=120000000
RCC.EthernetFreq_Value=120000000
RCC.FCLKCortexFreq_Value=120000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=120000000
RCC.HSE_VALUE=25000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=160000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LCDTFTFreq_Value,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQ,PLLQCLKFreq_Value,PLLSourceVirtual,RCC_RTC_Clock_Source,RCC_RTC_Clock_SourceVirtual,RTCFreq_Value,RTCHSEDivFreq_Value,SAI_AClocksFreq_Value,SAI_BClocksFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VCOSAIOutputFreq_Value,VCOSAIOutputFreq_ValueQ,VCOSAIOutputFreq_ValueR,VcooutputI2S,VcooutputI2SQ
RCC.LCDTFTFreq_Value=20416666.666666668
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=120000000
RCC.PLLCLKFreq_Value=120000000
RCC.PLLM=15
RCC.PLLN=144
RCC.PLLQ=5
RCC.PLLQCLKFreq_Value=48000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.RCC_RTC_Clock_Source=RCC_RTCCLKSOURCE_LSE
RCC.RCC_RTC_Clock_SourceVirtual=RCC_RTCCLKSOURCE_LSE
RCC.RTCFreq_Value=32768
RCC.RTCHSEDivFreq_Value=12500000
RCC.SAI_AClocksFreq_Value=20416666.666666668
RCC.SAI_BClocksFreq_Value=20416666.666666668
RCC.SYSCLKFreq_VALUE=120000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=320000000
RCC.VCOInputFreq_Value=1666666.6666666667
RCC.VCOOutputFreq_Value=240000000
RCC.VCOSAIOutputFreq_Value=81666666.66666667
RCC.VCOSAIOutputFreq_ValueQ=20416666.666666668
RCC.VCOSAIOutputFreq_ValueR=40833333.333333336
RCC.VcooutputI2S=160000000
RCC.VcooutputI2SQ=160000000
RTC.Date=14
RTC.Format=RTC_FORMAT_BIN
RTC.Hours=23
RTC.IPParameters=Format,Hours,Minutes,Seconds,Year,Date,Month
RTC.Minutes=59
RTC.Month=RTC_MONTH_JUNE
RTC.Seconds=50
RTC.Year=25
SH.ADCx_IN10.0=ADC1_IN10,IN10
SH.ADCx_IN10.ConfNb=1
SPI1.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_8
SPI1.CLKPhase=SPI_PHASE_2EDGE
SPI1.CalculateBaudRate=7.5 MBits/s
SPI1.Direction=SPI_DIRECTION_2LINES
SPI1.IPParameters=VirtualType,Mode,Direction,CalculateBaudRate,BaudRatePrescaler,CLKPhase
SPI1.Mode=SPI_MODE_MASTER
SPI1.VirtualType=VM_MASTER
SPI2.CLKPhase=SPI_PHASE_2EDGE
SPI2.CLKPolarity=SPI_POLARITY_HIGH
SPI2.CalculateBaudRate=15.0 MBits/s
SPI2.Direction=SPI_DIRECTION_2LINES
SPI2.IPParameters=VirtualType,Mode,Direction,CalculateBaudRate,CLKPolarity,CLKPhase
SPI2.Mode=SPI_MODE_MASTER
SPI2.VirtualType=VM_MASTER
STMicroelectronics.X-CUBE-ALGOBUILD.1.3.0.DSPOoLibraryJjLibrary_Checked=true
STMicroelectronics.X-CUBE-ALGOBUILD.1.3.0.IPParameters=LibraryCcDSPOoLibraryJjDSPOoLibrary
STMicroelectronics.X-CUBE-ALGOBUILD.1.3.0.LibraryCcDSPOoLibraryJjDSPOoLibrary=true
STMicroelectronics.X-CUBE-ALGOBUILD.1.3.0_SwParameter=LibraryCcDSPOoLibraryJjDSPOoLibrary\:true;
TIM14.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM14.IPParameters=Prescaler,Period,AutoReloadPreload
TIM14.Period=5000-1
TIM14.Prescaler=180-1
TIM3.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM3.IPParameters=Period,TIM_MasterOutputTrigger,AutoReloadPreload,Prescaler
TIM3.Period=100-1
TIM3.Prescaler=180-1
TIM3.TIM_MasterOutputTrigger=TIM_TRGO_UPDATE
TIM6.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM6.IPParameters=Period,TIM_MasterOutputTrigger,Prescaler,AutoReloadPreload
TIM6.Period=100-1
TIM6.Prescaler=180-1
TIM6.TIM_MasterOutputTrigger=TIM_TRGO_UPDATE
USART1.BaudRate=460800
USART1.IPParameters=VirtualMode,BaudRate
USART1.VirtualMode=VM_ASYNC
USART2.BaudRate=9600
USART2.IPParameters=VirtualMode,BaudRate
USART2.VirtualMode=VM_ASYNC
VP_FATFS_VS_SDIO.Mode=SDIO
VP_FATFS_VS_SDIO.Signal=FATFS_VS_SDIO
VP_RTC_VS_RTC_Activate.Mode=RTC_Enabled
VP_RTC_VS_RTC_Activate.Signal=RTC_VS_RTC_Activate
VP_RTC_VS_RTC_Calendar.Mode=RTC_Calendar
VP_RTC_VS_RTC_Calendar.Signal=RTC_VS_RTC_Calendar
VP_STMicroelectronics.X-CUBE-ALGOBUILD_VS_DSPOoLibraryJjLibrary_1.3.0_1.3.0.Mode=DSPOoLibraryJjLibrary
VP_STMicroelectronics.X-CUBE-ALGOBUILD_VS_DSPOoLibraryJjLibrary_1.3.0_1.3.0.Signal=STMicroelectronics.X-CUBE-ALGOBUILD_VS_DSPOoLibraryJjLibrary_1.3.0_1.3.0
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM14_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM14_VS_ClockSourceINT.Signal=TIM14_VS_ClockSourceINT
VP_TIM3_VS_ClockSourceINT.Mode=Internal
VP_TIM3_VS_ClockSourceINT.Signal=TIM3_VS_ClockSourceINT
VP_TIM6_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM6_VS_ClockSourceINT.Signal=TIM6_VS_ClockSourceINT
board=custom
