#include "usart_app.h"
#include "rtc_app.h"
#include "config_app.h"
#include "selftest_app.h"
#include "adc_app.h"
#include "sd_app.h"
#include "scheduler.h"
#include "binary_protocol.h"
#include "stdlib.h"
#include "stdarg.h"
#include "string.h"
#include "stdio.h"
#include "usart.h"  // 包含USART2定义
#include "main.h"   // 包含uwTick定义
#include "gpio.h"   // 包含GPIO定义

// 内联实现二进制协议函数，避免链接问题
#include "binary_protocol.c"
#include "usart.h"
#include "mydefine.h"

uint16_t uart_rx_index = 0; // RX position
uint32_t uart_rx_ticks = 0; // RX time
uint8_t uart_rx_buffer[128] = {0}; // Temp buffer
uint8_t uart_rx_dma_buffer[128] = {0}; // DMA buffer
uint8_t uart_dma_buffer[128] = {0}; // Process buffer
uint8_t uart_flag = 0; // Status flag
struct rt_ringbuffer uart_ringbuffer; // Ring buffer
uint8_t ringbuffer_pool[128]; // Buffer pool

typedef enum {
    UART_STATE_NORMAL, // Normal command mode
    UART_STATE_RTC_CONFIG, // Wait for time input
    UART_STATE_RATIO_INPUT, // Wait for ratio input
    UART_STATE_LIMIT_INPUT // Wait for limit input
} uart_state_t;

static uart_state_t uart_state = UART_STATE_NORMAL;

static const cmd_entry_t cmd_table[] = { // All supported commands
    {"test", CMD_TEST, handle_test_cmd}, // Full test
    {"test simple", CMD_TEST_SIMPLE, handle_test_simple_cmd}, // Simple test
    {"sd test", CMD_SD_TEST, handle_sd_test_cmd}, // SD card test
    {"sd detect", CMD_SD_DETECT, handle_sd_detect_cmd}, // Re-detect SD card
    {"sd format", CMD_SD_FORMAT, handle_sd_format_cmd}, // Format SD card
    {"RTC Config", CMD_RTC_CONFIG, handle_rtc_config_cmd}, // Set time
    {"RTC now", CMD_RTC_NOW, handle_rtc_now_cmd}, // Show time
    {"conf delete", CMD_CONF_DELETE, handle_conf_delete_cmd}, // Delete config
    {"conf test", CMD_CONF_TEST, handle_conf_test_cmd}, // Test config
    {"conf fix", CMD_CONF_FIX, handle_conf_fix_cmd}, // Fix config
    {"conf", CMD_CONF, handle_conf_cmd}, // Read config
    {"config save", CMD_CONFIG_SAVE, handle_config_save_cmd}, // Save config
    {"config read", CMD_CONFIG_READ, handle_config_read_cmd}, // Read config
    {"ratio", CMD_RATIO, handle_ratio_cmd}, // Set ratio
    {"limit", CMD_LIMIT, handle_limit_cmd}, // Set limit
    {"start", CMD_START, handle_start_cmd}, // Start sampling
    {"stop", CMD_STOP, handle_stop_cmd}, // Stop sampling
    {"hide", CMD_HIDE, handle_hide_cmd}, // Enable encryption
    {"unhide", CMD_UNHIDE, handle_unhide_cmd}, // Disable encryption
    {"system status", CMD_SYSTEM_STATUS, handle_system_status_cmd}, // Show status
    {"reset boot", CMD_RESET_BOOT, handle_reset_boot_cmd}, // Reset boot count
    {"reset stage", CMD_RESET_STAGE, handle_reset_stage_cmd}, // Reset test stage
    {"recover sd", CMD_RECOVER_SD, handle_recover_sd_cmd}, // SD card recovery
    {"check state", CMD_CHECK_STATE, handle_check_state_cmd}, // Check state consistency
    {"log validate", CMD_LOG_VALIDATE, handle_log_validate_cmd}, // Validate log files
    {"test simulate", CMD_TEST_SIMULATE, handle_test_simulate_cmd}, // Simulate test sequence
    {"test report", CMD_TEST_REPORT, handle_test_report_cmd}, // Generate test report
    {"debug state", CMD_DEBUG_STATE, handle_debug_state_cmd}, // Detailed system state
    {"sd sync", CMD_SD_SYNC, handle_sd_sync_cmd}, // Force filesystem sync
    {"sb start", CMD_SB_START, handle_sb_start_cmd}, // Start sampling board
    {"sb stop", CMD_SB_STOP, handle_sb_stop_cmd}, // Stop sampling board
    {"sb mode", CMD_SB_MODE, handle_sb_mode_cmd}, // Set sampling board mode
    {"sb read", CMD_SB_READ, handle_sb_read_cmd}, // Read sampling board data
    {"sb check", CMD_SB_CHECK, handle_sb_check_cmd}, // Check calibration accuracy
    // 竞赛专用命令
    {"get_device_id", CMD_GET_DEVICE_ID, handle_get_device_id_cmd}, // Get device ID
    {"set_device_id", CMD_SET_DEVICE_ID, handle_set_device_id_cmd}, // Set device ID
    {"get_RTC", CMD_GET_RTC, handle_get_rtc_cmd}, // Get RTC time
    {"set_RTC", CMD_SET_RTC, handle_set_rtc_cmd}, // Set RTC time
    {"get_ratio", CMD_GET_RATIO, handle_get_ratio_cmd}, // Get ratio
    {"set_ratio", CMD_SET_RATIO, handle_set_ratio_cmd}, // Set ratio
    {"get_limit", CMD_GET_LIMIT, handle_get_limit_cmd}, // Get limit
    {"set_limit", CMD_SET_LIMIT, handle_set_limit_cmd}, // Set limit
    {"get_data", CMD_GET_DATA, handle_get_data_cmd}, // Get data
    {"start_sample", CMD_START_SAMPLE, handle_start_sample_cmd}, // Start continuous sampling
    {"stop_sample", CMD_STOP_SAMPLE, handle_stop_sample_cmd}, // Stop continuous sampling
    {"set_interval", CMD_SET_INTERVAL, handle_set_interval_cmd}, // Set sampling interval
    {"uart_test", CMD_UART_TEST, handle_uart_test_cmd}, // UART switch test
    {NULL, CMD_NONE, NULL} // End marker
};

// 新增：RS485发送函数（带控制引脚）
static void rs485_transmit(UART_HandleTypeDef *huart, uint8_t *data, uint16_t size, uint32_t timeout)
{
#if UART_SELECT == 2
	RS485_TX_ENABLE();  // 切换到发送模式
	HAL_Delay(1);       // 短暂延时确保切换完成
#endif

	HAL_UART_Transmit(huart, data, size, timeout);

#if UART_SELECT == 2
	HAL_Delay(1);       // 确保数据发送完成
	RS485_RX_ENABLE();  // 切换回接收模式
#endif
}

// 新增：自动选择串口的printf函数
int uart_printf(const char *format, ...) // 自动选择串口的printf
{
	char buffer[512];
	va_list arg;
	int len;
	va_start(arg, format);
	len = vsnprintf(buffer, sizeof(buffer), format, arg);
	va_end(arg);
#if UART_SELECT == 1
	HAL_UART_Transmit(&huart1, (uint8_t *)buffer, (uint16_t)len, 0xFF);
#elif UART_SELECT == 2
	rs485_transmit(&huart2, (uint8_t *)buffer, (uint16_t)len, 0xFF);
#endif
	return len;
}

// 兼容性函数：保持原有my_printf接口
int my_printf(UART_HandleTypeDef *huart, const char *format, ...)
{
	char buffer[512];
	va_list arg;
	int len;
	va_start(arg, format);
	len = vsnprintf(buffer, sizeof(buffer), format, arg);
	va_end(arg);
#if UART_SELECT == 1
	HAL_UART_Transmit(&huart1, (uint8_t *)buffer, (uint16_t)len, 0xFF);
#elif UART_SELECT == 2
	rs485_transmit(&huart2, (uint8_t *)buffer, (uint16_t)len, 0xFF);
#endif
	return len;
}

void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
#if UART_SELECT == 1
	if (huart->Instance == USART1)
#elif UART_SELECT == 2
	if (huart->Instance == USART2)
#endif
	{
		uart_rx_ticks = uwTick;
		uart_rx_index++;
#if UART_SELECT == 1
		HAL_UART_Receive_IT(&huart1, &uart_rx_buffer[uart_rx_index], 1);
#elif UART_SELECT == 2
		HAL_UART_Receive_IT(&huart2, &uart_rx_buffer[uart_rx_index], 1);
#endif
	}
}

/**
 * @brief 串口DMA接收完成事件回调函数
 * @param huart 串口句柄
 * @param Size 表示本次事件中DMA已经成功接收了多少字节的数据
 * @retval None
 */
void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
	// 1. ȷ����Ŀ�괮�� (USART1)
#if UART_SELECT == 1
	if (huart->Instance == USART1)
#elif UART_SELECT == 2
	if (huart->Instance == USART2)
#endif
	{
		// 2. ����ֹͣ��ǰ�� DMA ���� (������ڽ�����)
		//    ��Ϊ�����ж���ζ�ŷ��ͷ��Ѿ�ֹͣ����ֹ DMA �����ȴ������
		HAL_UART_DMAStop(huart);

		rt_ringbuffer_put(&uart_ringbuffer, uart_rx_dma_buffer, Size);

		// 5. ��� DMA ���ջ�������Ϊ�´ν�����׼��
		//    ��Ȼ memcpy ֻ������ Size �������������������������
		memset(uart_rx_dma_buffer, 0, sizeof(uart_rx_dma_buffer));

		// 6. **�ؼ�������������һ�� DMA ���н���**
		//    �����ٴε��ã�����ֻ�������һ��
#if UART_SELECT == 1
		HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));
		__HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT); // 禁用半传输中断
#elif UART_SELECT == 2
		HAL_UARTEx_ReceiveToIdle_DMA(&huart2, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));
		__HAL_DMA_DISABLE_IT(&hdma_usart2_rx, DMA_IT_HT); // 禁用半传输中断
#endif
	}
}


void uart_task(void)
{
	uint16_t length;

	length = rt_ringbuffer_data_len(&uart_ringbuffer);

	if (length == 0)
		return;

	rt_ringbuffer_get(&uart_ringbuffer, uart_dma_buffer, length);

	// Call command parsing function
	parse_uart_command(uart_dma_buffer, length);

	// Clear buffer
	memset(uart_dma_buffer, 0, sizeof(uart_dma_buffer));
}

/**
 * @brief Parse command type
 * @param cmd_str Command string
 * @retval Command type
 */
cmd_type_t parse_command_type(char *cmd_str)
{
    if (cmd_str == NULL) return CMD_NONE;

    // Remove leading/trailing spaces
    while (*cmd_str == ' ' || *cmd_str == '\t') cmd_str++;

    // Add debug info
    extern UART_HandleTypeDef huart1;
    extern int my_printf(UART_HandleTypeDef *huart, const char *format, ...);
    // my_printf(&huart1, "DEBUG: Parsing command: '%s'\r\n", cmd_str);

    // Traverse command table to find matching command
    for (int i = 0; cmd_table[i].cmd_str != NULL; i++) {
        size_t cmd_len = strlen(cmd_table[i].cmd_str);
        if (strncmp(cmd_str, cmd_table[i].cmd_str, cmd_len) == 0) {
            // Check next character after match to ensure complete match
            char next_char = cmd_str[cmd_len];
            if (next_char == '\0' || next_char == ' ' || next_char == '\t' || next_char == '=' || next_char == ':') {
                // my_printf(&huart1, "DEBUG: Matched '%s' -> cmd_type=%d\r\n", cmd_table[i].cmd_str, cmd_table[i].cmd_type);
                return cmd_table[i].cmd_type;
            }
        }
    }

    // Debug output commented out for standard format - my_printf(&huart1, "DEBUG: No match found\r\n");
    return CMD_NONE;
}

/**
 * @brief 解析串口指令（支持状态机处理）
 * @param buffer 接收到的数据缓冲区
 * @param length 数据长度
 * @retval None
 */
void parse_uart_command(uint8_t *buffer, uint16_t length)
{
    // 确保字符串以null结尾
    if (length < sizeof(uart_dma_buffer))
        buffer[length] = '\0';
    else
        buffer[sizeof(uart_dma_buffer) - 1] = '\0';

    // 去除换行符和回车符
    char *cmd_str = (char *)buffer;
    char *end = cmd_str + strlen(cmd_str) - 1;
    while (end >= cmd_str && (*end == '\r' || *end == '\n' || *end == ' ')) {
        *end = '\0';
        end--;
    }

    // 如果是空字符串，直接返回
    if (strlen(cmd_str) == 0) return;

    // 调试输出：显示接收到的命令 (已关闭，符合测评要求)
    // my_printf(&huart1, "DEBUG: Received command: '%s' (length=%d)\r\n", cmd_str, strlen(cmd_str));

    // 根据当前状态处理输入
    if (uart_state == UART_STATE_RTC_CONFIG) {
        // 当前处于等待RTC时间输入状态，直接处理时间字符串
        if (rtc_set_time_from_string(cmd_str) == HAL_OK) {
            // 设置成功
            char time_buffer[32] = {0};
            rtc_format_current_time_string(time_buffer, sizeof(time_buffer));
            my_printf(&huart1, "RTC Config success\r\n");
            my_printf(&huart1, "Time: %s\r\n", time_buffer);

            // 记录RTC配置日志（修复：使用修改后的时间，只记录一次）
            char rtc_log_buffer[128] = {0};
            snprintf(rtc_log_buffer, sizeof(rtc_log_buffer), "rtc config success to %s", time_buffer);
            sd_write_log_data("rtc config");  // 使用修改后的时间记录
            sd_write_log_data(rtc_log_buffer);
        } else {
            // 设置失败
            my_printf(&huart1, "RTC Config failed\r\n");
            my_printf(&huart1, "Invalid time format\r\n");
            sd_write_log_data("rtc config failed - invalid format");
        }

        // 重置状态为正常模式
        uart_state = UART_STATE_NORMAL;
        return;
    }

    if (uart_state == UART_STATE_RATIO_INPUT) {
        // 当前处于等待ratio数值输入状态，直接处理数值字符串
        float ratio = atof(cmd_str);
        if (config_set_ratio(ratio) == CONFIG_OK) {
            my_printf(&huart1, "ratio modified success\r\n");
            my_printf(&huart1, "Ratio = %.1f\r\n", config_get_ratio());
            // 记录ratio配置成功日志（按照官方示例格式）
            char log_buffer[64] = {0};
            snprintf(log_buffer, sizeof(log_buffer), "ratio config success to %.2f", config_get_ratio());
            sd_write_log_data(log_buffer);
        } else {
            my_printf(&huart1, "ratio invalid\r\n");
            my_printf(&huart1, "Ratio = %.1f\r\n", config_get_ratio());
            sd_write_log_data("ratio parameter modification failed - invalid value");
        }

        // 重置状态为正常模式
        uart_state = UART_STATE_NORMAL;
        return;
    }

    if (uart_state == UART_STATE_LIMIT_INPUT) {
        // 当前处于等待limit数值输入状态，直接处理数值字符串
        float limit = atof(cmd_str);
        if (config_set_limit(limit) == CONFIG_OK) {
            my_printf(&huart1, "limit modified success\r\n");
            my_printf(&huart1, "limit = %.2f\r\n", config_get_limit());
            // 记录limit配置成功日志（按照官方示例格式）
            char log_buffer[64] = {0};
            snprintf(log_buffer, sizeof(log_buffer), "limit config success to %.2f", config_get_limit());
            sd_write_log_data(log_buffer);
        } else {
            my_printf(&huart1, "limit invalid\r\n");
            my_printf(&huart1, "limit = %.2f\r\n", config_get_limit());
            sd_write_log_data("limit parameter modification failed - invalid value");
        }

        // 重置状态为正常模式
        uart_state = UART_STATE_NORMAL;
        return;
    }

    // 处理 "command:" 前缀 - 符合测评要求格式
    if (strncmp(cmd_str, "command:", 8) == 0) {
        cmd_str += 8; // 跳过 "command:" 前缀
        // my_printf(&huart1, "DEBUG: Stripped command: '%s'\r\n", cmd_str);
    }

    // 检查是否为二进制协议 (十六进制字符串)
    if (is_hex_string(cmd_str)) {
        handle_binary_protocol_cmd(cmd_str);
        return;
    }

    // 正常命令解析模式
    cmd_type_t cmd_type = parse_command_type(cmd_str);

    if (cmd_type == CMD_NONE) {
        uart_printf("Error: Unknown command\r\n");
        return;
    }

    // 查找对应的处理函数并执行
    // 注释掉debug输出，保持功能演示的标准格式 - my_printf(&huart1, "DEBUG: Looking for handler for cmd_type=%d\r\n", cmd_type);
    for (int i = 0; cmd_table[i].cmd_str != NULL; i++) {
        if (cmd_table[i].cmd_type == cmd_type) {
            // 注释掉debug输出，保持功能演示的标准格式 - my_printf(&huart1, "DEBUG: Found handler '%s' for cmd_type=%d\r\n", cmd_table[i].cmd_str, cmd_type);
            // Extract parameter part
            char *params = cmd_str + strlen(cmd_table[i].cmd_str);
            while (*params == ' ' || *params == '\t') params++; // Skip spaces

            // Call handler function
            if (cmd_table[i].handler != NULL) {
                // Debug output commented out for standard format - my_printf(&huart1, "DEBUG: Calling handler function\r\n");
                cmd_table[i].handler(params);
            }
            break;
        }
    }
}

/**
 * @brief 处理test指令 - 系统自检
 * @param params 参数字符串
 * @retval None
 */
void handle_test_cmd(char *params) //处理test命令
{
    extern UART_HandleTypeDef huart1;

    // 显示当前日志状态
    my_printf(&huart1, "[TEST_CMD] Current log_id: %lu, boot_count: %lu\r\n",
              g_log_id_manager.log_id, sd_get_boot_count());

    sd_write_log_data("system hardware test"); //记录测试开始

    // 修复：创建selftest_info结构体，避免重复调用selftest_print_results
    selftest_info_t info = {0};

    // 检测Flash（使用简化版本，专注于竞赛要求）
    selftest_check_flash_simple(&info);

    // 检测SD卡
    selftest_check_sd(&info);

    // 检测RTC
    selftest_check_rtc(&info);

    // 打印结果到串口（不记录到日志）
    selftest_print_results(&info);

    // 判断整体结果
    selftest_result_t result = SELFTEST_OK;
    if (!info.flash_ok) result = SELFTEST_FLASH_ERROR;
    else if (!info.sd_ok) result = SELFTEST_SD_ERROR;
    else if (!info.rtc_ok) result = SELFTEST_RTC_ERROR;

    // 移除smart_log_switch调用 - 日志切换应该基于上电次数，不是基于test命令
    // smart_log_switch(LOG_TRIGGER_FIRST_TEST);

    if (result == SELFTEST_OK) { //根据结果记录日志
        my_printf(&huart1, "[TEST_CMD] Test successful, recording test ok\r\n");
        sd_write_log_data("test ok");
    } else if (result == SELFTEST_SD_ERROR) {
        my_printf(&huart1, "[TEST_CMD] SD card error, recording error\r\n");
        sd_write_log_data("test error: tf card not found");
    } else if (result == SELFTEST_FLASH_ERROR) {
        sd_write_log_data("test error: flash error");
    } else if (result == SELFTEST_RTC_ERROR) {
        sd_write_log_data("test error: rtc error");
    } else {
        sd_write_log_data("test error: unknown error");
    }

    // 显示测试后的日志状态
    my_printf(&huart1, "[TEST_CMD] After test - log_id: %lu, boot_count: %lu\r\n",
              g_log_id_manager.log_id, sd_get_boot_count());
}

/**
 * @brief 处理test simple指令 - 简化系统自检
 * @param params 参数字符串
 * @retval None
 */
void handle_test_simple_cmd(char *params)
{
    selftest_info_t info = {0};

    my_printf(&huart1, "======system selftest (simple)======\r\n");

    // 使用简化的Flash检测
    selftest_check_flash_simple(&info);

    // 检测SD卡
    selftest_check_sd(&info);

    // 检测RTC
    selftest_check_rtc(&info);

    // 打印结果
    selftest_print_results(&info);

    // 记录操作日志
    sd_write_log_data("system selftest (simple) executed");
}

/**
 * @brief 处理sd test指令 - SD卡测试
 * @param params 参数字符串
 * @retval None
 */
void handle_sd_test_cmd(char *params)
{
    FRESULT fr;
    FATFS *pfs;
    DWORD free_clusters, total_sectors;

    my_printf(&huart1, "======SD Card Test======\r\n");

    // 步骤1：检查SD卡硬件状态
    my_printf(&huart1, "Step 1: Checking SD card hardware\r\n");
    HAL_SD_CardStateTypeDef card_state = HAL_SD_GetCardState(&hsd);
    my_printf(&huart1, "SD card state: %d\r\n", card_state);

    // 步骤2：获取SD卡信息
    my_printf(&huart1, "Step 2: Reading SD card information\r\n");
    HAL_SD_CardInfoTypeDef card_info;
    if (HAL_SD_GetCardInfo(&hsd, &card_info) == HAL_OK) {
        my_printf(&huart1, "SD Card Information:\r\n");
        my_printf(&huart1, "- Card Type: ");
        switch(card_info.CardType) {
            case CARD_SDSC: my_printf(&huart1, "SDSC Card\r\n"); break;
            case CARD_SDHC_SDXC: my_printf(&huart1, "SDHC/SDXC Card\r\n"); break;
            default: my_printf(&huart1, "Unknown Card\r\n");
        }
        my_printf(&huart1, "- Card Capacity: %lu MB\r\n", (uint32_t)(card_info.BlockNbr / 2048));
        my_printf(&huart1, "- Block Size: %lu Bytes\r\n", (uint32_t)card_info.BlockSize);

        // 验证卡信息是否有效
        if(card_info.BlockSize == 0 || card_info.BlockNbr == 0) {
            my_printf(&huart1, "Warning: Invalid card parameters detected!\r\n");
        }
    } else {
        my_printf(&huart1, "Failed to get SD card information!\r\n");
        my_printf(&huart1, "This usually means SD card is not inserted or not working\r\n");
        my_printf(&huart1, "======SD Card Test======\r\n");
        return;
    }

    // 步骤3：检查文件系统状态（不重新初始化）
    my_printf(&huart1, "Step 3: Checking file system status\r\n");
    fr = sd_check_card_status();
    if (fr != FR_OK) {
        my_printf(&huart1, "File system not accessible, trying to reinitialize...\r\n");
        sd_app_init();

        // 重新检查
        fr = sd_check_card_status();
        if (fr != FR_OK) {
            my_printf(&huart1, "SD status........error (code: %d)\r\n", fr);
            my_printf(&huart1, "File system not accessible after reinit\r\n");
            my_printf(&huart1, "======SD Card Test======\r\n");
            return;
        }
    }
    my_printf(&huart1, "SD status........ok\r\n");

    // 步骤4：获取文件系统信息
    my_printf(&huart1, "Step 4: Getting file system information\r\n");

    // 获取SD卡容量信息
    fr = f_getfree("0:", &free_clusters, &pfs);
    if (fr == FR_OK) {
        total_sectors = (pfs->n_fatent - 2) * pfs->csize;
        uint32_t total_mb = total_sectors / 2048;  // 转换为MB
        uint32_t free_mb = free_clusters * pfs->csize / 2048;
        my_printf(&huart1, "SD capacity: %lu MB\r\n", total_mb);
        my_printf(&huart1, "SD free: %lu MB\r\n", free_mb);
    }

    // 步骤5：测试目录状态
    my_printf(&huart1, "Step 5: Checking directory status\r\n");
    fr = sd_create_directories();
    if (fr != FR_OK) {
        my_printf(&huart1, "Directory check..error (code: %d)\r\n", fr);
        if (fr == FR_WRITE_PROTECTED) {
            my_printf(&huart1, "SD card is write protected!\r\n");
        }
    } else {
        my_printf(&huart1, "Directory check..ok\r\n");
    }

    // 步骤6：测试各种数据写入
    my_printf(&huart1, "Step 6: Testing data storage\r\n");

    // 测试采样数据写入
    fr = sd_write_sample_data("", 1.5f);
    if (fr == FR_OK) {
        my_printf(&huart1, "Sample data......ok\r\n");
    } else {
        my_printf(&huart1, "Sample data......error (code: %d)\r\n", fr);
    }

    // 测试超限数据写入
    fr = sd_write_overlimit_data("", 30.0f, 10.0f);
    if (fr == FR_OK) {
        my_printf(&huart1, "OverLimit data...ok\r\n");
    } else {
        my_printf(&huart1, "OverLimit data...error (code: %d)\r\n", fr);
    }

    // 测试日志数据写入
    fr = sd_write_log_data("SD card test executed");
    if (fr == FR_OK) {
        my_printf(&huart1, "Log data.........ok\r\n");
    } else {
        my_printf(&huart1, "Log data.........error (code: %d)\r\n", fr);
    }

    // 测试加密数据写入
    fr = sd_write_hidedata("1234567890ABCDEF");
    if (fr == FR_OK) {
        my_printf(&huart1, "Hide data........ok\r\n");
    } else {
        my_printf(&huart1, "Hide data........error (code: %d)\r\n", fr);
    }

    my_printf(&huart1, "======SD Card Test======\r\n");
}

/**
 * @brief 处理sd detect指令 - SD卡重新检测
 * @param params 参数字符串
 * @retval None
 */
void handle_sd_detect_cmd(char *params)
{
    my_printf(&huart1, "======SD Card Detection======\r\n");

    // 步骤1：重新初始化SDIO硬件
    my_printf(&huart1, "Step 1: Reinitializing SDIO hardware\r\n");
    if (HAL_SD_Init(&hsd) != HAL_OK) {
        my_printf(&huart1, "SDIO init........error\r\n");
        my_printf(&huart1, "Hardware initialization failed\r\n");
        my_printf(&huart1, "======SD Card Detection======\r\n");
        return;
    }
    my_printf(&huart1, "SDIO init........ok\r\n");

    // 步骤2：等待SD卡稳定
    my_printf(&huart1, "Step 2: Waiting for SD card to stabilize\r\n");
    HAL_Delay(200);

    // 步骤3：检查SD卡状态
    my_printf(&huart1, "Step 3: Checking SD card state\r\n");
    HAL_SD_CardStateTypeDef card_state = HAL_SD_GetCardState(&hsd);
    my_printf(&huart1, "Card state: %d ", card_state);
    switch(card_state) {
        case HAL_SD_CARD_READY:
            my_printf(&huart1, "(Ready)\r\n");
            break;
        case HAL_SD_CARD_IDENTIFICATION:
            my_printf(&huart1, "(Identification)\r\n");
            break;
        case HAL_SD_CARD_STANDBY:
            my_printf(&huart1, "(Standby)\r\n");
            break;
        case HAL_SD_CARD_TRANSFER:
            my_printf(&huart1, "(Transfer)\r\n");
            break;
        case HAL_SD_CARD_SENDING:
            my_printf(&huart1, "(Sending)\r\n");
            break;
        case HAL_SD_CARD_RECEIVING:
            my_printf(&huart1, "(Receiving)\r\n");
            break;
        case HAL_SD_CARD_PROGRAMMING:
            my_printf(&huart1, "(Programming)\r\n");
            break;
        case HAL_SD_CARD_DISCONNECTED:
            my_printf(&huart1, "(Disconnected)\r\n");
            break;
        case HAL_SD_CARD_ERROR:
            my_printf(&huart1, "(Error)\r\n");
            break;
        default:
            my_printf(&huart1, "(Unknown)\r\n");
    }

    // 步骤4：尝试获取SD卡信息
    my_printf(&huart1, "Step 4: Reading SD card information\r\n");
    HAL_SD_CardInfoTypeDef card_info;
    if (HAL_SD_GetCardInfo(&hsd, &card_info) == HAL_OK) {
        my_printf(&huart1, "Card info........ok\r\n");
        my_printf(&huart1, "- Card Type: ");
        switch(card_info.CardType) {
            case CARD_SDSC: my_printf(&huart1, "SDSC\r\n"); break;
            case CARD_SDHC_SDXC: my_printf(&huart1, "SDHC/SDXC\r\n"); break;
            default: my_printf(&huart1, "Unknown\r\n");
        }
        my_printf(&huart1, "- Capacity: %lu MB\r\n", (uint32_t)(card_info.BlockNbr / 2048));
        my_printf(&huart1, "- Block Size: %lu Bytes\r\n", (uint32_t)card_info.BlockSize);

        if(card_info.BlockSize == 0 || card_info.BlockNbr == 0) {
            my_printf(&huart1, "Warning: Invalid card parameters!\r\n");
        }
    } else {
        my_printf(&huart1, "Card info........error\r\n");
        my_printf(&huart1, "Cannot read card information\r\n");
    }

    // 步骤5：检查BSP检测函数
    my_printf(&huart1, "Step 5: BSP detection result\r\n");
    if (BSP_SD_IsDetected() == SD_PRESENT) {
        my_printf(&huart1, "BSP detect.......ok (SD card present)\r\n");
    } else {
        my_printf(&huart1, "BSP detect.......error (SD card not detected)\r\n");
    }

    my_printf(&huart1, "======SD Card Detection======\r\n");
    my_printf(&huart1, "Tip: If SD card is detected, run 'sd test' to test functionality\r\n");
}

/**
 * @brief 处理sd format指令 - SD卡格式化
 * @param params 参数字符串
 * @retval None
 */
void handle_sd_format_cmd(char *params)
{
    FRESULT fr;

    my_printf(&huart1, "======SD Card Format======\r\n");
    my_printf(&huart1, "WARNING: This will erase all data on the SD card!\r\n");
    my_printf(&huart1, "Proceeding with format in 3 seconds...\r\n");

    // 等待3秒给用户反应时间
    for (int i = 3; i > 0; i--) {
        my_printf(&huart1, "%d...\r\n", i);
        HAL_Delay(1000);
    }

    my_printf(&huart1, "Starting format...\r\n");

    // 步骤1：卸载现有文件系统
    my_printf(&huart1, "Step 1: Unmounting file system\r\n");
    f_mount(NULL, "0:", 0);
    HAL_Delay(100);

    // 步骤2：重新初始化SD卡硬件
    my_printf(&huart1, "Step 2: Reinitializing SD card hardware\r\n");
    if (HAL_SD_Init(&hsd) != HAL_OK) {
        my_printf(&huart1, "SD hardware init failed!\r\n");
        my_printf(&huart1, "======SD Card Format======\r\n");
        return;
    }

    // 步骤3：格式化SD卡
    my_printf(&huart1, "Step 3: Formatting SD card (this may take a while)...\r\n");
    BYTE sfd = 1;      // 使用SFD分区方式
    UINT au = 0;       // 自动选择簇大小
    BYTE bpData[512];

    fr = f_mkfs("0:", sfd, au, bpData, 512);
    if (fr != FR_OK) {
        my_printf(&huart1, "Format failed! Error code: %d\r\n", fr);
        switch(fr) {
            case FR_NOT_READY:
                my_printf(&huart1, "SD card not ready\r\n");
                break;
            case FR_WRITE_PROTECTED:
                my_printf(&huart1, "SD card is write protected\r\n");
                break;
            case FR_DISK_ERR:
                my_printf(&huart1, "Disk I/O error\r\n");
                break;
            case FR_MKFS_ABORTED:
                my_printf(&huart1, "Format aborted\r\n");
                break;
            default:
                my_printf(&huart1, "Unknown error\r\n");
                break;
        }
        my_printf(&huart1, "======SD Card Format======\r\n");
        return;
    }

    my_printf(&huart1, "Format successful!\r\n");

    // 步骤4：重新挂载文件系统
    my_printf(&huart1, "Step 4: Remounting file system\r\n");
    fr = f_mount(&g_fs, "0:", 1);
    if (fr != FR_OK) {
        my_printf(&huart1, "Remount failed! Error code: %d\r\n", fr);
    } else {
        my_printf(&huart1, "Remount successful!\r\n");

        // 显示格式化后的空间信息
        DWORD fre_clust, fre_sect, tot_sect;
        FATFS *fs_ptr = &g_fs;
        fr = f_getfree("0:", &fre_clust, &fs_ptr);
        if(fr == FR_OK) {
            tot_sect = (fs_ptr->n_fatent - 2) * fs_ptr->csize;
            fre_sect = fre_clust * fs_ptr->csize;
            my_printf(&huart1, "Formatted SD Card Info:\r\n");
            my_printf(&huart1, "- Total Space: %lu MB\r\n", tot_sect / 2048);
            my_printf(&huart1, "- Free Space: %lu MB\r\n", fre_sect / 2048);
        }
    }

    my_printf(&huart1, "======SD Card Format======\r\n");
    my_printf(&huart1, "Tip: Run 'sd test' to verify the formatted SD card\r\n");
}

/**
 * @brief 处理RTC Config指令 - 时间设置（2025 CIMC西门子杯竞赛要求）
 * @details 按照竞赛评测流程严格实现：
 *          2.1 串口输入"RTC Config"，串口返回"Input Datetime"
 *          2.2 输入当前标准时间，例如"2025-01-01 15:00:10"，返回成功信息
 * @param params 参数字符串（忽略，因为测试流程是固定的两步操作）
 * @retval None
 * @note 竞赛要求的输出格式：
 *       成功时：RTC Config success
 *               Time: 2025-01-01 12:00:30
 *       失败时：RTC Config failed
 *               Invalid time format
 * @note 修复：移除此处的日志记录，避免使用修改前的时间，只在时间设置成功后记录
 */
void handle_rtc_config_cmd(char *params)
{
    // 按照竞赛测试流程，RTC Config指令总是返回"Input Datetime"并等待下一次输入
    my_printf(&huart1, "Input Datetime\r\n");

    // 设置状态机为等待时间输入状态
    uart_state = UART_STATE_RTC_CONFIG;
}

/**
 * @brief 处理RTC now指令 - 显示当前时间（2025 CIMC西门子杯竞赛要求）
 * @details 按照竞赛评测流程严格实现：
 *          2.3 输入"RTC now"，串口返回当前时间
 * @param params 参数字符串（忽略）
 * @retval None
 * @note 竞赛要求的输出格式：
 *       Current Time: 2025-01-01 12:00:30
 * @note 竞赛要求：RTC now命令需要记录到log0.txt中
 */
void handle_rtc_now_cmd(char *params)
{
    char time_buffer[32] = {0};

    // 获取当前时间并格式化
    rtc_format_current_time_string(time_buffer, sizeof(time_buffer));

    // 严格按照竞赛要求的输出格式
    my_printf(&huart1, "Current Time: %s\r\n", time_buffer);

    // 记录RTC now命令到日志（竞赛要求：记录到log0.txt）
    char rtc_now_log[64] = {0};
    snprintf(rtc_now_log, sizeof(rtc_now_log), "rtc now - current time: %s", time_buffer);
    sd_write_log_data(rtc_now_log);
}

/**
 * @brief 处理conf test指令 - SD卡文件操作安全测试
 * @details 专门用于测试SD卡文件操作，逐步检查每个环节
 * @param params 参数字符串
 * @retval None
 */
void handle_conf_test_cmd(char *params)
{
    my_printf(&huart1, "=== SD Card File Operation Test ===\r\n");

    // 调用安全测试函数
    config_status_t result = config_safe_sd_test();

    if (result == CONFIG_OK) {
        my_printf(&huart1, "SD card file test: PASSED\r\n");
    } else {
        my_printf(&huart1, "SD card file test: FAILED\r\n");
    }

    my_printf(&huart1, "=== Test Complete ===\r\n");
}

/**
 * @brief 处理conf delete指令 - 删除config.ini文件（测试用）
 * @details 用于测试conf指令在文件不存在时的行为
 * @param params 参数字符串
 * @retval None
 */
void handle_conf_delete_cmd(char *params)
{
    if (!g_filesystem_mounted) {
        my_printf(&huart1, "SD card not mounted\r\n");
        return;
    }

    FRESULT fr = f_unlink(CONFIG_FILE_NAME);
    if (fr == FR_OK) {
        my_printf(&huart1, "config.ini deleted successfully\r\n");
    } else if (fr == FR_NO_FILE) {
        my_printf(&huart1, "config.ini file not found (already deleted)\r\n");
    } else {
        my_printf(&huart1, "Failed to delete config.ini (error: %d)\r\n", fr);
    }
}

/**
 * @brief 处理conf fix指令 - 修复SD卡文件系统问题
 * @details 尝试重新挂载文件系统并创建config.ini文件
 * @param params 参数字符串
 * @retval None
 */
void handle_conf_fix_cmd(char *params)
{
    my_printf(&huart1, "=== SD Card File System Fix ===\r\n");

    // 步骤1：重新挂载文件系统
    extern FATFS g_fs;
    my_printf(&huart1, "Step 1: Remounting file system...\r\n");

    // 先卸载
    f_mount(NULL, "", 0);
    g_filesystem_mounted = 0;

    // 重新挂载
    FRESULT fr = f_mount(&g_fs, "", 1);
    if (fr == FR_OK) {
        g_filesystem_mounted = 1;
        my_printf(&huart1, "File system remounted successfully\r\n");
    } else {
        my_printf(&huart1, "Failed to remount file system (error: %d)\r\n", fr);
        return;
    }

    // 步骤2：尝试创建config.ini文件
    my_printf(&huart1, "Step 2: Creating config.ini file...\r\n");

    // 强制调用config save来创建文件
    config_save_to_sd();

    // 步骤3：验证文件是否创建成功
    my_printf(&huart1, "Step 3: Verifying file creation...\r\n");
    FIL file;
    fr = f_open(&file, CONFIG_FILE_NAME, FA_READ);
    if (fr == FR_OK) {
        f_close(&file);
        my_printf(&huart1, "config.ini file verified successfully\r\n");
        my_printf(&huart1, "=== Fix Complete ===\r\n");
    } else {
        my_printf(&huart1, "config.ini file still not accessible (error: %d)\r\n", fr);
        my_printf(&huart1, "=== Fix Failed ===\r\n");
    }
}

/**
 * @brief 处理conf指令 - 按照竞赛要求实现配置管理功能
 * @details 竞赛要求：
 *          1. 从TF卡读取config.ini文件，更新变比和阈值至Flash
 *          2. 如果文件不存在，返回"config.ini file not found."
 *          3. 如果文件存在，返回"Ratio = xxxx, Limit = xxxx, config read success"
 * @param params 参数字符串
 * @retval None
 */
void handle_conf_cmd(char *params)
{
    config_status_t status = config_display_from_sd();

    if (status == CONFIG_OK) {
        // 竞赛要求：读取成功后显示"config read success"
        my_printf(&huart1, "config read success\r\n");
    } else if (status == CONFIG_FILE_NOT_FOUND) {
        // 竞赛要求：文件不存在时的精确输出格式
        my_printf(&huart1, "config.ini file not found.\r\n");
    } else {
        // 其他错误情况
        my_printf(&huart1, "config read error\r\n");
    }
}

/**
 * @brief 处理ratio指令 - 变比设置（增强版本，包含详细状态反馈）
 * @details 提供清晰的用户反馈，帮助用户理解参数设置和保存的区别
 *          设置成功后提醒用户需要执行config save来持久化参数
 * @param params 参数字符串
 * @retval None
 */
void handle_ratio_cmd(char *params)
{
    // 如果有参数，直接设置；否则进入交互模式
    if (strlen(params) > 0) {
        float ratio = atof(params);
        if (config_set_ratio(ratio) == CONFIG_OK) {
            // 成功设置后提供详细的确认信息
            my_printf(&huart1, "ratio set to %.1f (use 'config save' to persist)\r\n", config_get_ratio());
            my_printf(&huart1, "Ratio = %.1f\r\n", config_get_ratio());

            // 记录操作日志
            char log_buffer[64] = {0};
            snprintf(log_buffer, sizeof(log_buffer), "ratio config success to %.2f", config_get_ratio());
            sd_write_log_data(log_buffer);
        } else {
            // 设置失败时提供明确的错误信息
            my_printf(&huart1, "ratio set failed (valid range: 0-100)\r\n");
            my_printf(&huart1, "Current Ratio = %.1f\r\n", config_get_ratio());
            sd_write_log_data("ratio parameter modification failed - invalid value");
        }
    } else {
        // 记录ratio配置指令日志（按照官方示例格式）
        sd_write_log_data("ratio config");

        // 交互模式：显示当前值并提示输入
        my_printf(&huart1, "Current Ratio = %.1f\r\n", config_get_ratio());
        my_printf(&huart1, "Input value(0 - 100):\r\n");

        // 设置状态机为等待ratio数值输入状态
        uart_state = UART_STATE_RATIO_INPUT;
    }
}

/**
 * @brief 处理limit指令 - 阈值设置
 * @param params 参数字符串
 * @retval None
 */
void handle_limit_cmd(char *params) //处理limit命令
{
    // 第一次limit命令时，使用统一的日志切换接口切换到log2（超阈值阶段）
    smart_log_switch(LOG_TRIGGER_FIRST_LIMIT);

    if (strlen(params) > 0) { //有参数直接设置
        float limit = atof(params);
        if (config_set_limit(limit) == CONFIG_OK) {
            my_printf(&huart1, "limit modified success\r\n");
            my_printf(&huart1, "limit = %.2f\r\n", config_get_limit());
            char log_buffer[64] = {0};
            snprintf(log_buffer, sizeof(log_buffer), "limit config success to %.2f", config_get_limit());
            sd_write_log_data(log_buffer);
        } else {
            my_printf(&huart1, "limit invalid\r\n");
            my_printf(&huart1, "limit = %.2f\r\n", config_get_limit());
            sd_write_log_data("limit parameter modification failed - invalid value");
        }
    } else { //交互模式
        sd_write_log_data("limit config"); //记录日志

        my_printf(&huart1, "limit = %.2f\r\n", config_get_limit());
        my_printf(&huart1, "Input value(0 - 200):\r\n");

        uart_state = UART_STATE_LIMIT_INPUT; //等待输入
    }
}

/**
 * @brief 处理config save指令 - 保存配置到Flash并同步到SD卡
 * @details 确保Flash保存成功后才同步到SD卡，避免数据不一致问题
 *          使用增强的config_save_to_flash()函数，包含写入验证机制
 *          添加详细的调试信息
 * @param params 参数字符串
 * @retval None
 */
void handle_config_save_cmd(char *params)
{
    // 显示当前内存中的参数（使用英文冒号符合竞赛要求）
	my_printf(&huart1, "ratio:%.1f\r\n", config_get_ratio());
	my_printf(&huart1, "limit:%.2f\r\n", config_get_limit());

    // 调用增强后的config_save_to_flash()函数（包含验证机制）
    config_status_t save_result = config_save_to_flash();
    if (save_result == CONFIG_OK) {
        my_printf(&huart1, "save parameters to flash\r\n");

        // 验证Flash写入结果（调试信息已删除）
        // config_params_t verify_params;
        // if (flash_direct_read(CONFIG_FLASH_ADDR, &verify_params, sizeof(verify_params)) == FLASH_OK) {
        //     my_printf(&huart1, "Flash verify: ratio=%.1f, limit=%.2f\r\n",
        //              verify_params.ratio, verify_params.limit);
        // } else {
        //     my_printf(&huart1, "Flash verify read failed\r\n");
        // }

        // 只有在Flash保存成功时才同步到SD卡config.ini文件
        // 确保Flash和SD卡数据的一致性，Flash为主存储，SD卡为备份
        if (g_filesystem_mounted) {
            config_save_to_sd();
        }
        // SD卡调试信息已删除
        // else {
        //     my_printf(&huart1, "SD card not mounted, skip config.ini sync\r\n");
        // }

        // 记录config save操作日志
        sd_write_log_data("config save");
    }
    // Flash保存失败调试信息已删除
    // else {
    //     my_printf(&huart1, "save parameters failed (error: %d)\r\n", save_result);
    //     // Flash保存失败时不更新SD卡，避免数据不一致
    //
    //     // 记录保存失败的日志
    //     sd_write_log_data("config save failed");
    // }
}

/**
 * @brief 处理config read指令 - 从Flash读取配置（只读显示，不覆盖内存参数）
 * @details 修复关键问题：不调用config_load_from_flash()避免覆盖用户设置的内存参数
 *          直接从Flash读取数据并显示，保持内存中用户设置的参数不变
 * @param params 参数字符串
 * @retval None
 */
void handle_config_read_cmd(char *params)
{
    // 直接从Flash读取参数，不覆盖内存中的g_config_params
    config_params_t flash_params;
    if (flash_direct_read(CONFIG_FLASH_ADDR, &flash_params, sizeof(flash_params)) == FLASH_OK) {
        my_printf(&huart1, "read parameters from flash\r\n");
			my_printf(&huart1, "ratio:%.1f\r\n", flash_params.ratio);
			my_printf(&huart1, "limit:%.2f\r\n", flash_params.limit);
    } else {
        my_printf(&huart1, "read parameters from flash failed\r\n");
    }
}

/**
 * @brief 处理start指令 - 开始采样
 * @param params 参数字符串
 * @retval None
 */
void handle_start_cmd(char *params)
{
    adc_start_sampling();
    my_printf(&huart1, "Periodic Sampling\r\n");
    my_printf(&huart1, "sample cycle: %ds\r\n", g_adc_control.cycle_ms / 1000);

    // 记录操作日志（按照官方示例格式）
    char log_buffer[64] = {0};
    snprintf(log_buffer, sizeof(log_buffer), "sample start - cycle %ds (command)", g_adc_control.cycle_ms / 1000);
    sd_write_log_data(log_buffer);
}

/**
 * @brief 处理stop指令 - 停止采样
 * @param params 参数字符串
 * @retval None
 */
void handle_stop_cmd(char *params)
{
    adc_stop_sampling();
    my_printf(&huart1, "Periodic Sampling STOP\r\n");

    // 记录操作日志（按照官方示例格式）
    sd_write_log_data("sample stop (command)");
}

/**
 * @brief 处理hide指令 - 数据隐藏（竞赛要求4：数据处理）
 * @details 按照竞赛要求将时间戳转换为Unix时间戳（4字节HEX）和电压值（4字节HEX）
 *          编码规则：
 *          - 时间戳：4字节Unix时间戳（如2025-01-01 12:30:45 → 1735705845 → 6774C4F5）
 *          - 电压值：4字节，分为两部分：
 *            * 小数点前：2字节（高位在前）例如：12.5V 整数部分12 → 000C
 *            * 小数点后：2字节（高位在前）例如：12.5V 小数部分0.5*65536=32768 → 8000
 *          - 最终结果：6774C4F5000C8000
 * @param params 参数字符串（忽略）
 * @retval None
 * @note 此功能用于数据隐藏，输出HEX格式的加密数据，并启用加密存储模式
 */
void handle_hide_cmd(char *params) //处理hide命令
{
    // 使用统一的日志切换接口切换到log3（加密阶段）
    smart_log_switch(LOG_TRIGGER_HIDE_MODE);

    extern uint8_t g_hide_mode_enabled; //启用加密模式
    g_hide_mode_enabled = 1;

    sd_write_log_data("hide data"); //记录日志
}

/**
 * @brief 处理unhide指令 - 恢复原有格式
 * @param params 参数字符串
 * @retval None
 */
void handle_unhide_cmd(char *params)
{
    // 禁用加密存储模式
    extern uint8_t g_hide_mode_enabled;
    g_hide_mode_enabled = 0;

    // 记录操作日志
    sd_write_log_data("unhide data");

    // 注意：不立即输出数据，避免破坏采样时间间隔
    // 后续的采样输出将自动使用正常格式
}

/**
 * @brief 处理system status指令 - 显示系统状态
 * @param params 参数字符串
 * @retval None
 */
void handle_system_status_cmd(char *params)
{
    uint32_t uptime = HAL_GetTick();
    uint8_t task_count = scheduler_get_task_count();
    uint8_t scheduler_status = scheduler_get_status();

    // 显示系统基本信息
    my_printf(&huart1, "=== System Status ===\r\n");
    my_printf(&huart1, "Program Version: %lu\r\n", CURRENT_PROGRAM_VERSION);
    // 临时注释掉boot count显示，避免链接错误
    // my_printf(&huart1, "Boot Count: %lu\r\n", sd_get_boot_count());
    my_printf(&huart1, "Uptime: %lu ms\r\n", uptime);
    my_printf(&huart1, "Scheduler: %s (%d tasks)\r\n",
             scheduler_status ? "Running" : "Error", task_count);

    // 显示测试阶段状态信息
    my_printf(&huart1, "=== Test Stage Status ===\r\n");
    my_printf(&huart1, "Current Stage: %d\r\n", test_stage_get_current_stage());
    my_printf(&huart1, "State Consistency: %s\r\n",
              check_test_stage_consistency() ? "OK" : "ERROR");

    // 显示log文件状态
    if (g_log_id_manager.initialized) {
        my_printf(&huart1, "Current Log ID: %lu\r\n", g_log_id_manager.log_id);
    }

    // 显示测试进度和完成情况
    my_printf(&huart1, "=== Test Progress ===\r\n");
    uint8_t current_stage = test_stage_get_current_stage();
    uint8_t progress = (current_stage * 100) / 3;
    my_printf(&huart1, "Progress: %d%% (%d/3 stages)\r\n", progress, current_stage);

    const char *stage_names[] = {"Initial/RTC", "Data Collection", "Threshold", "Encryption"};
    my_printf(&huart1, "Current Phase: %s\r\n", stage_names[current_stage]);

    // 显示各个log文件的记录数量
    if (g_filesystem_mounted) {
        my_printf(&huart1, "=== Log Files Status ===\r\n");
        const char *log_files[] = {"log/log0.txt", "log/log1.txt", "log/log2.txt", "log/log3.txt"};
        for (int i = 0; i < 4; i++) {
            uint8_t record_count = log_count_records_in_file(log_files[i]);
            my_printf(&huart1, "%s: %d records\r\n", log_files[i], record_count);
        }
    }

    // 显示采样状态
    my_printf(&huart1, "Sampling: %s\r\n",
             (g_adc_control.state == SAMPLING_ACTIVE) ? "Active" : "Idle");
    if (g_adc_control.state == SAMPLING_ACTIVE) {
        my_printf(&huart1, "Sample cycle: %d ms\r\n", g_adc_control.cycle_ms);
        my_printf(&huart1, "Current voltage: %.2f V\r\n", g_adc_control.processed_voltage);
        my_printf(&huart1, "Over limit: %s\r\n", g_adc_control.over_limit ? "Yes" : "No");
    }

    // 显示配置参数
    my_printf(&huart1, "Config - Ratio: %.1f, Limit: %.2f\r\n",
             config_get_ratio(), config_get_limit());

    // 显示RTC时间
    char time_buffer[32] = {0};
    rtc_format_current_time_string(time_buffer, sizeof(time_buffer));
    my_printf(&huart1, "RTC Time: %s\r\n", time_buffer);

    my_printf(&huart1, "=== End Status ===\r\n");

    // 记录操作日志
    sd_write_log_data("system status command executed");
}

/**
 * @brief 处理reset boot指令 - 重置启动次数（调试用）
 * @param params 参数字符串
 * @retval None
 */
void handle_reset_boot_cmd(char *params)
{
    // 重置启动次数
    FRESULT result = sd_reset_boot_count();

    if (result == FR_OK) {
        my_printf(&huart1, "Boot count reset to 0 successfully\r\n");
        my_printf(&huart1, "Flash log cache cleared completely\r\n");
        my_printf(&huart1, "Next log file will be log0.txt\r\n");

        // 显示Flash缓存状态（重置后应为0，验证清空效果）
        uint32_t cached_count = sd_get_cached_log_count();
        my_printf(&huart1, "Flash cache now contains %lu log entries\r\n", cached_count);

        // 不记录操作日志（按用户要求删除"boot count reset to 0"日志）
        // sd_write_log_data("boot count reset to 0");  // 已删除
    } else {
        my_printf(&huart1, "Failed to reset boot count (error: %d)\r\n", result);
    }
}

/**
 * @brief 处理reset stage指令 - 重置测试阶段状态
 * @param params 参数字符串
 * @retval None
 */
void handle_reset_stage_cmd(char *params)
{
    my_printf(&huart1, "Resetting test stage to initial state...\r\n");

    reset_test_stage_to_initial();

    my_printf(&huart1, "Test stage reset completed\r\n");
    my_printf(&huart1, "Current stage: %d\r\n", test_stage_get_current_stage());

    // 记录操作日志
    sd_write_log_data("test stage reset to initial state");
}

/**
 * @brief 处理recover sd指令 - SD卡异常恢复
 * @param params 参数字符串
 * @retval None
 */
void handle_recover_sd_cmd(char *params)
{
    my_printf(&huart1, "Starting SD card error recovery...\r\n");

    recover_from_sd_card_error();

    my_printf(&huart1, "SD card recovery process completed\r\n");

    // 记录操作日志
    sd_write_log_data("SD card error recovery executed");
}

/**
 * @brief 处理check state指令 - 检查状态一致性
 * @param params 参数字符串
 * @retval None
 */
void handle_check_state_cmd(char *params)
{
    my_printf(&huart1, "Checking system state consistency...\r\n");

    uint8_t is_consistent = check_test_stage_consistency();

    if (is_consistent) {
        my_printf(&huart1, "State consistency check: PASSED\r\n");
        my_printf(&huart1, "Current stage: %d\r\n", test_stage_get_current_stage());
    } else {
        my_printf(&huart1, "State consistency check: FAILED\r\n");
        my_printf(&huart1, "Attempting automatic repair...\r\n");

        if (auto_repair_inconsistent_state()) {
            my_printf(&huart1, "Automatic repair: SUCCESS\r\n");
        } else {
            my_printf(&huart1, "Automatic repair: FAILED\r\n");
            my_printf(&huart1, "Manual intervention required\r\n");
        }
    }

    // 记录操作日志
    char log_buffer[64];
    snprintf(log_buffer, sizeof(log_buffer), "state consistency check - %s",
             is_consistent ? "passed" : "failed");
    sd_write_log_data(log_buffer);
}

/**
 * @brief 处理log validate指令 - 验证日志文件
 * @param params 参数字符串
 * @retval None
 */
void handle_log_validate_cmd(char *params)
{
    my_printf(&huart1, "Starting comprehensive log validation...\r\n");

    log_validate_all_files();

    my_printf(&huart1, "Log validation completed\r\n");

    // 记录操作日志
    sd_write_log_data("log files validation executed");
}

/**
 * @brief 处理test simulate指令 - 模拟测试序列
 * @param params 参数字符串
 * @retval None
 */
void handle_test_simulate_cmd(char *params)
{
    my_printf(&huart1, "Starting test sequence simulation...\r\n");

    test_simulate_full_sequence();

    my_printf(&huart1, "Test simulation completed\r\n");
    my_printf(&huart1, "Run 'log validate' to verify results\r\n");

    // 记录操作日志
    sd_write_log_data("test sequence simulation executed");
}

/**
 * @brief 处理test report指令 - 生成测试报告
 * @param params 参数字符串
 * @retval None
 */
void handle_test_report_cmd(char *params)
{
    test_generate_report();

    // 记录操作日志
    sd_write_log_data("test report generated");
}

/**
 * @brief 处理sb start指令 - 开始采样板采样
 * @param params 参数字符串
 * @retval None
 */
void handle_sb_start_cmd(char *params)
{
    sampling_board_start_sampling();

    // 记录操作日志
    sd_write_log_data("sampling board start (command)");
}

/**
 * @brief 处理sb stop指令 - 停止采样板采样
 * @param params 参数字符串
 * @retval None
 */
void handle_sb_stop_cmd(char *params)
{
    sampling_board_stop_sampling();

    // 记录操作日志
    sd_write_log_data("sampling board stop (command)");
}

/**
 * @brief 处理sb mode指令 - 设置采样板测量模式
 * @param params 参数字符串（0=电压，1=电流，2=电阻）
 * @retval None
 */
void handle_sb_mode_cmd(char *params)
{
    if (strlen(params) > 0) {
        int mode = atoi(params);
        if (mode >= 0 && mode <= 2) {
            sampling_board_set_measure_mode((measure_type_t)mode);
            const char* mode_names[] = {"Voltage", "Current", "Resistance"};
            my_printf(&huart1, "Sampling board mode set to: %s\r\n", mode_names[mode]);

            // 记录操作日志
            char log_buffer[64] = {0};
            snprintf(log_buffer, sizeof(log_buffer), "sampling board mode: %s", mode_names[mode]);
            sd_write_log_data(log_buffer);
        } else {
            my_printf(&huart1, "Error: Invalid mode (0=Voltage, 1=Current, 2=Resistance)\r\n");
        }
    } else {
        measure_type_t current_mode = sampling_board_get_measure_mode();
        const char* mode_names[] = {"Voltage", "Current", "Resistance"};
        my_printf(&huart1, "Current mode: %s (%d)\r\n", mode_names[current_mode], current_mode);
        my_printf(&huart1, "Usage: sb mode <0|1|2> (0=Voltage, 1=Current, 2=Resistance)\r\n");
    }
}

/**
 * @brief 处理sb read指令 - 读取采样板电压数据
 * @param params 参数字符串
 * @retval None
 */
void handle_sb_read_cmd(char *params)
{
    sampling_board_data_t* data = sampling_board_get_data();

    if (data->data_valid) {
        char time_buffer[32] = {0};
        rtc_format_current_time_string(time_buffer, sizeof(time_buffer));

        my_printf(&huart1, "=== Current Measurement ===\r\n");
        my_printf(&huart1, "Time: %s\r\n", time_buffer);
        my_printf(&huart1, "Current: %.3fmA (4-20mA)\r\n", data->voltage_ch0);
        my_printf(&huart1, "Channel: AIN1~GND, Calibrated\r\n");
        my_printf(&huart1, "===========================\r\n");
    } else {
        my_printf(&huart1, "Error: No valid voltage data available\r\n");
    }

    // 记录操作日志
    sd_write_log_data("voltage measurement read (command)");
}

/**
 * @brief 处理sb check指令 - 校准精度检查
 * @param params 参数字符串（可选：期望电压值）
 * @retval None
 */
void handle_sb_check_cmd(char *params)
{
    my_printf(&huart1, "=== Current Calibration Check ===\r\n");

    // 读取当前AIN1~GND原始数据
    float raw_current = GD30AD3344_AD_Read(GD30AD3344_CH_AIN1_GND, GD30AD3344_PGA_6V144);
    float calibrated_current = current_calibrate_linear(raw_current);
    float filtered_current = current_filter_update(calibrated_current);

    my_printf(&huart1, "Raw reading: %.4fV\r\n", raw_current);
    my_printf(&huart1, "Calibrated: %.3fmA\r\n", calibrated_current);
    my_printf(&huart1, "Filtered: %.3fmA\r\n", filtered_current);
    my_printf(&huart1, "Method: Linear interpolation + Moving Average\r\n");

    // 如果提供了期望值，计算误差
    if (strlen(params) > 0) {
        float expected_current = atof(params);
        if (expected_current >= 0.0f && expected_current <= 25.0f) {
            float error = filtered_current - expected_current;
            float error_percent = (expected_current > 0.001f) ? (error / expected_current) * 100.0f : 0.0f;

            my_printf(&huart1, "Expected: %.3fmA\r\n", expected_current);
            my_printf(&huart1, "Error: %.3fmA (%.2f%%)\r\n", error, error_percent);

            if (fabs(error_percent) < 0.5f) {
                my_printf(&huart1, "✅ EXCELLENT: Error < 0.5%%\r\n");
            } else if (fabs(error_percent) < 1.0f) {
                my_printf(&huart1, "✅ GOOD: Error < 1.0%%\r\n");
            } else if (fabs(error_percent) < 2.0f) {
                my_printf(&huart1, "⚠️  ACCEPTABLE: Error < 2.0%%\r\n");
            } else {
                my_printf(&huart1, "❌ POOR: Error %.2f%% (check calibration)\r\n", error_percent);
            }
        } else {
            my_printf(&huart1, "Error: Expected current must be 0.0-25.0mA\r\n");
        }
    }

    // 显示校准参考表（从校准表中选取关键点）
    my_printf(&huart1, "\r\nCalibration Reference (Key Points):\r\n");
    my_printf(&huart1, "Input(V) | Raw Data(V) | Calibrated(V)\r\n");
    my_printf(&huart1, "---------|-------------|-------------\r\n");

    // 显示电流校准参考表
    my_printf(&huart1, "\r\nCurrent Calibration Reference (Key Points):\r\n");
    my_printf(&huart1, "Current(mA) | Raw Data(V) | Calibrated(mA)\r\n");
    my_printf(&huart1, "------------|-------------|---------------\r\n");

    // 显示关键校准点
    int key_indices[] = {0, 4, 10, 16, 20, 24}; // 0mA, 4mA, 10mA, 16mA, 20mA, 24mA
    const char* key_labels[] = {"0mA", "4mA", "10mA", "16mA", "20mA", "24mA"};

    for (int i = 0; i < 6; i++) {
        int idx = key_indices[i];
        if (idx < CURRENT_CALIBRATION_POINTS) {
            float back_calc = current_calibrate_linear(current_calibration_table[idx].raw_voltage);
            my_printf(&huart1, " %s      |   %.4fV   |   %.3fmA\r\n",
                     key_labels[i],
                     current_calibration_table[idx].raw_voltage,
                     back_calc);
        }
    }

    my_printf(&huart1, "==================================\r\n");
    my_printf(&huart1, "Usage: sb check [expected_voltage]\r\n");
    my_printf(&huart1, "Example: sb check 5.0\r\n");

    // 记录操作日志
    sd_write_log_data("calibration accuracy check performed");
}





/**
 * @brief 处理debug state指令 - 详细系统状态
 * @param params 参数字符串
 * @retval None
 */
void handle_debug_state_cmd(char *params)
{
    debug_print_system_state();

    // 记录操作日志
    sd_write_log_data("debug system state printed");
}

/**
 * @brief 处理sd sync指令 - 强制同步文件系统
 * @param params 参数字符串
 * @retval None
 */
void handle_sd_sync_cmd(char *params)
{
    my_printf(&huart1, "Forcing SD card filesystem sync...\r\n");

    sd_force_sync_filesystem();

    my_printf(&huart1, "SD sync operation completed\r\n");

    // 记录操作日志
    sd_write_log_data("filesystem sync executed");
}

// --- 竞赛专用命令处理函数实现区域 ---

/**
 * @brief 处理get_device_id指令 - 获取设备ID
 * @param params 参数字符串
 * @retval None
 */
void handle_get_device_id_cmd(char *params)
{
    uint16_t device_id = device_id_get();
    uart_printf("report:device_id=0x%04X\r\n", device_id);
}

/**
 * @brief 处理set_device_id指令 - 设置设备ID
 * @param params 参数字符串 (格式: 0x0001 或 1)
 * @retval None
 */
void handle_set_device_id_cmd(char *params)
{
    if (strlen(params) == 0) {
        uart_printf("Usage: set_device_id <id>\r\n");
        return;
    }

    uint16_t new_id;
    // 支持十六进制和十进制输入
    if (strncmp(params, "0x", 2) == 0 || strncmp(params, "0X", 2) == 0) {
        new_id = (uint16_t)strtol(params, NULL, 16);
    } else {
        new_id = (uint16_t)atoi(params);
    }

    if (device_id_set(new_id) == HAL_OK) {
        uart_printf("Device ID set to 0x%04X\r\n", new_id);
        // 保存到Flash
        device_id_save_to_flash();
        uart_printf("Device ID saved to Flash\r\n");
    } else {
        uart_printf("Invalid device ID: 0x%04X\r\n", new_id);
    }
}

/**
 * @brief 处理get_RTC指令 - 获取RTC时间
 * @param params 参数字符串
 * @retval None
 */
void handle_get_rtc_cmd(char *params)
{
    char time_buffer[32] = {0};
    rtc_format_current_time_string(time_buffer, sizeof(time_buffer));
    uart_printf("report:currentTime=%s\r\n", time_buffer);
}

/**
 * @brief 处理set_RTC指令 - 设置RTC时间
 * @param params 参数字符串 (格式: 2025-01-01 12:00:00)
 * @retval None
 */
void handle_set_rtc_cmd(char *params)
{
    if (strlen(params) == 0) {
        uart_printf("Usage: set_RTC=YYYY-MM-DD HH:MM:SS\r\n");
        return;
    }

    // 查找等号分隔符
    char *time_str = strchr(params, '=');
    if (time_str == NULL) {
        uart_printf("Invalid format. Use: set_RTC=YYYY-MM-DD HH:MM:SS\r\n");
        return;
    }
    time_str++; // 跳过等号

    if (rtc_set_time_from_string(time_str) == HAL_OK) {
        uart_printf("report:ok\r\n");
        // 记录时间设置操作
        char log_buffer[64] = {0};
        snprintf(log_buffer, sizeof(log_buffer), "RTC time set to %s", time_str);
        sd_write_log_data(log_buffer);
    } else {
        uart_printf("report:Failed to set RTC time\r\n");
    }
}

/**
 * @brief 处理get_ratio指令 - 获取变比（直接读取版：与get_limit保持一致）
 * @details 采用与get_limit相同的成功策略：直接从SD卡读取配置文件
 *          避免复杂的同步逻辑，确保返回SD卡中的真实数据
 * @param params 参数字符串
 * @retval None
 */
void handle_get_ratio_cmd(char *params)
{
    // 直接从SD卡读取变比配置
    float ch0_ratio, ch1_ratio, ch2_ratio;
    config_get_ratios_direct_from_sd(&ch0_ratio, &ch1_ratio, &ch2_ratio);

    // 返回SD卡中的真实数据
    uart_printf("report:ch0ratio=%.2f,ch1ratio=%.2f,ch2ratio=%.2f\r\n",
              ch0_ratio, ch1_ratio, ch2_ratio);
}

/**
 * @brief 处理set_ratio指令 - 设置变比 (暂时使用现有单通道，后续扩展为3通道)
 * @param params 参数字符串 (格式: ch0=1.00,ch1=2.00,ch2=3.00)
 * @retval None
 */
void handle_set_ratio_cmd(char *params)
{
    if (strlen(params) == 0) {
        uart_printf("Usage: set_ratio:ch0=xx.xx,ch1=xx.xx,ch2=xx.xx\r\n");
        return;
    }

    // 查找冒号分隔符
    char *ratio_str = strchr(params, ':');
    if (ratio_str == NULL) {
        uart_printf("Invalid format. Use: set_ratio:ch0=xx.xx,ch1=xx.xx,ch2=xx.xx\r\n");
        return;
    }
    ratio_str++; // 跳过冒号

    // 解析3通道变比值
    float ch0_ratio = 1.0f, ch1_ratio = 1.0f, ch2_ratio = 1.0f;
    uint8_t ch0_found = 0, ch1_found = 0, ch2_found = 0;

    // 解析ch0
    char *ch0_str = strstr(ratio_str, "ch0=");
    if (ch0_str != NULL) {
        ch0_ratio = atof(ch0_str + 4);
        ch0_found = 1;
    }

    // 解析ch1
    char *ch1_str = strstr(ratio_str, "ch1=");
    if (ch1_str != NULL) {
        ch1_ratio = atof(ch1_str + 4);
        ch1_found = 1;
    }

    // 解析ch2
    char *ch2_str = strstr(ratio_str, "ch2=");
    if (ch2_str != NULL) {
        ch2_ratio = atof(ch2_str + 4);
        ch2_found = 1;
    }

    // 检查是否至少找到一个通道
    if (!ch0_found && !ch1_found && !ch2_found) {
        uart_printf("Invalid ratio format\r\n");
        return;
    }

    // 设置所有通道变比
    if (config_set_all_ratios(ch0_ratio, ch1_ratio, ch2_ratio) == CONFIG_OK) {
        uart_printf("report:ok\r\n");
        // 保存配置到Flash和SD卡
        config_save_to_flash();
        config_save_to_sd();
        // 记录操作日志
        char log_buffer[128] = {0};
        snprintf(log_buffer, sizeof(log_buffer), "ratios set: ch0=%.2f,ch1=%.2f,ch2=%.2f",
                 ch0_ratio, ch1_ratio, ch2_ratio);
        sd_write_log_data(log_buffer);
    } else {
        uart_printf("report:Failed to set ratios\r\n");
    }
}

/**
 * @brief 处理get_limit指令 - 获取阈值（直接读取版：绕过同步机制）
 * @details 最简单直接的解决方案：直接从SD卡读取配置文件
 *          避免复杂的同步逻辑，确保返回SD卡中的真实数据
 * @param params 参数字符串
 * @retval None
 */
void handle_get_limit_cmd(char *params)
{
    // 直接从SD卡读取阈值配置
    float ch0_limit, ch1_limit, ch2_limit;
    config_get_limits_direct_from_sd(&ch0_limit, &ch1_limit, &ch2_limit);

    // 返回SD卡中的真实数据
    uart_printf("report:ch0limit=%.2f,ch1limit=%.2f,ch2limit=%.2f\r\n",
              ch0_limit, ch1_limit, ch2_limit);
}

/**
 * @brief 处理set_limit指令 - 设置阈值 (暂时使用现有单通道，后续扩展为3通道)
 * @param params 参数字符串 (格式: ch0=1.00,ch1=2.00,ch2=3.00)
 * @retval None
 */
void handle_set_limit_cmd(char *params)
{
    if (strlen(params) == 0) {
        uart_printf("Usage: set_limit:ch0=xx.xx,ch1=xx.xx,ch2=xx.xx\r\n");
        return;
    }

    // 查找冒号分隔符
    char *limit_str = strchr(params, ':');
    if (limit_str == NULL) {
        uart_printf("Invalid format. Use: set_limit:ch0=xx.xx,ch1=xx.xx,ch2=xx.xx\r\n");
        return;
    }
    limit_str++; // 跳过冒号

    // 解析3通道阈值
    float ch0_limit = 3.3f, ch1_limit = 20.0f, ch2_limit = 10000.0f;
    uint8_t ch0_found = 0, ch1_found = 0, ch2_found = 0;

    // 解析ch0
    char *ch0_str = strstr(limit_str, "ch0=");
    if (ch0_str != NULL) {
        ch0_limit = atof(ch0_str + 4);
        ch0_found = 1;
    }

    // 解析ch1
    char *ch1_str = strstr(limit_str, "ch1=");
    if (ch1_str != NULL) {
        ch1_limit = atof(ch1_str + 4);
        ch1_found = 1;
    }

    // 解析ch2
    char *ch2_str = strstr(limit_str, "ch2=");
    if (ch2_str != NULL) {
        ch2_limit = atof(ch2_str + 4);
        ch2_found = 1;
    }

    // 检查是否至少找到一个通道
    if (!ch0_found && !ch1_found && !ch2_found) {
        uart_printf("Invalid limit format\r\n");
        return;
    }

    // 设置所有通道阈值
    if (config_set_all_limits(ch0_limit, ch1_limit, ch2_limit) == CONFIG_OK) {
        uart_printf("report:ok\r\n");
        // 保存配置到Flash和SD卡
        config_save_to_flash();
        config_save_to_sd();
        // 记录操作日志
        char log_buffer[128] = {0};
        snprintf(log_buffer, sizeof(log_buffer), "limits set: ch0=%.2f,ch1=%.2f,ch2=%.2f",
                 ch0_limit, ch1_limit, ch2_limit);
        sd_write_log_data(log_buffer);
    } else {
        uart_printf("Failed to set limits\r\n");
    }
}

/**
 * @brief 处理get_data指令 - 获取单次数据 (暂时使用现有单通道，后续扩展为3通道)
 * @param params 参数字符串
 * @retval None
 */
void handle_get_data_cmd(char *params)
{
    // 执行完整的多通道数据采集流程
    multi_channel_read_data();        // 读取3通道原始数据
    multi_channel_apply_ratios();     // 应用变比计算
    multi_channel_check_limits();     // 检查超限状态
    multi_channel_update_timestamp(); // 更新时间戳

    // 使用多通道格式化输出函数
    char output_buffer[128] = {0};
    multi_channel_format_output(output_buffer, sizeof(output_buffer), 0); // 不包含时间戳

    uart_printf("report:%s\r\n", output_buffer);
}

/**
 * @brief 处理start_sample指令 - 开始连续采样
 * @param params 参数字符串
 * @retval None
 */
void handle_start_sample_cmd(char *params)
{
    // 启动多通道连续采样 (文本模式)
    multi_channel_start_continuous_sampling_text();
    adc_start_sampling(); // 保持兼容性

    // 立即输出一次数据作为确认
    multi_channel_read_data();
    multi_channel_apply_ratios();
    multi_channel_check_limits();
    multi_channel_update_timestamp();

    char output_buffer[128] = {0};
    multi_channel_format_output(output_buffer, sizeof(output_buffer), 1); // 包含时间戳
    uart_printf("report:%s\r\n", output_buffer);

    // 记录操作日志
    char log_buffer[64] = {0};
    snprintf(log_buffer, sizeof(log_buffer), "continuous sampling started (text mode)");
    sd_write_log_data(log_buffer);
}

/**
 * @brief 处理stop_sample指令 - 停止连续采样
 * @param params 参数字符串
 * @retval None
 */
void handle_stop_sample_cmd(char *params)
{
    // 停止多通道连续采样
    multi_channel_stop_continuous_sampling();
    adc_stop_sampling(); // 保持兼容性
    uart_printf("report:ok\r\n");

    // 记录操作日志
    char log_buffer[64] = {0};
    snprintf(log_buffer, sizeof(log_buffer), "continuous sampling stopped");
    sd_write_log_data(log_buffer);
}

/**
 * @brief 处理设置采样间隔命令
 * @param params 参数字符串，格式：set_interval=5 (设置5秒间隔)
 */
void handle_set_interval_cmd(char *params)
{
    // 解析参数：set_interval=X (X为秒数)
    char *interval_str = strchr(params, '=');
    if (interval_str == NULL) {
        uart_printf("report:Invalid format. Use: set_interval=X (X=seconds)\r\n");
        return;
    }
    interval_str++; // 跳过等号

    // 转换为整数 (秒)
    int interval_seconds = atoi(interval_str);
    if (interval_seconds < 1 || interval_seconds > 60) {
        uart_printf("report:Invalid interval. Range: 1-60 seconds\r\n");
        return;
    }

    // 设置采样间隔 (转换为毫秒)
    uint32_t interval_ms = interval_seconds * 1000;
    g_adc_control.cycle_ms = interval_ms;

    // 保存到配置系统
    config_set_sample_cycle(interval_ms);
    config_save_to_flash();

    // 返回确认
    uart_printf("report:Sampling interval set to %d seconds\r\n", interval_seconds);

    // 记录操作日志
    char log_buffer[64] = {0};
    snprintf(log_buffer, sizeof(log_buffer), "sampling interval set to %d seconds", interval_seconds);
    sd_write_log_data(log_buffer);
}

/**
 * @brief 处理CRC计算辅助命令
 * @param params 参数字符串，格式：crc_calc=FFFF020800001 (计算指定数据的CRC)
 */
void handle_crc_calc_cmd(char *params)
{
    // 解析参数：crc_calc=HEX_DATA
    char *data_str = strchr(params, '=');
    if (data_str == NULL) {
        my_printf(&huart1, "report:Usage: crc_calc=HEX_DATA (e.g., crc_calc=FFFF02080001)\r\n");
        return;
    }
    data_str++; // 跳过等号

    // 检查数据长度
    size_t hex_len = strlen(data_str);
    if (hex_len == 0 || hex_len % 2 != 0) {
        my_printf(&huart1, "report:Invalid hex data. Must be even number of hex characters\r\n");
        return;
    }

    // 转换十六进制字符串为字节数组
    uint8_t bytes[32];
    size_t byte_count = 0;

    for (size_t i = 0; i < hex_len; i += 2) {
        if (byte_count >= sizeof(bytes)) {
            my_printf(&huart1, "report:Hex data too long. Maximum 32 bytes\r\n");
            return;
        }

        char hex_byte[3] = {data_str[i], data_str[i+1], '\0'};
        bytes[byte_count] = (uint8_t)strtol(hex_byte, NULL, 16);
        byte_count++;
    }

    // 计算CRC16
    extern uint16_t crc16_calculate(const uint8_t *data, size_t length);
    uint16_t crc = crc16_calculate(bytes, byte_count);

    // 生成完整的二进制协议命令 (添加CRC)
    char complete_cmd[128] = {0};
    strcpy(complete_cmd, data_str);

    // 添加CRC (小端序)
    char crc_str[5];
    snprintf(crc_str, sizeof(crc_str), "%02X%02X", (uint8_t)(crc & 0xFF), (uint8_t)(crc >> 8));
    strcat(complete_cmd, crc_str);

    // 返回结果
    my_printf(&huart1, "report:CRC16=0x%04X, Complete command=%s\r\n", crc, complete_cmd);

    // 记录操作日志
    char log_buffer[128] = {0};
    snprintf(log_buffer, sizeof(log_buffer), "CRC calculated for %s: 0x%04X", data_str, crc);
    sd_write_log_data(log_buffer);
}

// 测试函数已移除，保持代码简洁

/**
 * @brief 处理二进制协议命令
 * @param params 十六进制字符串参数
 */
void handle_binary_protocol_cmd(char *params)
{
    if (params == NULL) {
        uart_printf("Error: Invalid binary protocol\r\n");
        return;
    }

    // 解析二进制协议
    binary_protocol_t protocol;
    protocol_result_t result = binary_protocol_parse(params, &protocol);

    if (result != PROTOCOL_OK) {
        const char* error_msg = get_protocol_error_string(result);
        uart_printf("Error: Protocol parse failed - %s\r\n", error_msg);
        return;
    }

    // 调试信息 (临时)
    uart_printf("DEBUG: Protocol parse OK\r\n");

    // 检查设备ID匹配
    uint16_t current_device_id = device_id_get();

    // 调试信息 (临时)
    uart_printf("DEBUG: Binary cmd received, target_id=0x%04X, current_id=0x%04X, msg_type=0x%02X\r\n",
              protocol.device_id, current_device_id, protocol.msg_type);

    if (protocol.msg_type == MSG_TYPE_SET_DEVICE_ID) {
        // 设置设备ID命令使用特殊匹配逻辑
        uint8_t allowed = is_set_device_id_allowed(protocol.device_id, current_device_id);
        uart_printf("DEBUG: Set device ID allowed = %d\r\n", allowed);

        if (!allowed) {
            // 设备ID不匹配，忽略消息 (不回复)
            uart_printf("DEBUG: Device ID mismatch, ignoring command\r\n");
            return;
        }
    } else {
        // 其他命令使用标准匹配逻辑
        if (!is_device_id_match(protocol.device_id, current_device_id)) {
            // 设备ID不匹配，忽略消息 (不回复)
            return;
        }
    }

    // 根据消息类型处理
    switch (protocol.msg_type) {
        case MSG_TYPE_SET_DEVICE_ID:
            handle_binary_set_device_id(&protocol);
            break;
        case MSG_TYPE_GET_DEVICE_ID:
            handle_binary_get_device_id(&protocol);
            break;
        case MSG_TYPE_SINGLE_READ:
            handle_binary_single_read(&protocol);
            break;
        case MSG_TYPE_CONTINUOUS_READ:
            handle_binary_continuous_read(&protocol);
            break;
        case MSG_TYPE_STOP_READ:
            handle_binary_stop_read(&protocol);
            break;
        default:
            uart_printf("Error: Unsupported message type: 0x%02X\r\n", protocol.msg_type);
            break;
    }
}



/**
 * @brief 检查设置设备ID命令的特殊匹配逻辑
 * @param received_id 接收到的设备ID
 * @param current_id 当前设备ID
 * @retval 1: 允许处理, 0: 拒绝处理
 */
uint8_t is_set_device_id_allowed(uint16_t received_id, uint16_t current_id)
{
    // 广播地址 (0xFFFF) 总是允许
    if (received_id == 0xFFFF) {
        return 1;
    }

    // 精确匹配当前设备ID
    if (received_id == current_id) {
        return 1;
    }

    // 特殊情况：如果当前设备ID是默认值(0x0001)，允许任何目标ID
    // 这样可以在初始化时设置设备ID
    if (current_id == 0x0001) {
        return 1;
    }

    return 0;
}

// 代码清理完成，所有测试函数已移除










// --- 二进制协议处理函数实现区域 ---

/**
 * @brief 检查字符串是否为十六进制格式
 * @param str 字符串
 * @retval 1: 是十六进制, 0: 不是十六进制
 */
uint8_t is_hex_string(const char *str)
{
    if (str == NULL || strlen(str) == 0) return 0;

    // 检查长度是否为偶数 (每字节2个字符)
    size_t len = strlen(str);
    if (len % 2 != 0) return 0;

    // 检查最小长度 (至少6字节 = 12个字符，支持更短的二进制协议)
    if (len < 12) return 0;

    // 检查所有字符是否为十六进制
    for (size_t i = 0; i < len; i++) {
        char c = str[i];
        if (!((c >= '0' && c <= '9') ||
              (c >= 'A' && c <= 'F') ||
              (c >= 'a' && c <= 'f'))) {
            return 0;
        }
    }

    return 1;
}

/**
 * @brief 处理二进制获取设备ID命令
 * @param request 请求协议
 */
void handle_binary_get_device_id(const binary_protocol_t *request)
{
    if (request == NULL) return;

    // 按照题目要求生成响应：000102000A010001F1C2
    uint16_t current_device_id = device_id_get();

    // 构建响应字节数组 - 按照题目要求的大端序格式
    uint8_t response_bytes[10];

    // 设备ID (2字节) - 大端序 (0001 -> 00 01)
    response_bytes[0] = (current_device_id >> 8) & 0xFF; // 高字节
    response_bytes[1] = current_device_id & 0xFF;        // 低字节

    // 消息类型 (1字节) - 0x02 应答
    response_bytes[2] = 0x02;

    // 报文长度 (2字节) - 0x000A (10字节) 大端序
    response_bytes[3] = 0x00;  // 高字节
    response_bytes[4] = 0x0A;  // 低字节

    // 协议版本 (1字节) - 0x01
    response_bytes[5] = 0x01;

    // 报文内容 (2字节) - 设备ID 大端序
    response_bytes[6] = (current_device_id >> 8) & 0xFF; // 高字节
    response_bytes[7] = current_device_id & 0xFF;        // 低字节

    // 计算CRC校验 (前8字节) - 使用题目专用算法
    extern uint16_t crc16_calculate_exam(const uint8_t *data, size_t length);
    uint16_t crc = crc16_calculate_exam(response_bytes, 8);

    // CRC校验 (2字节) - 大端序
    response_bytes[8] = (crc >> 8) & 0xFF; // 高字节
    response_bytes[9] = crc & 0xFF;        // 低字节

    // 转换为十六进制字符串
    char hex_response[32] = {0};
    for (int i = 0; i < 10; i++) {
        sprintf(hex_response + i * 2, "%02X", response_bytes[i]);
    }

    // 发送响应 - 按照题目要求格式
    uart_printf("report:%s\r\n", hex_response);

    // 记录操作日志
    char log_buffer[64] = {0};
    snprintf(log_buffer, sizeof(log_buffer), "Binary protocol: Get device ID response sent");
    sd_write_log_data(log_buffer);
}

/**
 * @brief 处理二进制设置设备ID命令
 * @param request 请求协议
 */
void handle_binary_set_device_id(const binary_protocol_t *request)
{
    if (request == NULL) return;

    // 调试信息 (临时)
    uart_printf("DEBUG: handle_binary_set_device_id called\r\n");

    // 从负载中提取新的设备ID (大端序)
    uint16_t new_device_id = ((uint16_t)request->payload[0] << 8) | (uint16_t)request->payload[1];

    uart_printf("DEBUG: New device ID = 0x%04X\r\n", new_device_id);

    // 设置新的设备ID
    HAL_StatusTypeDef set_result = device_id_set(new_device_id);

    // 按照第14题要求构建响应: 000202000A018000F151
    uint8_t response_bytes[10];

    // 设备ID (2字节) - 大端序
    // 成功则使用新ID，失败则使用原ID
    uint16_t response_device_id = (set_result == HAL_OK) ? new_device_id : device_id_get();
    response_bytes[0] = (response_device_id >> 8) & 0xFF; // 高字节
    response_bytes[1] = response_device_id & 0xFF;        // 低字节

    // 消息类型 (1字节) - 0x02 应答
    response_bytes[2] = 0x02;

    // 报文长度 (2字节) - 0x000A (10字节) 大端序
    response_bytes[3] = 0x00;  // 高字节
    response_bytes[4] = 0x0A;  // 低字节

    // 协议版本 (1字节) - 0x01
    response_bytes[5] = 0x01;

    // 报文内容 (2字节) - 响应码 大端序
    uint16_t response_code = (set_result == HAL_OK) ? 0x8000 : 0x7000;
    response_bytes[6] = (response_code >> 8) & 0xFF; // 高字节
    response_bytes[7] = response_code & 0xFF;        // 低字节

    // 计算CRC校验 (前8字节) - 使用题目专用算法
    extern uint16_t crc16_calculate_exam(const uint8_t *data, size_t length);
    uint16_t crc = crc16_calculate_exam(response_bytes, 8);

    // CRC校验 (2字节) - 大端序
    response_bytes[8] = (crc >> 8) & 0xFF; // 高字节
    response_bytes[9] = crc & 0xFF;        // 低字节

    // 转换为十六进制字符串
    char hex_response[32] = {0};
    for (int i = 0; i < 10; i++) {
        sprintf(hex_response + i * 2, "%02X", response_bytes[i]);
    }

    // 发送响应 - 按照题目要求格式
    uart_printf("report:%s\r\n", hex_response);

    // 如果设置成功，保存到Flash
    if (set_result == HAL_OK) {
        device_id_save_to_flash();
    }

    // 记录操作日志
    char log_buffer[64] = {0};
    snprintf(log_buffer, sizeof(log_buffer), "binary set device_id: 0x%04X %s",
             new_device_id, (set_result == HAL_OK) ? "success" : "failed");
    sd_write_log_data(log_buffer);
}

/**
 * @brief 处理二进制单次读取命令
 * @param request 请求协议
 */
void handle_binary_single_read(const binary_protocol_t *request)
{
    if (request == NULL) return;

    // 执行数据采集
    multi_channel_read_data();
    multi_channel_apply_ratios();
    multi_channel_update_timestamp();

    // 构建响应协议
    binary_protocol_t response;
    response.device_id = device_id_get();
    response.msg_type = MSG_TYPE_DATA;
    response.msg_length = 24; // 固定长度：6字节头 + 16字节负载 + 2字节CRC
    response.protocol_ver = BINARY_PROTOCOL_VERSION;

    // 设置负载：时间戳 + 3通道数据
    memset(response.payload, 0, sizeof(response.payload));
    protocol_pack_timestamp_and_channels(response.payload,
                                        g_multi_channel_data.timestamp,
                                        g_multi_channel_data.ch0_processed,
                                        g_multi_channel_data.ch1_processed,
                                        g_multi_channel_data.ch2_processed);

    // 生成十六进制字符串
    char hex_response[64] = {0};
    protocol_result_t result = binary_protocol_generate(&response, hex_response, sizeof(hex_response));

    if (result == PROTOCOL_OK) {
        uart_printf("report:%s\r\n", hex_response);

        // 记录操作日志
        char log_buffer[128] = {0};
        snprintf(log_buffer, sizeof(log_buffer), "binary single read: ch0=%.2f,ch1=%.2f,ch2=%.2f",
                 g_multi_channel_data.ch0_processed,
                 g_multi_channel_data.ch1_processed,
                 g_multi_channel_data.ch2_processed);
        sd_write_log_data(log_buffer);
    } else {
        uart_printf("Error: Failed to generate response\r\n");
    }
}

/**
 * @brief 处理二进制连续读取命令
 * @param request 请求协议
 */
void handle_binary_continuous_read(const binary_protocol_t *request)
{
    if (request == NULL) return;

    // 启动连续采样 (二进制模式)
    multi_channel_start_continuous_sampling_binary();

    // 立即执行一次数据采集并返回
    multi_channel_read_data();
    multi_channel_apply_ratios();
    multi_channel_update_timestamp();

    // 按照第16题要求构建响应: 0002010018016890481E4060A3D74164CCCD46959C00DF4F
    uint8_t response_bytes[24];

    // 设备ID (2字节) - 大端序
    uint16_t device_id = device_id_get();
    response_bytes[0] = (device_id >> 8) & 0xFF; // 高字节
    response_bytes[1] = device_id & 0xFF;        // 低字节

    // 消息类型 (1字节) - 0x01 数据
    response_bytes[2] = 0x01;

    // 报文长度 (2字节) - 0x0018 (24字节) 大端序
    response_bytes[3] = 0x00;  // 高字节
    response_bytes[4] = 0x18;  // 低字节

    // 协议版本 (1字节) - 0x01
    response_bytes[5] = 0x01;

    // 时间戳 (4字节) - UNIX时间戳 大端序
    uint32_t timestamp = g_multi_channel_data.timestamp;
    response_bytes[6] = (timestamp >> 24) & 0xFF;
    response_bytes[7] = (timestamp >> 16) & 0xFF;
    response_bytes[8] = (timestamp >> 8) & 0xFF;
    response_bytes[9] = timestamp & 0xFF;

    // 通道0数据 (4字节) - IEEE 754浮点数 大端序
    union { float f; uint32_t i; } ch0_union;
    ch0_union.f = g_multi_channel_data.ch0_processed;
    response_bytes[10] = (ch0_union.i >> 24) & 0xFF;
    response_bytes[11] = (ch0_union.i >> 16) & 0xFF;
    response_bytes[12] = (ch0_union.i >> 8) & 0xFF;
    response_bytes[13] = ch0_union.i & 0xFF;

    // 通道1数据 (4字节) - IEEE 754浮点数 大端序
    union { float f; uint32_t i; } ch1_union;
    ch1_union.f = g_multi_channel_data.ch1_processed;
    response_bytes[14] = (ch1_union.i >> 24) & 0xFF;
    response_bytes[15] = (ch1_union.i >> 16) & 0xFF;
    response_bytes[16] = (ch1_union.i >> 8) & 0xFF;
    response_bytes[17] = ch1_union.i & 0xFF;

    // 通道2数据 (4字节) - IEEE 754浮点数 大端序
    union { float f; uint32_t i; } ch2_union;
    ch2_union.f = g_multi_channel_data.ch2_processed;
    response_bytes[18] = (ch2_union.i >> 24) & 0xFF;
    response_bytes[19] = (ch2_union.i >> 16) & 0xFF;
    response_bytes[20] = (ch2_union.i >> 8) & 0xFF;
    response_bytes[21] = ch2_union.i & 0xFF;

    // 计算CRC校验 (前22字节) - 使用题目专用算法
    extern uint16_t crc16_calculate_exam(const uint8_t *data, size_t length);
    uint16_t crc = crc16_calculate_exam(response_bytes, 22);

    // CRC校验 (2字节) - 大端序
    response_bytes[22] = (crc >> 8) & 0xFF; // 高字节
    response_bytes[23] = crc & 0xFF;        // 低字节

    // 转换为十六进制字符串
    char hex_response[64] = {0};
    for (int i = 0; i < 24; i++) {
        sprintf(hex_response + i * 2, "%02X", response_bytes[i]);
    }

    // 发送响应 - 按照题目要求格式
    uart_printf("report:%s\r\n", hex_response);

    // 记录操作日志
    char log_buffer[128] = {0};
    snprintf(log_buffer, sizeof(log_buffer), "binary continuous read started: ch0=%.2f,ch1=%.2f,ch2=%.2f",
             g_multi_channel_data.ch0_processed,
             g_multi_channel_data.ch1_processed,
             g_multi_channel_data.ch2_processed);
    sd_write_log_data(log_buffer);
}

/**
 * @brief 处理二进制停止读取命令
 * @param request 请求协议
 */
void handle_binary_stop_read(const binary_protocol_t *request)
{
    if (request == NULL) return;

    // 停止连续采样
    multi_channel_stop_continuous_sampling();

    // 按照第17题要求构建响应: 000202000A018000F151 (与第14题相同)
    uint8_t response_bytes[10];

    // 设备ID (2字节) - 大端序
    uint16_t device_id = device_id_get();
    response_bytes[0] = (device_id >> 8) & 0xFF; // 高字节
    response_bytes[1] = device_id & 0xFF;        // 低字节

    // 消息类型 (1字节) - 0x02 应答
    response_bytes[2] = 0x02;

    // 报文长度 (2字节) - 0x000A (10字节) 大端序
    response_bytes[3] = 0x00;  // 高字节
    response_bytes[4] = 0x0A;  // 低字节

    // 协议版本 (1字节) - 0x01
    response_bytes[5] = 0x01;

    // 报文内容 (2字节) - 0x8000 操作成功 大端序
    response_bytes[6] = 0x80;  // 高字节
    response_bytes[7] = 0x00;  // 低字节

    // 计算CRC校验 (前8字节) - 使用题目专用算法
    extern uint16_t crc16_calculate_exam(const uint8_t *data, size_t length);
    uint16_t crc = crc16_calculate_exam(response_bytes, 8);

    // CRC校验 (2字节) - 大端序
    response_bytes[8] = (crc >> 8) & 0xFF; // 高字节
    response_bytes[9] = crc & 0xFF;        // 低字节

    // 转换为十六进制字符串
    char hex_response[32] = {0};
    for (int i = 0; i < 10; i++) {
        sprintf(hex_response + i * 2, "%02X", response_bytes[i]);
    }

    // 发送响应 - 按照题目要求格式
    uart_printf("report:%s\r\n", hex_response);

    // 记录操作日志
    sd_write_log_data("binary continuous read stopped");
}

/**
 * @brief 处理uart_test指令 - 串口切换测试
 * @param params 参数字符串
 * @retval None
 */
void handle_uart_test_cmd(char *params)
{
#if UART_SELECT == 1
    my_printf(&huart1, "=== UART Switch Test ===\r\n");
    my_printf(&huart1, "Current UART: UART1\r\n");
    my_printf(&huart1, "Baud Rate: 460800\r\n");
    my_printf(&huart1, "GPIO: PA9(TX), PA10(RX)\r\n");
    my_printf(&huart1, "Protocol: Standard UART\r\n");
    my_printf(&huart1, "Test: Basic output - OK\r\n");
    my_printf(&huart1, "To switch: Change UART_SELECT to 2 in mydefine.h\r\n");
    my_printf(&huart1, "========================\r\n");
#elif UART_SELECT == 2
    my_printf(&huart2, "=== UART Switch Test ===\r\n");
    my_printf(&huart2, "Current UART: UART2 (RS485)\r\n");
    my_printf(&huart2, "Baud Rate: 9600\r\n");
    my_printf(&huart2, "GPIO: PA2(TX), PA3(RX), PA1(DE/RE)\r\n");
    my_printf(&huart2, "Protocol: RS485 with MAX3485\r\n");
    my_printf(&huart2, "Control: PA1 controls TX/RX switching\r\n");
    my_printf(&huart2, "Test: RS485 output - OK\r\n");
    my_printf(&huart2, "To switch: Change UART_SELECT to 1 in mydefine.h\r\n");
    my_printf(&huart2, "========================\r\n");

    // RS485控制测试
    my_printf(&huart2, "RS485 Control Test:\r\n");
    my_printf(&huart2, "TX Mode: PA1=HIGH, RX Mode: PA1=LOW\r\n");
    my_printf(&huart2, "Current Mode: RX (PA1=LOW)\r\n");
#endif

    // 记录操作日志
    sd_write_log_data("UART switch test executed");
}


