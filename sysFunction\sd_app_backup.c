#include "sd_app.h"
#include "rtc_app.h"        // RTC时间管理
#include "string.h"         // 字符串处理函数
#include "stdio.h"          // 标准输入输出函数

// --- 文件系统全局变量定义区域 ---

/**
 * @brief 四种数据类型的文件管理器数组
 * @details 分别管理：采样数据、超限数据、日志数据、HEX数据
 *          每个管理器独立跟踪文件名、记录数、创建时间等信息
 */
file_manager_t g_file_managers[4] = {0};

/**
 * @brief 日志ID管理器
 * @details 管理日志文件的唯一ID，支持掉电保持
 *          确保日志文件名的唯一性和连续性
 */
log_id_manager_t g_log_id_manager = {0, 0};

/**
 * @brief Flash中存储日志ID的文件名
 * @note  使用LittleFS在Flash中持久化存储日志ID
 */
#define LOG_ID_FLASH_FILE "log_id.dat"

/**
 * @brief SD卡应用初始化（完全非阻塞版本）
 * @retval None
 */
void sd_app_init(void)
{
    // 初始化文件管理器
    for (int i = 0; i < 4; i++) {
        memset(&g_file_managers[i], 0, sizeof(file_manager_t));
    }

    // 初始化日志ID管理器
    sd_log_id_init();

    // 暂时完全跳过SD卡初始化，避免任何可能的阻塞
    // sd_quick_init();
}

/**
 * @brief 快速SD卡初始化（非阻塞）
 * @retval FATFS结果
 */
FRESULT sd_quick_init(void)
{
    FRESULT fr;

    // 快速检查SD卡是否存在
    if (BSP_SD_IsDetected() != SD_PRESENT) {
        return FR_NOT_READY;
    }

    // 尝试挂载，设置短超时
    fr = sd_mount_filesystem();
    if (fr != FR_OK) {
        return fr;
    }

    // 快速创建目录，不等待
    sd_create_directories();

    return FR_OK;
}

/**
 * @brief 挂载SD卡文件系统
 * @retval FATFS结果
 */
FRESULT sd_mount_filesystem(void)
{
    FRESULT fr;
    static FATFS fs;

    // 尝试挂载SD卡文件系统
    fr = f_mount(&fs, "0:", 1);  // 强制挂载
    if (fr != FR_OK) {
        // 挂载失败，可能是SD卡未插入或格式化问题
        return fr;
    }

    return FR_OK;
}

/**
 * @brief 检查SD卡状态（暂时禁用）
 * @retval FATFS结果
 */
FRESULT sd_check_card_status(void)
{
    // 暂时直接返回错误，避免任何可能的阻塞
    return FR_NOT_READY;
}

/**
 * @brief 综合检测SD卡状态（暂时禁用）
 * @details 暂时直接返回错误，避免阻塞
 * @retval FATFS结果
 */
FRESULT sd_comprehensive_check(void)
{
    // 暂时直接返回错误，避免任何可能的阻塞
    return FR_NOT_READY;
}

/**
 * @brief 重新初始化SD卡
 * @details 当SD卡在系统启动后插入时使用
 * @retval FATFS结果
 */
FRESULT sd_reinitialize(void)
{
    FRESULT fr;

    // 重新初始化BSP层
    if (BSP_SD_Init() != MSD_OK) {
        return FR_NOT_READY;
    }

    // 重新挂载文件系统
    fr = sd_mount_filesystem();
    if (fr != FR_OK) {
        return fr;
    }

    // 创建目录结构
    fr = sd_create_directories();
    if (fr != FR_OK) {
        return fr;
    }

    return FR_OK;
}

/**
 * @brief SD卡状态监控任务（暂时禁用）
 * @retval None
 */
void sd_monitor_task(void)
{
    // 暂时什么都不做，避免任何可能的阻塞
    return;
}

/**
 * @brief 创建目录结构
 * @retval FATFS结果
 */
FRESULT sd_create_directories(void)
{
    FRESULT fr;

    // 首先检查SD卡状态
    fr = sd_check_card_status();
    if (fr != FR_OK) {
        return fr;
    }

    // 创建sample文件夹
    fr = f_mkdir(FOLDER_SAMPLE);
    if (fr != FR_OK && fr != FR_EXIST) {
        return fr;
    }

    // 创建overLimit文件夹
    fr = f_mkdir(FOLDER_OVERLIMIT);
    if (fr != FR_OK && fr != FR_EXIST) {
        return fr;
    }

    // 创建log文件夹
    fr = f_mkdir(FOLDER_LOG);
    if (fr != FR_OK && fr != FR_EXIST) {
        return fr;
    }

    // 创建hideData文件夹
    fr = f_mkdir(FOLDER_HIDEDATA);
    if (fr != FR_OK && fr != FR_EXIST) {
        return fr;
    }

    return FR_OK;
}

/**
 * @brief 获取日期时间字符串（格式：YYYYMMDDHHMMSS）
 * @param datetime_str 输出字符串缓冲区
 * @param str_size 缓冲区大小
 * @retval None
 */
void sd_get_datetime_string(char *datetime_str, size_t str_size)
{
    if (datetime_str == NULL || str_size == 0) return;

    RTC_TimeTypeDef current_time = {0};
    RTC_DateTypeDef current_date = {0};

    // 获取当前时间
    rtc_get_time_info(&current_time, &current_date);

    // 格式化为YYYYMMDDHHMMSS
    snprintf(datetime_str, str_size, "%04d%02d%02d%02d%02d%02d",
             current_date.Year + 2000,
             current_date.Month,
             current_date.Date,
             current_time.Hours,
             current_time.Minutes,
             current_time.Seconds);
}

/**
 * @brief 生成文件名
 * @param type 数据类型
 * @param filename 输出文件名缓冲区
 * @param filename_size 缓冲区大小
 * @retval None
 */
void sd_generate_filename(data_type_t type, char *filename, size_t filename_size)
{
    if (filename == NULL || filename_size == 0) return;

    char datetime_str[16] = {0};

    switch (type) {
        case DATA_TYPE_SAMPLE:
            sd_get_datetime_string(datetime_str, sizeof(datetime_str));
            snprintf(filename, filename_size, "%s/sampleData%s.txt", FOLDER_SAMPLE, datetime_str);
            break;

        case DATA_TYPE_OVERLIMIT:
            sd_get_datetime_string(datetime_str, sizeof(datetime_str));
            snprintf(filename, filename_size, "%s/overLimit%s.txt", FOLDER_OVERLIMIT, datetime_str);
            break;

        case DATA_TYPE_LOG:
            snprintf(filename, filename_size, "%s/log%lu.txt", FOLDER_LOG, g_log_id_manager.log_id);
            break;

        case DATA_TYPE_HIDEDATA:
            sd_get_datetime_string(datetime_str, sizeof(datetime_str));
            snprintf(filename, filename_size, "%s/hideData%s.txt", FOLDER_HIDEDATA, datetime_str);
            break;

        default:
            filename[0] = '\0';
            break;
    }
}

/**
 * @brief 检查并创建新文件
 * @param type 数据类型
 * @retval FATFS结果
 */
FRESULT sd_check_and_create_new_file(data_type_t type)
{
    file_manager_t *manager = &g_file_managers[type];

    // 检查是否需要创建新文件
    if (manager->record_count >= MAX_RECORDS_PER_FILE || strlen(manager->current_filename) == 0) {
        // 生成新文件名
        sd_generate_filename(type, manager->current_filename, sizeof(manager->current_filename));
        manager->record_count = 0;
        manager->file_creation_time = uwTick;

        // 对于日志文件，需要递增日志ID
        if (type == DATA_TYPE_LOG) {
            g_log_id_manager.log_id++;
            sd_save_log_id_to_flash();
        }
    }

    return FR_OK;
}

/**
 * @brief 通用数据写入函数（暂时禁用）
 * @param type 数据类型
 * @param data 要写入的数据
 * @retval FATFS结果
 */
FRESULT sd_write_data(data_type_t type, const char *data)
{
    // 暂时直接返回错误，避免任何可能的阻塞
    return FR_NOT_READY;
}

/**
 * @brief 写入采样数据
 * @param time_str 时间字符串
 * @param voltage 电压值
 * @retval FATFS结果
 */
FRESULT sd_write_sample_data(const char *time_str, float voltage)
{
    if (time_str == NULL) return FR_INVALID_PARAMETER;

    char data_buffer[128] = {0};
    // 按照样例格式：2025-01-01 00:30:10 1.5V
    snprintf(data_buffer, sizeof(data_buffer), "%s %.1fV", time_str, voltage);

    return sd_write_data(DATA_TYPE_SAMPLE, data_buffer);
}

/**
 * @brief 写入超限数据
 * @param time_str 时间字符串
 * @param voltage 电压值
 * @param limit 阈值
 * @retval FATFS结果
 */
FRESULT sd_write_overlimit_data(const char *time_str, float voltage, float limit)
{
    if (time_str == NULL) return FR_INVALID_PARAMETER;

    char data_buffer[128] = {0};
    // 按照样例格式：2025-01-01 00:30:10 30V limit 10V
    snprintf(data_buffer, sizeof(data_buffer), "%s %.0fV limit %.0fV",
             time_str, voltage, limit);

    return sd_write_data(DATA_TYPE_OVERLIMIT, data_buffer);
}

/**
 * @brief 写入日志数据
 * @param log_content 日志内容
 * @retval FATFS结果
 */
FRESULT sd_write_log_data(const char *log_content)
{
    if (log_content == NULL) return FR_INVALID_PARAMETER;

    char data_buffer[256] = {0};
    char time_buffer[32] = {0};

    // 获取当前时间
    rtc_format_current_time_string(time_buffer, sizeof(time_buffer));

    // 按照样例格式：2025-01-01 10:00:01 system init
    snprintf(data_buffer, sizeof(data_buffer), "%s %s", time_buffer, log_content);

    return sd_write_data(DATA_TYPE_LOG, data_buffer);
}

/**
 * @brief 写入HEX数据（包含原始数据和加密数据）
 * @param hex_data HEX数据字符串
 * @retval FATFS结果
 */
FRESULT sd_write_hidedata(const char *hex_data)
{
    if (hex_data == NULL) return FR_INVALID_PARAMETER;

    FRESULT fr;
    char data_buffer[256] = {0};
    char time_buffer[32] = {0};
    file_manager_t *manager = &g_file_managers[DATA_TYPE_HIDEDATA];

    // 获取当前时间
    rtc_format_current_time_string(time_buffer, sizeof(time_buffer));

    // 检查并创建新文件
    fr = sd_check_and_create_new_file(DATA_TYPE_HIDEDATA);
    if (fr != FR_OK) return fr;

    FIL file;
    UINT bytes_written;

    // 打开文件进行追加写入
    fr = f_open(&file, manager->current_filename, FA_WRITE | FA_OPEN_APPEND);
    if (fr != FR_OK) {
        fr = f_open(&file, manager->current_filename, FA_WRITE | FA_CREATE_NEW);
        if (fr != FR_OK) return fr;
    }

    // 按照样例格式写入：原始数据行 + hide数据行
    // 原始数据行：2025-01-01 00:30:10 1.5V
    extern adc_control_t g_adc_control;
    snprintf(data_buffer, sizeof(data_buffer), "%s %.1fV", time_buffer, g_adc_control.processed_voltage);
    fr = f_write(&file, data_buffer, strlen(data_buffer), &bytes_written);
    if (fr == FR_OK) {
        fr = f_write(&file, "\r\n", 2, &bytes_written);
    }

    // hide数据行：hide: XXXXXXXXXXXXXXXX
    if (fr == FR_OK) {
        snprintf(data_buffer, sizeof(data_buffer), "hide: %s", hex_data);
        fr = f_write(&file, data_buffer, strlen(data_buffer), &bytes_written);
        if (fr == FR_OK) {
            fr = f_write(&file, "\r\n", 2, &bytes_written);
            manager->record_count++;
        }
    }

    f_close(&file);
    return fr;
}

/**
 * @brief 日志ID初始化
 * @retval None
 */
void sd_log_id_init(void)
{
    if (!g_log_id_manager.initialized) {
        // 尝试从Flash加载日志ID
        if (sd_load_log_id_from_flash() != FR_OK) {
            // 如果加载失败，从0开始
            g_log_id_manager.log_id = 0;
        }
        g_log_id_manager.initialized = 1;
    }
}

/**
 * @brief 获取下一个日志ID
 * @retval 日志ID
 */
uint32_t sd_get_next_log_id(void)
{
    return g_log_id_manager.log_id;
}

/**
 * @brief 保存日志ID到Flash
 * @retval FATFS结果
 */
FRESULT sd_save_log_id_to_flash(void)
{
    // 使用直接Flash操作保存日志ID到指定地址
    uint32_t log_id_addr = 0x2000;  // 日志ID存储地址
    flash_result_t result;

    result = flash_direct_write(log_id_addr, &g_log_id_manager.log_id, sizeof(g_log_id_manager.log_id));
    if (result != FLASH_OK) {
        return FR_DISK_ERR;
    }

    return FR_OK;
}

/**
 * @brief 从Flash加载日志ID
 * @retval FATFS结果
 */
FRESULT sd_load_log_id_from_flash(void)
{
    // 使用直接Flash操作从指定地址读取日志ID
    uint32_t log_id_addr = 0x2000;  // 日志ID存储地址
    flash_result_t result;

    result = flash_direct_read(log_id_addr, &g_log_id_manager.log_id, sizeof(g_log_id_manager.log_id));
    if (result != FLASH_OK) {
        return FR_DISK_ERR;
    }

    return FR_OK;
}

