#include "sampling_board_app.h"
#include "config_app.h"
#include "rtc_app.h"
#include "sd_app.h"

// 全局变量定义
sampling_board_control_t g_sampling_board_control;
channel_filter_t g_channel_filters[SAMPLING_BOARD_CH_MAX];

// 精细校准数据表 - 0.00V-0.14V，0.01V间隔
const calibration_point_t fine_calibration_table[FINE_CALIBRATION_POINTS] = {
    {0.00f, 0.0360f}, {0.01f, 0.0377f}, {0.02f, 0.0407f}, {0.03f, 0.0435f}, {0.04f, 0.0467f},
    {0.05f, 0.0487f}, {0.06f, 0.0527f}, {0.07f, 0.0555f}, {0.08f, 0.0585f}, {0.09f, 0.0615f},
    {0.10f, 0.0643f}, {0.11f, 0.0673f}, {0.12f, 0.0703f}, {0.13f, 0.0733f}, {0.14f, 0.0763f}
};

// 粗糙校准数据表 - 0.0V-10.0V，0.1V间隔
const calibration_point_t coarse_calibration_table[COARSE_CALIBRATION_POINTS] = {
    {0.0f, 0.0384f}, {0.1f, 0.0643f}, {0.2f, 0.0939f}, {0.3f, 0.1236f}, {0.4f, 0.1534f},
    {0.5f, 0.1828f}, {0.6f, 0.2124f}, {0.7f, 0.2421f}, {0.8f, 0.2715f}, {0.9f, 0.3011f},
    {1.0f, 0.3309f}, {1.1f, 0.3606f}, {1.2f, 0.3902f}, {1.3f, 0.4198f}, {1.4f, 0.4496f},
    {1.5f, 0.4793f}, {1.6f, 0.5089f}, {1.7f, 0.5383f}, {1.8f, 0.5679f}, {1.9f, 0.5976f},
    {2.0f, 0.6272f}, {2.1f, 0.6568f}, {2.2f, 0.6864f}, {2.3f, 0.7161f}, {2.4f, 0.7457f},
    {2.5f, 0.7753f}, {2.6f, 0.8049f}, {2.7f, 0.8346f}, {2.8f, 0.8642f}, {2.9f, 0.8938f},
    {3.0f, 0.9234f}, {3.1f, 0.9532f}, {3.2f, 0.9829f}, {3.3f, 1.0125f}, {3.4f, 1.0419f},
    {3.5f, 1.0716f}, {3.6f, 1.1012f}, {3.7f, 1.1310f}, {3.8f, 1.1604f}, {3.9f, 1.1903f},
    {4.0f, 1.2199f}, {4.1f, 1.2497f}, {4.2f, 1.2791f}, {4.3f, 1.3088f}, {4.4f, 1.3384f},
    {4.5f, 1.3680f}, {4.6f, 1.3974f}, {4.7f, 1.4274f}, {4.8f, 1.4571f}, {4.9f, 1.4867f},
    {5.0f, 1.5163f}, {5.1f, 1.5459f}, {5.2f, 1.5756f}, {5.3f, 1.6050f}, {5.4f, 1.6348f},
    {5.5f, 1.6644f}, {5.6f, 1.6939f}, {5.7f, 1.7239f}, {5.8f, 1.7533f}, {5.9f, 1.7831f},
    {6.0f, 1.8124f}, {6.1f, 1.8422f}, {6.2f, 1.8720f}, {6.3f, 1.9016f}, {6.4f, 1.9312f},
    {6.5f, 1.9609f}, {6.6f, 1.9903f}, {6.7f, 2.0203f}, {6.8f, 2.0498f}, {6.9f, 2.0794f},
    {7.0f, 2.1090f}, {7.1f, 2.1390f}, {7.2f, 2.1684f}, {7.3f, 2.1981f}, {7.4f, 2.2279f},
    {7.5f, 2.2575f}, {7.6f, 2.2871f}, {7.7f, 2.3169f}, {7.8f, 2.3462f}, {7.9f, 2.3760f},
    {8.0f, 2.4054f}, {8.1f, 2.4354f}, {8.2f, 2.4647f}, {8.3f, 2.4945f}, {8.4f, 2.5241f},
    {8.5f, 2.5539f}, {8.6f, 2.5836f}, {8.7f, 2.6132f}, {8.8f, 2.6426f}, {8.9f, 2.6723f},
    {9.0f, 2.7023f}, {9.1f, 2.7321f}, {9.2f, 2.7617f}, {9.3f, 2.7911f}, {9.4f, 2.8207f},
    {9.5f, 2.8504f}, {9.6f, 2.8802f}, {9.7f, 2.9102f}, {9.8f, 2.9396f}, {9.9f, 2.9694f},
    {10.0f, 2.9989f}
};

// 电流校准数据表 - 基于您提供的实际测量数据（精确计算版）
const calibration_point_t current_calibration_table[CURRENT_CALIBRATION_POINTS] = {
    // 格式：{实际输入电流(mA), 对应的原始ADC读数精确平均值(V)}
    {0.01f,  0.3540f},   // 实际0.01mA → 0.3540V
    {1.4f,   0.1893f},   // 实际1.4mA  → 0.1893V (29个样本平均)
    {2.3f,   0.2732f},   // 实际2.3mA  → 0.2732V (46个样本平均)
    {3.39f,  0.3791f},   // 实际3.39mA → 0.3791V (39个样本平均)
    {4.32f,  0.4738f},   // 实际4.32mA → 0.4738V (46个样本平均)
    {5.38f,  0.5769f},   // 实际5.38mA → 0.5769V (44个样本平均)
    {6.3f,   0.6657f},   // 实际6.3mA  → 0.6657V (41个样本平均)
    {7.4f,   0.7785f},   // 实际7.4mA  → 0.7785V (32个样本平均)
    {8.29f,  0.8607f},   // 实际8.29mA → 0.8607V (46个样本平均)
    {9.37f,  0.9698f},   // 实际9.37mA → 0.9698V (36个样本平均)
    {10.33f, 1.0695f},   // 实际10.33mA → 1.0695V (29个样本平均)
    {11.45f, 1.1700f},   // 实际11.45mA → 1.1700V (使用稳定前6个样本)
    {12.35f, 1.2705f},   // 实际12.35mA → 1.2705V (38个样本平均)
    {13.42f, 1.3757f},   // 实际13.42mA → 1.3757V (29个样本平均)
    {14.35f, 1.4700f},   // 实际14.35mA → 1.4700V (27个样本平均)
    {15.4f,  1.5729f},   // 实际15.4mA  → 1.5729V (31个样本平均，使用稳定后值)
    {16.32f, 1.6550f},   // 实际16.32mA → 1.6550V (27个样本平均，使用稳定后值)
    {17.42f, 1.7750f},   // 实际17.42mA → 1.7750V (27个样本平均，使用稳定后值)
    {18.46f, 1.8675f},   // 实际18.46mA → 1.8675V (19个样本平均)
    {19.39f, 1.9645f},   // 实际19.39mA → 1.9645V (32个样本平均，使用稳定后值)
    {20.42f, 2.0605f},   // 实际20.42mA → 2.0605V (16个样本平均)
    {21.42f, 2.1655f},   // 实际21.42mA → 2.1655V (16个样本平均)
    {22.42f, 2.2575f},   // 实际22.42mA → 2.2575V (14个样本平均)
    {23.38f, 2.3512f},   // 实际23.38mA → 2.3512V (14个样本平均)
    {24.4f,  2.4635f}    // 实际24.4mA  → 2.4635V (14个样本平均)
};

/**
 * @brief 分段校准函数，智能选择精细或粗糙校准表
 * @param raw_voltage 原始电压值
 * @return 校准后的实际电压值
 */
float voltage_calibrate_segmented(float raw_voltage)
{
    // 判断使用哪个校准表
    // 如果原始值在精细表范围内（0.0360V-0.0763V），优先使用精细表
    if (raw_voltage >= fine_calibration_table[0].raw_voltage &&
        raw_voltage <= fine_calibration_table[FINE_CALIBRATION_POINTS-1].raw_voltage) {

        // 使用精细校准表进行插值
        for (int i = 0; i < FINE_CALIBRATION_POINTS - 1; i++) {
            if (raw_voltage >= fine_calibration_table[i].raw_voltage &&
                raw_voltage <= fine_calibration_table[i+1].raw_voltage) {

                float slope = (fine_calibration_table[i+1].input_voltage - fine_calibration_table[i].input_voltage) /
                             (fine_calibration_table[i+1].raw_voltage - fine_calibration_table[i].raw_voltage);

                return fine_calibration_table[i].input_voltage +
                       slope * (raw_voltage - fine_calibration_table[i].raw_voltage);
            }
        }
    }

    // 使用粗糙校准表
    // 处理低于最小值的情况
    if (raw_voltage <= coarse_calibration_table[0].raw_voltage) {
        return 0.0f;
    }

    // 处理高于最大值的情况
    if (raw_voltage >= coarse_calibration_table[COARSE_CALIBRATION_POINTS-1].raw_voltage) {
        int last = COARSE_CALIBRATION_POINTS - 1;
        float slope = (coarse_calibration_table[last].input_voltage - coarse_calibration_table[last-1].input_voltage) /
                     (coarse_calibration_table[last].raw_voltage - coarse_calibration_table[last-1].raw_voltage);
        return coarse_calibration_table[last].input_voltage +
               slope * (raw_voltage - coarse_calibration_table[last].raw_voltage);
    }

    // 在粗糙表范围内进行线性插值
    for (int i = 0; i < COARSE_CALIBRATION_POINTS - 1; i++) {
        if (raw_voltage >= coarse_calibration_table[i].raw_voltage &&
            raw_voltage <= coarse_calibration_table[i+1].raw_voltage) {

            float slope = (coarse_calibration_table[i+1].input_voltage - coarse_calibration_table[i].input_voltage) /
                         (coarse_calibration_table[i+1].raw_voltage - coarse_calibration_table[i].raw_voltage);

            return coarse_calibration_table[i].input_voltage +
                   slope * (raw_voltage - coarse_calibration_table[i].raw_voltage);
        }
    }

    return 0.0f;
}

/**
 * @brief 电流线性校准函数，基于4-20mA实测数据
 * @param raw_voltage 原始电压值
 * @return 校准后的电流值(mA)
 */
float current_calibrate_linear(float raw_voltage)
{
    // 处理低于最小值的情况
    if (raw_voltage <= current_calibration_table[1].raw_voltage) {
        // 小于1mA对应的电压时，返回0mA
        return 0.0f;
    }

    // 处理高于最大值的情况
    if (raw_voltage >= current_calibration_table[CURRENT_CALIBRATION_POINTS-1].raw_voltage) {
        // 使用最后两个点进行线性外推
        int last = CURRENT_CALIBRATION_POINTS - 1;
        float slope = (current_calibration_table[last].input_voltage - current_calibration_table[last-1].input_voltage) /
                     (current_calibration_table[last].raw_voltage - current_calibration_table[last-1].raw_voltage);
        return current_calibration_table[last].input_voltage +
               slope * (raw_voltage - current_calibration_table[last].raw_voltage);
    }

    // 在表格范围内进行线性插值
    for (int i = 0; i < CURRENT_CALIBRATION_POINTS - 1; i++) {
        if (raw_voltage >= current_calibration_table[i].raw_voltage &&
            raw_voltage <= current_calibration_table[i+1].raw_voltage) {

            // 线性插值计算
            float slope = (current_calibration_table[i+1].input_voltage - current_calibration_table[i].input_voltage) /
                         (current_calibration_table[i+1].raw_voltage - current_calibration_table[i].raw_voltage);

            return current_calibration_table[i].input_voltage +
                   slope * (raw_voltage - current_calibration_table[i].raw_voltage);
        }
    }

    return 0.0f;
}

// 电流滤波器实例
static current_filter_t current_filter = {0};

/**
 * @brief 电流滑动平均滤波器
 * @param new_value 新的电流值
 * @return 滤波后的电流值
 */
float current_filter_update(float new_value)
{
    // 移除旧值
    if (current_filter.count == CURRENT_FILTER_SIZE) {
        current_filter.sum -= current_filter.buffer[current_filter.index];
    } else {
        current_filter.count++;
    }

    // 添加新值
    current_filter.buffer[current_filter.index] = new_value;
    current_filter.sum += new_value;

    // 更新索引
    current_filter.index = (current_filter.index + 1) % CURRENT_FILTER_SIZE;

    // 返回平均值
    return current_filter.sum / current_filter.count;
}




/**
 * @brief 初始化采样板系统
 */
void sampling_board_init(void)
{
    // 初始化GD30AD3344芯片（可能失败，但不影响系统运行）
    my_printf(&huart1, "Initializing GD30AD3344...\r\n");
    GD30AD3344_Init();

    my_printf(&huart1, "GD30AD3344 initialization completed\r\n");
    
    // 初始化控制结构
    g_sampling_board_control.state = SAMPLING_IDLE;
    g_sampling_board_control.cycle = CYCLE_5S;
    g_sampling_board_control.cycle_ms = 5000;
    g_sampling_board_control.last_sample_time = 0;
    g_sampling_board_control.current_channel = 0;
    g_sampling_board_control.measure_mode = MEASURE_TYPE_VOLTAGE;
    
    // 初始化数据结构
    g_sampling_board_control.data.voltage_ch0 = 0.0f;
    g_sampling_board_control.data.voltage_ch1 = 0.0f;
    g_sampling_board_control.data.voltage_ch2 = 0.0f;
    g_sampling_board_control.data.voltage_ch3 = 0.0f;
    g_sampling_board_control.data.current = 0.0f;
    g_sampling_board_control.data.resistance = 0.0f;
    g_sampling_board_control.data.last_update_time = 0;
    g_sampling_board_control.data.data_valid = 0;
    
    // 重置滤波器
    sampling_board_reset_filters();
    
    my_printf(&huart1, "Sampling board initialized\r\n");
}

/**
 * @brief 采样板主任务，切换到电流测量模式（AIN1~GND）
 */
void sampling_board_task(void)
{
    // 读取AIN1~GND通道的原始数据（用于电流测量）
    float raw_result = GD30AD3344_AD_Read(GD30AD3344_CH_AIN1_GND, GD30AD3344_PGA_6V144);

    // 应用电流校准算法
    float calibrated_current = current_calibrate_linear(raw_result);

    // 应用滑动平均滤波器，减少跳变
    float filtered_current = current_filter_update(calibrated_current);

    //my_printf(&huart1, "current : %.3fmA\r\n", filtered_current);

    // 更新数据供其他功能使用
    g_sampling_board_control.data.voltage_ch0 = filtered_current; // 存储滤波后的电流值
    g_sampling_board_control.data.last_update_time = HAL_GetTick();
    g_sampling_board_control.data.data_valid = 1;
}

/**
 * @brief 开始采样板采样
 */
void sampling_board_start_sampling(void)
{
    g_sampling_board_control.state = SAMPLING_ACTIVE;
    g_sampling_board_control.last_sample_time = HAL_GetTick();
    
    my_printf(&huart1, "Sampling board started\r\n");
    my_printf(&huart1, "Sample cycle: %ds\r\n", g_sampling_board_control.cycle_ms / 1000);
    
    // 立即输出一次数据
    sampling_board_process_sample();
}

/**
 * @brief 停止采样板采样
 */
void sampling_board_stop_sampling(void)
{
    g_sampling_board_control.state = SAMPLING_IDLE;
    my_printf(&huart1, "Sampling board stopped\r\n");
}

/**
 * @brief 设置采样周期
 * @param cycle 采样周期
 */
void sampling_board_set_cycle(sampling_cycle_t cycle)
{
    g_sampling_board_control.cycle = cycle;
    
    switch (cycle) {
        case CYCLE_5S:
            g_sampling_board_control.cycle_ms = 5000;
            break;
        case CYCLE_10S:
            g_sampling_board_control.cycle_ms = 10000;
            break;
        case CYCLE_15S:
            g_sampling_board_control.cycle_ms = 15000;
            break;
        default:
            g_sampling_board_control.cycle_ms = 5000;
            break;
    }
}

/**
 * @brief 处理定时采样数据输出
 */
void sampling_board_process_sample(void)
{
    uint32_t current_time = HAL_GetTick();
    
    if ((current_time - g_sampling_board_control.last_sample_time) >= g_sampling_board_control.cycle_ms) {
        g_sampling_board_control.last_sample_time += g_sampling_board_control.cycle_ms;
        
        // 输出采样数据
        sampling_board_output_data();
    }
}

/**
 * @brief 读取指定通道的原始数据
 * @param channel 通道号
 * @return 读取到的电压值
 */
float sampling_board_read_channel(sampling_board_channel_t channel)
{
    GD30AD3344_Channel_TypeDef gd30_channel;
    
    // 映射通道
    switch (channel) {
        case SAMPLING_BOARD_CH0:
            gd30_channel = GD30AD3344_CH_AIN0_GND;
            break;
        case SAMPLING_BOARD_CH1:
            gd30_channel = GD30AD3344_CH_AIN1_GND;
            break;
        case SAMPLING_BOARD_CH2:
            gd30_channel = GD30AD3344_CH_AIN2_GND;
            break;
        case SAMPLING_BOARD_CH3:
            gd30_channel = GD30AD3344_CH_AIN3_GND;
            break;
        default:
            gd30_channel = GD30AD3344_CH_AIN0_GND;
            break;
    }
    
    // 读取AD值并转换为电压
    return GD30AD3344_AD_Read(gd30_channel, GD30AD3344_PGA_6V144);
}

/**
 * @brief 对指定通道进行滤波处理
 * @param channel 通道号
 * @param new_value 新的采样值
 * @return 滤波后的值
 */
float sampling_board_filter_channel(sampling_board_channel_t channel, float new_value)
{
    if (channel >= SAMPLING_BOARD_CH_MAX) {
        return new_value;
    }
    
    channel_filter_t *filter = &g_channel_filters[channel];
    
    // 存储新值
    filter->buffer[filter->index] = new_value;
    filter->index = (filter->index + 1) % SAMPLING_BOARD_FILTER_SIZE;
    
    if (!filter->filled && filter->index == 0) {
        filter->filled = 1;
    }
    
    // 计算平均值
    float sum = 0.0f;
    uint8_t count = filter->filled ? SAMPLING_BOARD_FILTER_SIZE : filter->index;
    
    for (uint8_t i = 0; i < count; i++) {
        sum += filter->buffer[i];
    }
    
    return sum / count;
}

/**
 * @brief 计算衍生值（电流、电阻等）
 */
void sampling_board_calculate_derived_values(void)
{
    // 根据测量模式计算衍生值
    switch (g_sampling_board_control.measure_mode) {
        case MEASURE_TYPE_VOLTAGE:
            // 电压测量模式，直接使用通道0的值
            break;
            
        case MEASURE_TYPE_CURRENT:
            // 电流测量模式，假设通道1为电流检测电阻上的电压
            // I = V / R_shunt (假设分流电阻为0.1欧姆)
            g_sampling_board_control.data.current = g_sampling_board_control.data.voltage_ch1 / 0.1f;
            break;
            
        case MEASURE_TYPE_RESISTANCE:
            // 电阻测量模式，使用分压原理
            // R = (V_ref - V_measure) * R_ref / V_measure
            if (g_sampling_board_control.data.voltage_ch2 > 0.01f) { // 避免除零
                float v_ref = 3.3f; // 参考电压
                float r_ref = 10000.0f; // 参考电阻10K
                g_sampling_board_control.data.resistance = 
                    (v_ref - g_sampling_board_control.data.voltage_ch2) * r_ref / g_sampling_board_control.data.voltage_ch2;
            }
            break;
    }
}

/**
 * @brief 输出采样数据（专注于电压测量）
 */
void sampling_board_output_data(void)
{
    char time_buffer[32] = {0};
    rtc_format_current_time_string(time_buffer, sizeof(time_buffer));

    // 专注于电压测量输出
    my_printf(&huart1, "%s Voltage=%.4fV (Calibrated)\r\n",
             time_buffer,
             g_sampling_board_control.data.voltage_ch0);

    // 保存数据到SD卡
//    char log_buffer[64] = {0};
//    snprintf(log_buffer, sizeof(log_buffer),
//             "Voltage: %.4fV",
//             g_sampling_board_control.data.voltage_ch0);
//    sd_write_sample_data(time_buffer, g_sampling_board_control.data.voltage_ch0);
}

/**
 * @brief 重置所有通道的滤波器
 */
void sampling_board_reset_filters(void)
{
    for (uint8_t i = 0; i < SAMPLING_BOARD_CH_MAX; i++) {
        g_channel_filters[i].index = 0;
        g_channel_filters[i].filled = 0;
        for (uint8_t j = 0; j < SAMPLING_BOARD_FILTER_SIZE; j++) {
            g_channel_filters[i].buffer[j] = 0.0f;
        }
    }
}

/**
 * @brief 设置测量模式
 * @param mode 测量模式
 */
void sampling_board_set_measure_mode(measure_type_t mode)
{
    g_sampling_board_control.measure_mode = mode;
    my_printf(&huart1, "Measure mode set to: %d\r\n", mode);
}

/**
 * @brief 获取测量模式
 * @return 当前测量模式
 */
measure_type_t sampling_board_get_measure_mode(void)
{
    return g_sampling_board_control.measure_mode;
}

/**
 * @brief 获取采样数据指针
 * @return 采样数据结构指针
 */
sampling_board_data_t* sampling_board_get_data(void)
{
    return &g_sampling_board_control.data;
}
