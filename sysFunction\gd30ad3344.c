/* Licence
* Company: MCUSTUDIO
* Auther: Ahypnis.
* Version: V0.10
* Time: 2025/08/05
* Note: 移植到STM32 HAL库
*/
#include "gd30ad3344.h"

uint8_t gd30_send_array[ARRAYSIZE];      // SPI DMA发送缓冲区
uint8_t gd30_receive_array[ARRAYSIZE];   // SPI DMA接收缓冲区
GD30AD3344 GD30AD3344_InitStruct;        // 配置结构体

/**
 * @brief 使用DMA发送并接收一个字节
 * @param byte 要发送的字节
 * @return 从SPI总线接收到的字节
 */
uint8_t spi_gd30ad3344_send_byte_dma(uint8_t byte)
{
    HAL_StatusTypeDef status;
    
    // 将数据放入发送缓冲区
    gd30_send_array[0] = byte;
    
    // 使用HAL库进行SPI DMA传输
    status = HAL_SPI_TransmitReceive_DMA(&hspi1, gd30_send_array, gd30_receive_array, 1);
    
    if (status != HAL_OK) {
        return 0; // 传输失败返回0
    }
    
    // 等待传输完成，添加超时机制
    uint32_t timeout = HAL_GetTick() + 1000; // 1秒超时
    while (HAL_SPI_GetState(&hspi1) != HAL_SPI_STATE_READY) {
        if (HAL_GetTick() > timeout) {
            uart_printf("GD30AD3344 SPI byte timeout!\r\n");
            return 0;
        }
    }
    
    // 返回接收到的数据
    return gd30_receive_array[0];
}

/**
 * @brief 使用DMA发送并接收一个半字（16位数据）
 * @param half_word 要发送的半字
 * @return 从SPI总线接收到的半字
 */
uint16_t spi_gd30ad3344_send_halfword_dma(uint16_t half_word)
{
    HAL_StatusTypeDef status;
    uint16_t rx_data;
    
    SPI_GD30AD3344_CS_LOW();
    
    // 准备发送数据（高字节在前）
    gd30_send_array[0] = (uint8_t)(half_word >> 8);
    gd30_send_array[1] = (uint8_t)half_word;
    
    // 使用HAL库进行SPI DMA传输
    status = HAL_SPI_TransmitReceive_DMA(&hspi1, gd30_send_array, gd30_receive_array, 2);
    
    if (status != HAL_OK) {
        SPI_GD30AD3344_CS_HIGH();
        return 0; // 传输失败返回0
    }
    
    // 等待传输完成，添加超时机制
    uint32_t timeout = HAL_GetTick() + 1000; // 1秒超时
    while (HAL_SPI_GetState(&hspi1) != HAL_SPI_STATE_READY) {
        if (HAL_GetTick() > timeout) {
            // 超时处理
            SPI_GD30AD3344_CS_HIGH();
            uart_printf("GD30AD3344 SPI timeout!\r\n");
            return 0;
        }
    }
    
    // 组合接收到的数据
    rx_data = (uint16_t)(gd30_receive_array[0] << 8);
    rx_data |= gd30_receive_array[1];
    
    SPI_GD30AD3344_CS_HIGH();
    return rx_data;
}

/**
 * @brief 使用DMA发送和接收多个字节
 * @param tx_buffer 发送缓冲区
 * @param rx_buffer 接收缓冲区
 * @param size 传输大小
 */
void spi_gd30ad3344_transmit_receive_dma(uint8_t *tx_buffer, uint8_t *rx_buffer, uint16_t size)
{
    HAL_StatusTypeDef status;
    
    // 检查传输大小是否超过缓冲区
    if (size > ARRAYSIZE) {
        size = ARRAYSIZE;
    }
    
    // 准备发送数据
    for (uint16_t i = 0; i < size; i++) {
        gd30_send_array[i] = tx_buffer[i];
    }
    
    SPI_GD30AD3344_CS_LOW();
    
    // 使用HAL库进行SPI DMA传输
    status = HAL_SPI_TransmitReceive_DMA(&hspi1, gd30_send_array, gd30_receive_array, size);
    
    if (status != HAL_OK) {
        SPI_GD30AD3344_CS_HIGH();
        return; // 传输失败直接返回
    }
    
    // 等待传输完成，添加超时机制
    uint32_t timeout = HAL_GetTick() + 1000; // 1秒超时
    while (HAL_SPI_GetState(&hspi1) != HAL_SPI_STATE_READY) {
        if (HAL_GetTick() > timeout) {
            uart_printf("GD30AD3344 SPI multi-byte timeout!\r\n");
            SPI_GD30AD3344_CS_HIGH();
            return;
        }
    }
    
    // 复制接收到的数据到接收缓冲区
    for (uint16_t i = 0; i < size; i++) {
        rx_buffer[i] = gd30_receive_array[i];
    }
    
    SPI_GD30AD3344_CS_HIGH();
}

/**
 * @brief 等待DMA传输完成
 */
void spi_gd30ad3344_wait_for_dma_end(void)
{
    // 等待SPI传输完成，添加超时机制
    uint32_t timeout = HAL_GetTick() + 1000; // 1秒超时
    while (HAL_SPI_GetState(&hspi1) != HAL_SPI_STATE_READY) {
        if (HAL_GetTick() > timeout) {
            uart_printf("GD30AD3344 wait DMA timeout!\r\n");
            return;
        }
    }
}

/**
 * @brief 初始化GD30AD3344
 */
void GD30AD3344_Init(void)
{
    // 配置GD30AD3344参数
    GD30AD3344_InitStruct.SS         = 0;        // 写状态:0无作用 1开始单次转换（默认） 读的时候总是返回0
    GD30AD3344_InitStruct.MUX        = 4;        // 0(默认)      1         2         3         4         5         6         7
                                                 // AIN0~AIN1 AIN0~AIN3 AIN1~AIN3 AIN2~AIN3 AIN0~GND  AIN1~GND  AIN2~GND  AIN3~GND
    GD30AD3344_InitStruct.PGA        = 0;        //    0         1       2(默认)     3         4         5         6         7
                                                 // ±6.144V   ±4.096V   ±2.048V   ±1.024V   ±0.512V   ±0.256V   ±0.256V  ±0.256V
    GD30AD3344_InitStruct.MODE       = 0;        // 0:连续转换模式    1:掉电，单次转换模式（默认）
    GD30AD3344_InitStruct.DR         = 1;        //    0         1         2         3         4         5         6         7
                                                 //  6.25SPS     12.5SPS   25SPS     50SPS     100SPS    250SPS    500SPS    1000SPS
    GD30AD3344_InitStruct.RESERVED_1 = 0;        // 保留:写的时候写1，读的时候返回0或1
    GD30AD3344_InitStruct.PULL_UP_EN = 0;        // 0:关闭DOUT引脚上拉电阻(默认)    1:开启DOUT引脚上拉电阻
    GD30AD3344_InitStruct.NOP        = 1;        // 0:不更新配置寄存器的数据  1:更新配置寄存器的数据(默认)  2:无效数据，且不更新配置寄存器数据
    GD30AD3344_InitStruct.RESERVED   = 1;        // 保留:写的时候写1，读的时候返回0或1

    // 初始化CS引脚为高电平
    SPI_GD30AD3344_CS_HIGH();
    HAL_Delay(10); // 等待芯片稳定

    // 发送配置数据
    uint16_t config_value = GD30AD3344_InitStruct_Value;
    uint16_t response = spi_gd30ad3344_send_halfword_dma(config_value);

    // 输出配置值和响应用于调试
    uart_printf("GD30AD3344 Config: 0x%04X, Response: 0x%04X\r\n", config_value, response);

    // 简单的连接测试
    if (response == 0x7FFF || response == 0xFFFF || response == 0x0000) {
        uart_printf("Warning: GD30AD3344 may not be connected properly!\r\n");
        uart_printf("Please check SPI connections (PA5/PA6/PA7/PA4)\r\n");
    } else {
        uart_printf("GD30AD3344 appears to be responding\r\n");
    }
}

/**
 * @brief 根据PGA设置获取对应的电压范围
 * @param PGA 增益设置
 * @return 对应的电压范围值
 */
float GD30AD3344_PGA_SET(GD30AD3344_PGA_TypeDef PGA)
{
    float PGA_DATA = 0.0;
    switch(PGA)
    {
        case GD30AD3344_PGA_6V144:
            PGA_DATA = 6.144;
            break;
        case GD30AD3344_PGA_4V096:
            PGA_DATA = 4.096;
            break;
        case GD30AD3344_PGA_2V048:
            PGA_DATA = 2.048;
            break;
        case GD30AD3344_PGA_1V024:
            PGA_DATA = 1.024;
            break;
        case GD30AD3344_PGA_0V512:
            PGA_DATA = 0.512;
            break;
        case GD30AD3344_PGA_0V256:
            PGA_DATA = 0.256;
            break;
        case GD30AD3344_PGA_0V064:
            PGA_DATA = 0.064;
            break;
    }
    return PGA_DATA;
}

/**
 * @brief 读取指定通道的AD值并转换为电压
 * @param CH 通道选择
 * @param Ref 增益参考
 * @return 转换后的电压值
 */
float GD30AD3344_AD_Read(GD30AD3344_Channel_TypeDef CH, GD30AD3344_PGA_TypeDef Ref)
{
    uint16_t raw_data;
    float result = 0.0;
    
    // 更新配置
    GD30AD3344_InitStruct.MUX = CH;   // 设置通道
    GD30AD3344_InitStruct.PGA = Ref;  // 设置增益

    // 发送配置
    spi_gd30ad3344_send_halfword_dma(GD30AD3344_InitStruct_Value);

    // 等待ADC稳定（通道切换后需要稳定时间）
    HAL_Delay(2); // 2ms稳定时间

    // 读取数据（发送0x0000读取上一次转换结果）
    raw_data = spi_gd30ad3344_send_halfword_dma(0x0000);

    // 数据有效性检查
    if (raw_data == 32767 || raw_data == 0xFFFF) {
        // 简化错误处理，减少输出
        static uint32_t error_counter = 0;
        if (++error_counter % 100 == 0) { // 每100次错误输出一次
            uart_printf("GD30AD3344: Invalid data - Check hardware\r\n");
        }
        return 0.0f; // 返回0表示无效数据
    }

    // 转换为电压值
    result = (float)raw_data * GD30AD3344_PGA_SET(Ref) / 32768.0f;
    
    return result;
}
