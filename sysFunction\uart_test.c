/**
 * @file    uart_test.c
 * @brief   串口切换功能测试程序
 * @details 测试串口1和串口2的切换功能
 * <AUTHOR> CIMC西门子杯竞赛项目组
 * @date    2025-01-11
 * @version 1.0
 */

#include "mydefine.h"

/**
 * @brief 测试串口切换功能
 * @param None
 * @retval None
 */
void uart_switch_test(void)
{
    // 显示当前使用的串口
#if UART_SELECT == 1
    uart_printf("Current UART: UART1 (460800 baud)\r\n");
    uart_printf("GPIO: PA9(TX), PA10(RX)\r\n");
#elif UART_SELECT == 2
    uart_printf("Current UART: UART2 (9600 baud)\r\n");
    uart_printf("GPIO: PA2(TX), PA3(RX)\r\n");
#endif

    uart_printf("UART switch test completed!\r\n");
    uart_printf("To switch UART, change UART_SELECT in mydefine.h\r\n");
    uart_printf("1 = UART1, 2 = UART2\r\n");
}

/**
 * @brief 测试串口数据收发功能（内联实现）
 * @param None
 * @retval None
 * @note 此函数已内联到usart_app.c中，避免链接问题
 */
