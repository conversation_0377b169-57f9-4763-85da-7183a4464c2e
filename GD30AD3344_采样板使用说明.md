# GD30AD3344采样板使用说明

## 1. 概述

本文档描述了如何在STM32工程中使用GD30AD3344采样板进行多通道电压、电流、电阻测量。

## 2. 硬件连接

### 2.1 SPI连接
- **SPI1_SCK (PA5)** → GD30AD3344 SCLK
- **SPI1_MISO (PA6)** → GD30AD3344 DOUT  
- **SPI1_MOSI (PA7)** → GD30AD3344 DIN
- **PA4 (CS)** → GD30AD3344 CS

### 2.2 电源连接
- **VCC** → 3.3V
- **GND** → GND

### 2.3 模拟输入
- **AIN0** → 通道0输入（电压测量）
- **AIN1** → 通道1输入（电流测量）
- **AIN2** → 通道2输入（电阻测量）
- **AIN3** → 通道3输入（备用）

## 3. 软件配置

### 3.1 文件结构
```
sysFunction/
├── gd30ad3344.h          # GD30AD3344驱动头文件
├── gd30ad3344.c          # GD30AD3344驱动实现
├── sampling_board_app.h  # 采样板应用层头文件
└── sampling_board_app.c  # 采样板应用层实现
```

### 3.2 初始化
系统启动时会自动初始化采样板：
```c
void sampling_board_init(void);  // 在scheduler_init()中调用
```

### 3.3 任务调度
采样板任务每50ms执行一次，轮询采集各通道数据：
```c
{sampling_board_task, 50, 0}  // 在scheduler_task[]中配置
```

## 4. 串口命令

### 4.1 开始采样
```
sb start
```
开始周期性采样并输出数据。

### 4.2 停止采样
```
sb stop
```
停止采样输出。

### 4.3 设置测量模式
```
sb mode <模式>
```
- `sb mode 0` - 电压测量模式
- `sb mode 1` - 电流测量模式  
- `sb mode 2` - 电阻测量模式
- `sb mode` - 查看当前模式

### 4.4 读取当前数据
```
sb read
```
立即读取并显示当前所有通道数据。

## 5. 数据输出格式

### 5.1 电压测量模式
```
2025-01-15 10:30:25 CH0=1.234V CH1=2.345V CH2=3.456V CH3=0.789V
```

### 5.2 电流测量模式
```
2025-01-15 10:30:25 Current=0.123A Voltage=1.234V
```

### 5.3 电阻测量模式
```
2025-01-15 10:30:25 Resistance=1234.5Ω Voltage=2.345V
```

## 6. 技术参数

### 6.1 测量范围
- **电压范围**: ±2.048V (默认PGA设置)
- **分辨率**: 16位 (65536级)
- **精度**: 约0.00006V/LSB

### 6.2 采样特性
- **采样频率**: 每通道约20Hz (50ms任务周期 × 4通道)
- **滤波**: 8点滑动平均滤波
- **通道数**: 4个单端输入通道

### 6.3 计算公式

#### 电流计算
```c
// 假设使用0.1Ω分流电阻
Current = Voltage_CH1 / 0.1f;  // 单位：安培
```

#### 电阻计算
```c
// 使用分压原理，参考电阻10KΩ
Resistance = (3.3V - Voltage_CH2) * 10000.0f / Voltage_CH2;  // 单位：欧姆
```

## 7. 调试信息

### 7.1 初始化信息
```
GD30AD3344 Config: 0x8583
Sampling board initialized
```

### 7.2 原始数据输出
```
Raw data: 32768  // 16位ADC原始值
```

## 8. 注意事项

1. **电压范围**: 输入电压不要超过±2.048V，否则会饱和
2. **CS信号**: PA4作为CS信号，确保连接正确
3. **SPI时序**: 使用SPI模式0 (CPOL=0, CPHA=1)
4. **DMA配置**: 已配置DMA2_Stream3(TX)和DMA2_Stream0(RX)
5. **滤波延迟**: 由于8点滤波，数据稳定需要约400ms

## 9. 故障排除

### 9.1 无数据输出
- 检查SPI连接
- 检查CS信号(PA4)
- 检查电源连接

### 9.2 数据异常
- 检查输入电压范围
- 检查参考地连接
- 重新初始化: 重启系统

### 9.3 通信错误
- 检查SPI时钟频率
- 检查DMA配置
- 使用示波器检查SPI信号

## 10. 扩展功能

### 10.1 增益调整
可修改`GD30AD3344_PGA_2V048`为其他增益设置：
- `GD30AD3344_PGA_6V144` - ±6.144V
- `GD30AD3344_PGA_4V096` - ±4.096V  
- `GD30AD3344_PGA_1V024` - ±1.024V
- `GD30AD3344_PGA_0V512` - ±0.512V

### 10.2 差分测量
可修改通道配置使用差分输入：
- `GD30AD3344_CH_AIN0_AIN1` - AIN0-AIN1差分
- `GD30AD3344_CH_AIN0_AIN3` - AIN0-AIN3差分

### 10.3 采样率调整
可修改任务调度周期来调整采样率：
```c
{sampling_board_task, 25, 0}  // 25ms = 40Hz每通道
```
