/**
 * @file    第1章_实践练习与代码示例.c
 * @brief   STM32系统架构与开发环境基础 - 实践练习代码
 * @details 通过实际代码示例帮助理解HAL库概念和系统架构
 * <AUTHOR>
 * @date    2025-01-07
 */

#include "main.h"
#include "stdio.h"

// ============================================================================
// 练习1：理解句柄概念
// ============================================================================

/**
 * @brief 演示UART句柄的使用
 * @note  句柄包含了外设的所有配置信息和状态
 */
void practice_uart_handle_demo(void)
{
    // 外部声明的UART句柄（在main.c中定义）
    extern UART_HandleTypeDef huart1;
    
    // 查看句柄中的关键信息
    printf("=== UART句柄信息 ===\r\n");
    printf("外设基地址: 0x%08X\r\n", (uint32_t)huart1.Instance);
    printf("波特率: %d\r\n", huart1.Init.BaudRate);
    printf("数据位: %d\r\n", huart1.Init.WordLength);
    printf("停止位: %d\r\n", huart1.Init.StopBits);
    printf("校验位: %d\r\n", huart1.Init.Parity);
    printf("当前状态: %d\r\n", huart1.gState);
    
    // 句柄的作用：所有UART操作都需要传入这个句柄
    uint8_t test_data[] = "Hello STM32!\r\n";
    HAL_UART_Transmit(&huart1, test_data, sizeof(test_data)-1, 1000);
}

/**
 * @brief 演示ADC句柄的使用
 */
void practice_adc_handle_demo(void)
{
    extern ADC_HandleTypeDef hadc1;
    
    printf("=== ADC句柄信息 ===\r\n");
    printf("外设基地址: 0x%08X\r\n", (uint32_t)hadc1.Instance);
    printf("分辨率: %d\r\n", hadc1.Init.Resolution);
    printf("扫描模式: %d\r\n", hadc1.Init.ScanConvMode);
    printf("连续转换: %d\r\n", hadc1.Init.ContinuousConvMode);
    printf("当前状态: %d\r\n", hadc1.State);
}

// ============================================================================
// 练习2：分析初始化顺序
// ============================================================================

/**
 * @brief 演示正确的初始化顺序
 * @note  顺序很重要！错误的顺序可能导致系统无法正常工作
 */
void practice_initialization_sequence(void)
{
    printf("=== 初始化顺序分析 ===\r\n");
    
    // 步骤1：HAL库初始化（必须最先执行）
    printf("1. HAL_Init() - 配置SysTick、中断优先级、Flash接口\r\n");
    printf("   当前系统时钟: %d Hz\r\n", HAL_GetTick());
    
    // 步骤2：系统时钟配置（在外设初始化之前）
    printf("2. SystemClock_Config() - 配置系统时钟到144MHz\r\n");
    printf("   系统时钟频率: %d Hz\r\n", SystemCoreClock);
    
    // 步骤3：外设初始化（按依赖关系顺序）
    printf("3. 外设初始化顺序:\r\n");
    printf("   - GPIO (基础，其他外设依赖)\r\n");
    printf("   - DMA (数据传输，多个外设使用)\r\n");
    printf("   - UART (通信接口)\r\n");
    printf("   - ADC (模拟输入)\r\n");
    printf("   - Timer (定时功能)\r\n");
    printf("   - I2C/SPI (外部设备通信)\r\n");
    
    // 步骤4：应用层初始化（在HAL初始化之后）
    printf("4. 应用层初始化:\r\n");
    printf("   - OLED显示初始化\r\n");
    printf("   - 环形缓冲区初始化\r\n");
    printf("   - 任务调度器初始化\r\n");
}

// ============================================================================
// 练习3：时钟配置计算
// ============================================================================

/**
 * @brief 演示时钟配置参数计算
 * @note  理解PLL时钟计算公式
 */
void practice_clock_calculation(void)
{
    printf("=== 时钟配置计算 ===\r\n");
    
    // 从SystemClock_Config()函数中的参数
    uint32_t HSE_VALUE = 30000000;  // 外部高速振荡器 30MHz
    uint32_t PLLM = 15;             // PLL分频系数
    uint32_t PLLN = 144;            // PLL倍频系数  
    uint32_t PLLP = 2;              // PLL输出分频系数
    
    // PLL时钟计算公式
    uint32_t VCO_Input = HSE_VALUE / PLLM;      // VCO输入频率
    uint32_t VCO_Output = VCO_Input * PLLN;     // VCO输出频率
    uint32_t SYSCLK = VCO_Output / PLLP;        // 系统时钟频率
    
    printf("HSE频率: %d MHz\r\n", HSE_VALUE / 1000000);
    printf("PLLM分频: %d\r\n", PLLM);
    printf("PLLN倍频: %d\r\n", PLLN);
    printf("PLLP分频: %d\r\n", PLLP);
    printf("VCO输入: %d MHz\r\n", VCO_Input / 1000000);
    printf("VCO输出: %d MHz\r\n", VCO_Output / 1000000);
    printf("系统时钟: %d MHz\r\n", SYSCLK / 1000000);
    printf("实际系统时钟: %d MHz\r\n", SystemCoreClock / 1000000);
}

// ============================================================================
// 练习4：HAL库 vs 寄存器操作对比
// ============================================================================

/**
 * @brief 演示寄存器操作方式（不推荐，仅用于理解）
 */
void practice_register_operation(void)
{
    printf("=== 寄存器操作示例 ===\r\n");
    
    // 注意：这里只是演示，实际项目中不推荐直接操作寄存器
    
    // 1. 使能GPIOA时钟（寄存器方式）
    RCC->AHB1ENR |= RCC_AHB1ENR_GPIOAEN;
    printf("通过寄存器使能GPIOA时钟\r\n");
    
    // 2. 配置PA5为输出模式（寄存器方式）
    GPIOA->MODER &= ~(3 << (5*2));    // 清除模式位
    GPIOA->MODER |= (1 << (5*2));     // 设置为输出模式
    printf("通过寄存器配置PA5为输出模式\r\n");
    
    // 3. 设置PA5输出高电平（寄存器方式）
    GPIOA->ODR |= (1 << 5);
    printf("通过寄存器设置PA5输出高电平\r\n");
}

/**
 * @brief 演示HAL库操作方式（推荐）
 */
void practice_hal_operation(void)
{
    printf("=== HAL库操作示例 ===\r\n");
    
    // 1. 使能GPIOA时钟（HAL方式）
    __HAL_RCC_GPIOA_CLK_ENABLE();
    printf("通过HAL库使能GPIOA时钟\r\n");
    
    // 2. 配置PA5为输出模式（HAL方式）
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = GPIO_PIN_5;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
    printf("通过HAL库配置PA5为输出模式\r\n");
    
    // 3. 设置PA5输出高电平（HAL方式）
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_5, GPIO_PIN_SET);
    printf("通过HAL库设置PA5输出高电平\r\n");
}

// ============================================================================
// 练习5：调试技巧演示
// ============================================================================

/**
 * @brief 演示调试宏的使用
 */
#ifdef DEBUG
    #define DEBUG_PRINT(fmt, ...) printf("[DEBUG] " fmt, ##__VA_ARGS__)
#else
    #define DEBUG_PRINT(fmt, ...)
#endif

/**
 * @brief 演示系统状态检查
 */
void practice_system_status_check(void)
{
    printf("=== 系统状态检查 ===\r\n");
    
    // 检查系统时钟
    if (SystemCoreClock == 144000000) {
        printf("✓ 系统时钟配置正确: %d MHz\r\n", SystemCoreClock / 1000000);
    } else {
        printf("✗ 系统时钟配置错误: %d MHz\r\n", SystemCoreClock / 1000000);
    }
    
    // 检查SysTick是否正常工作
    uint32_t tick1 = HAL_GetTick();
    HAL_Delay(10);
    uint32_t tick2 = HAL_GetTick();
    
    if ((tick2 - tick1) >= 10) {
        printf("✓ SysTick工作正常\r\n");
    } else {
        printf("✗ SysTick工作异常\r\n");
    }
    
    // 使用调试宏
    DEBUG_PRINT("调试信息：当前Tick值 = %d\r\n", HAL_GetTick());
}

/**
 * @brief 演示错误处理
 */
void practice_error_handling(void)
{
    printf("=== 错误处理演示 ===\r\n");
    
    // 模拟一个可能失败的操作
    HAL_StatusTypeDef status;
    
    // 假设这是一个UART发送操作
    uint8_t data[] = "Test";
    extern UART_HandleTypeDef huart1;
    status = HAL_UART_Transmit(&huart1, data, sizeof(data)-1, 1000);
    
    // 检查操作结果
    switch (status) {
        case HAL_OK:
            printf("✓ UART发送成功\r\n");
            break;
        case HAL_ERROR:
            printf("✗ UART发送失败\r\n");
            break;
        case HAL_BUSY:
            printf("⚠ UART忙碌中\r\n");
            break;
        case HAL_TIMEOUT:
            printf("⚠ UART发送超时\r\n");
            break;
        default:
            printf("? 未知状态\r\n");
            break;
    }
}

// ============================================================================
// 主练习函数
// ============================================================================

/**
 * @brief 第1章所有练习的入口函数
 * @note  在main.c中调用此函数来执行所有练习
 */
void chapter1_practice_all(void)
{
    printf("\r\n");
    printf("========================================\r\n");
    printf("  第1章：STM32系统架构与开发环境基础\r\n");
    printf("           实践练习开始\r\n");
    printf("========================================\r\n");
    
    // 执行所有练习
    practice_uart_handle_demo();
    printf("\r\n");
    
    practice_adc_handle_demo();
    printf("\r\n");
    
    practice_initialization_sequence();
    printf("\r\n");
    
    practice_clock_calculation();
    printf("\r\n");
    
    practice_register_operation();
    printf("\r\n");
    
    practice_hal_operation();
    printf("\r\n");
    
    practice_system_status_check();
    printf("\r\n");
    
    practice_error_handling();
    printf("\r\n");
    
    printf("========================================\r\n");
    printf("           实践练习结束\r\n");
    printf("========================================\r\n");
    printf("\r\n");
}

/**
 * @brief 文件结束标记
 * @note  在实际项目中，可以在main.c的适当位置调用chapter1_practice_all()
 *        来执行这些练习，帮助理解STM32系统架构
 */
