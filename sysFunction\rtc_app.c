#include "rtc_app.h"
#include "stm32f4xx_hal_rtc.h"
#include "stdio.h"
#include "string.h"

// Copyright (c) 2024 米醋电子工作室. All rights reserved.

RTC_TimeTypeDef time; // 全局时间结构体
RTC_DateTypeDef date; // 全局日期结构体

// 星期枚举转换表（暂时注释避免编译警告）
// static const char *weekday_names[] = {
//     "", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"};

// 月份名称表（暂时注释避免编译警告）
// static const char *month_names[] = {
//     "", "Jan", "Feb", "Mar", "Apr", "May", "Jun",
//     "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"};

void rtc_proc(void)
{
    HAL_RTC_GetTime(&hrtc, &time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &date, RTC_FORMAT_BIN);
}

// 解析时间字符串 "2025年01月01日12:00:30"
static HAL_StatusTypeDef parse_time_string(const char *time_str, RTC_TimeTypeDef *sTime, RTC_DateTypeDef *sDate)
{
    if (time_str == NULL || sTime == NULL || sDate == NULL)
    {
        return HAL_ERROR;
    }

    int year, month, day, hour, minute, second;
    int parsed = 0;

    // 尝试标准格式: "2025-01-01 12:00:30"
    parsed = sscanf(time_str, "%d-%d-%d %d:%d:%d", &year, &month, &day, &hour, &minute, &second);

    // 如果标准格式解析失败，尝试紧凑格式: "2025-01-01 01-30-10"
    if (parsed != 6)
    {
        parsed = sscanf(time_str, "%d-%d-%d %d-%d-%d", &year, &month, &day, &hour, &minute, &second);
    }

    // 如果所有格式都解析失败
    if (parsed != 6)
    {
        return HAL_ERROR; // 解析失败
    }

    // 参数验证
    if (year < 2000 || year > 2099 || month < 1 || month > 12 ||
        day < 1 || day > 31 || hour > 23 || minute > 59 || second > 59)
    {
        return HAL_ERROR;
    }

    // 设置时间结构体
    sTime->Hours = hour;
    sTime->Minutes = minute;
    sTime->Seconds = second;
    sTime->DayLightSaving = RTC_DAYLIGHTSAVING_NONE;
    sTime->StoreOperation = RTC_STOREOPERATION_RESET;

    // 设置日期结构体 (年份需要转换为2位数)
    sDate->Year = year - 2000; // 2025 -> 25
    sDate->Month = month;
    sDate->Date = day;
    sDate->WeekDay = RTC_WEEKDAY_MONDAY; // 默认设置为周一，实际应用中可计算

    return HAL_OK;
}

// 从字符串设置RTC时间
HAL_StatusTypeDef rtc_set_time_from_string(const char *time_str)
{
    if (time_str == NULL)
    {
        return HAL_ERROR;
    }

    RTC_TimeTypeDef sTime = {0};
    RTC_DateTypeDef sDate = {0};

    // 解析时间字符串
    if (parse_time_string(time_str, &sTime, &sDate) != HAL_OK)
    {
        return HAL_ERROR;
    }

    // 设置RTC日期（必须先设置日期再设置时间）
    if (HAL_RTC_SetDate(&hrtc, &sDate, RTC_FORMAT_BIN) != HAL_OK)
    {
        return HAL_ERROR;
    }

    // 设置RTC时间
    if (HAL_RTC_SetTime(&hrtc, &sTime, RTC_FORMAT_BIN) != HAL_OK)
    {
        return HAL_ERROR;
    }

    // 等待RTC寄存器同步（关键修复）
    HAL_Delay(10); // 短暂延时确保寄存器更新

    // 验证设置是否成功（调试用，可在竞赛时注释掉）
    RTC_TimeTypeDef verify_time = {0};
    RTC_DateTypeDef verify_date = {0};
    HAL_RTC_GetTime(&hrtc, &verify_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &verify_date, RTC_FORMAT_BIN);

    // 检查设置的时间是否与读取的时间一致
    if (verify_time.Hours != sTime.Hours ||
        verify_time.Minutes != sTime.Minutes ||
        verify_date.Year != sDate.Year ||
        verify_date.Month != sDate.Month ||
        verify_date.Date != sDate.Date) {
        // 如果时间设置失败，返回错误
        return HAL_ERROR;
    }

    return HAL_OK;
}

// 打印当前时间
void rtc_print_current_time(void)
{
    RTC_TimeTypeDef current_time = {0};
    RTC_DateTypeDef current_date = {0};

    // 获取当前时间和日期
    HAL_RTC_GetTime(&hrtc, &current_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_date, RTC_FORMAT_BIN);

    // 格式化输出时间 "2025-01-01 12:00:30"
    my_printf(&huart1, "%04d-%02d-%02d %02d:%02d:%02d\r\n",
              current_date.Year + 2000, // 转换回4位年份
              current_date.Month,
              current_date.Date,
              current_time.Hours,
              current_time.Minutes,
              current_time.Seconds);
}

// 获取当前时间信息（扩展功能）
void rtc_get_time_info(RTC_TimeTypeDef *current_time, RTC_DateTypeDef *current_date)
{
    if (current_time != NULL && current_date != NULL)
    {
        HAL_RTC_GetTime(&hrtc, current_time, RTC_FORMAT_BIN);
        HAL_RTC_GetDate(&hrtc, current_date, RTC_FORMAT_BIN);
    }
}

// 格式化时间输出（扩展功能）
void format_time_output(const RTC_TimeTypeDef *sTime, const RTC_DateTypeDef *sDate, char *buffer, size_t buffer_size)
{
    if (sTime != NULL && sDate != NULL && buffer != NULL && buffer_size > 0)
    {
        snprintf(buffer, buffer_size, "%04d-%02d-%02d %02d:%02d:%02d",
                 sDate->Year + 2000,
                 sDate->Month,
                 sDate->Date,
                 sTime->Hours,
                 sTime->Minutes,
                 sTime->Seconds);
    }
}

// === 竞赛要求的格式化函数 ===
/**
 * @brief 格式化当前时间为字符串（竞赛输出格式）
 * @details 按照竞赛要求格式化当前RTC时间为标准字符串格式
 *          输出格式：YYYY-MM-DD HH:MM:SS
 *          例如：2025-01-01 12:00:30
 * @param buffer 输出缓冲区指针
 * @param buffer_size 缓冲区大小（建议至少20字节）
 * @retval None
 * @note 此函数用于"RTC Config"和"RTC now"指令的时间显示
 */
void rtc_format_current_time_string(char *buffer, size_t buffer_size)
{
    // 参数有效性检查
    if (buffer == NULL || buffer_size == 0) {
        return;
    }

    RTC_TimeTypeDef current_time = {0};
    RTC_DateTypeDef current_date = {0};

    // 获取当前时间和日期（必须先读时间再读日期）
    HAL_RTC_GetTime(&hrtc, &current_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_date, RTC_FORMAT_BIN);

    // 格式化为竞赛要求的标准格式："YYYY-MM-DD HH:MM:SS"
    snprintf(buffer, buffer_size, "%04d-%02d-%02d %02d:%02d:%02d",
             current_date.Year + 2000,  // 转换为4位年份
             current_date.Month,
             current_date.Date,
             current_time.Hours,
             current_time.Minutes,
             current_time.Seconds);
}

// === Unix时间戳转换功能（用于数据隐藏功能） ===

/**
 * @brief 将RTC时间转换为Unix时间戳
 * @details 按照竞赛要求4（数据处理）实现Unix时间戳转换
 *          用于hide指令的数据隐藏功能
 *          例如：2025-01-01 12:30:45 → 1735705845 → 6774C4F5
 *
 *          重要：包含时区修正，减去8小时以适配赛事方解码程序
 *          赛事方解码程序会在时间戳基础上增加8小时，所以我们预先减去8小时
 * @param sTime RTC时间结构体指针
 * @param sDate RTC日期结构体指针
 * @retval Unix时间戳（秒，已修正时区）
 * @note 此函数专门用于竞赛要求的数据隐藏功能，包含时区修正
 */
uint32_t rtc_convert_to_unix_timestamp(const RTC_TimeTypeDef *sTime, const RTC_DateTypeDef *sDate)
{
    if (sTime == NULL || sDate == NULL) {
        return 0;
    }

    // Unix时间戳基准：1970年1月1日 00:00:00 UTC
    int year = sDate->Year + 2000;  // 转换为4位年份
    int month = sDate->Month;
    int day = sDate->Date;
    int hour = sTime->Hours;
    int minute = sTime->Minutes;
    int second = sTime->Seconds;

    // 计算从1970年到指定年份的天数
    uint32_t days = 0;

    // 计算年份贡献的天数
    for (int y = 1970; y < year; y++) {
        if ((y % 4 == 0 && y % 100 != 0) || (y % 400 == 0)) {
            days += 366;  // 闰年
        } else {
            days += 365;  // 平年
        }
    }

    // 每月天数表（平年）
    static const int days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};

    // 计算月份贡献的天数
    for (int m = 1; m < month; m++) {
        days += days_in_month[m - 1];
        // 如果是闰年且已经过了2月，需要加1天
        if (m == 2 && ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0))) {
            days += 1;
        }
    }

    // 加上当月的天数（减1因为当天还没过完）
    days += (day - 1);

    // 转换为秒
    uint32_t timestamp = days * 24 * 3600 + hour * 3600 + minute * 60 + second;

    // 修正时区问题：赛事方解码程序会增加8小时，所以我们减去8小时（28800秒）
    // 这样解码后的时间就是正确的本地时间
    timestamp -= 28800;  // 8 * 3600 = 28800秒

    return timestamp;
}

/**
 * @brief 获取当前RTC时间的Unix时间戳
 * @details 获取当前时间并转换为Unix时间戳，用于数据隐藏功能
 *          包含时区修正，适配赛事方解码程序的8小时偏移
 * @retval 当前Unix时间戳（秒，已修正时区）
 * @note 此函数用于adc_convert_to_hex()中的时间戳获取
 */
uint32_t rtc_get_unix_timestamp_now(void)
{
    RTC_TimeTypeDef current_time = {0};
    RTC_DateTypeDef current_date = {0};

    // 获取当前时间和日期
    HAL_RTC_GetTime(&hrtc, &current_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_date, RTC_FORMAT_BIN);

    return rtc_convert_to_unix_timestamp(&current_time, &current_date);
}


