# 系统自检模块程序设计

## 1. 模块架构设计

### 1.1 核心数据结构

```c
// 自检结果枚举
typedef enum {
    SELFTEST_OK = 0,
    SELFTEST_FLASH_ERROR,
    SELFTEST_SD_ERROR,
    SELFTEST_RTC_ERROR
} selftest_result_t;

// 自检项目结构体 - 数据驱动的核心
typedef struct {
    uint8_t flash_ok;       // Flash检测结果
    uint8_t sd_ok;          // SD卡检测结果
    uint8_t rtc_ok;         // RTC检测结果
    uint32_t flash_id;      // Flash ID
    uint32_t sd_capacity;   // SD卡容量(KB)
    char rtc_time[32];      // RTC时间字符串
} selftest_info_t;

// 设备ID结构体
typedef struct {
    char team_number[16];       // 队伍编号
    uint32_t power_on_count;    // 上电次数计数器
    uint8_t initialized;        // 初始化标志
} device_info_t;
```

### 1.2 模块接口设计

```c
// === 核心自检接口 ===
selftest_result_t selftest_run_all(void);                          // 运行完整自检
selftest_result_t selftest_check_flash_simple(selftest_info_t *info); // 简化Flash检测
selftest_result_t selftest_check_sd(selftest_info_t *info);        // 检测SD卡
selftest_result_t selftest_check_rtc(selftest_info_t *info);       // 检测RTC
void selftest_print_results(const selftest_info_t *info);          // 打印自检结果

// === 设备ID管理接口 ===
void device_id_init(void);                      // 设备ID初始化
void device_id_load_from_flash(void);           // 从Flash加载设备ID
void device_id_save_to_flash(void);             // 保存设备ID到Flash
void device_id_print(void);                     // 打印设备ID
void system_startup_sequence(void);             // 系统启动序列
```

## 2. 分层检测策略实现

### 2.1 Flash分层检测

```c
selftest_result_t selftest_check_flash_simple(selftest_info_t *info)
{
    if (info == NULL) return SELFTEST_FLASH_ERROR;
    // === 物理通信层验证 ===
    // 步骤1：检查Flash ID是否有效
    info->flash_id = spi_flash_read_id();
    
    if (info->flash_id == 0x000000 || info->flash_id == 0xFFFFFF) {
        info->flash_id = spi_flash_read_id_alt();  // 备用读取方法
    }

    if (info->flash_id == 0x000000 || info->flash_id == 0xFFFFFF) {
        info->flash_ok = 0;
        return SELFTEST_FLASH_ERROR;  // 物理通信失败
    }
    // === 功能逻辑层验证 ===
    // 步骤2：检查能否从Flash读取到队伍ID
    if (strcmp(g_device_info.team_number, DEFAULT_TEAM_NUMBER) == 0) {
        // 如果队伍ID等于默认值"000"，说明Flash读写功能异常
        info->flash_ok = 0;
        return SELFTEST_FLASH_ERROR;
    }
    // Flash ID有效且能读取到队伍ID，Flash检测通过
    info->flash_ok = 1;
    return SELFTEST_OK;
}
```

### 2.2 SD卡故障自适应恢复

```c
selftest_result_t selftest_check_sd(selftest_info_t *info)
{
    if (info == NULL) return SELFTEST_SD_ERROR;
    
    FRESULT fr;
    DWORD free_clusters, total_sectors;
    FATFS *pfs;

    // === 初级检测 ===
    // 步骤1：使用综合检测方法
    fr = sd_comprehensive_check();
    if (fr == FR_OK) {
        // 获取SD卡容量信息
        fr = f_getfree("0:", &free_clusters, &pfs);
        if (fr == FR_OK) {
            total_sectors = (pfs->n_fatent - 2) * pfs->csize;
            info->sd_capacity = total_sectors / 2;  // 转换为KB
            info->sd_ok = 1;
            return SELFTEST_OK;
        }
    }

    // === 故障恢复 ===
    // 步骤2：第一次检测失败，尝试智能重新初始化
    if (sd_check_and_reinit_if_needed()) {
        // 重新初始化成功，再次尝试获取容量信息
        fr = f_getfree("0:", &free_clusters, &pfs);
        if (fr == FR_OK) {
            total_sectors = (pfs->n_fatent - 2) * pfs->csize;
            info->sd_capacity = total_sectors / 2;
            info->sd_ok = 1;
            return SELFTEST_OK;
        }
    }

    // === 最终判定 ===
    // 步骤3：所有尝试都失败，SD卡检测失败
    info->sd_ok = 0;
    info->sd_capacity = 0;
    return SELFTEST_SD_ERROR;
}
```

## 3. 数据驱动架构

### 3.1 生产者-消费者模式

```c
// === 主控制函数 - 协调者 ===
selftest_result_t selftest_run_all(void)
{
    selftest_info_t info = {0};  // 数据总线初始化
    
    // === 生产者函数 - 填充数据 ===
    selftest_check_flash_simple(&info);  // 填充Flash检测结果
    selftest_check_sd(&info);            // 填充SD卡检测结果
    selftest_check_rtc(&info);           // 填充RTC检测结果
    
    // === 消费者函数 - 处理数据 ===
    selftest_print_results(&info);       // 消费并打印结果
    
    // === 结果判定 ===
    if (!info.flash_ok) return SELFTEST_FLASH_ERROR;
    if (!info.sd_ok) return SELFTEST_SD_ERROR;
    if (!info.rtc_ok) return SELFTEST_RTC_ERROR;
    
    return SELFTEST_OK;
}
```

### 3.2 格式化输出实现

```c
void selftest_print_results(const selftest_info_t *info)
{
    if (info == NULL) return;
    
    my_printf(&huart1, "======system selftest======\r\n");
    // Flash检测结果
    if (info->flash_ok) {
        my_printf(&huart1, "flash.........ok\r\n");
    } else {
        my_printf(&huart1, "flash.........error\r\n");
    }
    // SD卡检测结果
    if (info->sd_ok) {
        my_printf(&huart1, "TF card..........ok\r\n");
    } else {
        my_printf(&huart1, "TF card..........error\r\n");
    }
    // 硬件详细信息
    my_printf(&huart1, "flash ID: 0x%06lX\r\n", info->flash_id);
    if (info->sd_ok) {
        my_printf(&huart1, "TF card memory: %lu KB\r\n", info->sd_capacity);
    } else {
        my_printf(&huart1, "can not find TF card\r\n");
    }
    my_printf(&huart1, "RTC: %s\r\n", info->rtc_time);
    my_printf(&huart1, "======system selftest======\r\n");
}
```

## 4. 模块化设计特点

### 4.1 高内聚低耦合

```c
// === 模块依赖关系 ===
#include "selftest_app.h"
#include "mydefine.h"       // 系统公共头文件
#include "gd25qxx.h"        // Flash驱动接口
#include "bsp_driver_sd.h"  // SD卡驱动接口
#include "rtc_app.h"        // RTC应用层接口
#include "usart_app.h"      // 串口应用层接口
#include "sd_app.h"         // SD卡应用层接口
#include "flash_app.h"      // Flash应用层接口
```

**设计原则：**
- **单一职责**：每个函数只负责一个硬件模块的检测
- **接口抽象**：通过标准化API与底层驱动交互
- **依赖注入**：通过参数传递数据结构，避免全局状态依赖

### 4.2 错误处理机制

```c
// === 参数验证 ===
if (info == NULL) return SELFTEST_FLASH_ERROR;
// === 多重验证 ===
if (info->flash_id == 0x000000 || info->flash_id == 0xFFFFFF) {
    info->flash_id = spi_flash_read_id_alt();  // 备用方法
}
// === 状态一致性检查 ===
if (strcmp(g_device_info.team_number, DEFAULT_TEAM_NUMBER) == 0) {
    info->flash_ok = 0;
    return SELFTEST_FLASH_ERROR;
}
```

## 5. 系统集成

### 5.1 启动序列集成

```c
void system_startup_sequence(void)
{
    // 1. 系统初始化标识
    my_printf(&huart1, "====system init====\r\n");
    // 2. 防重复记录机制
    uint8_t already_logged = sd_check_system_init_logged();
    if (!already_logged) {
        sd_write_log_data("system init");
        sd_mark_system_init_logged();
    }
    // 3. 设备ID显示
    device_id_print();  // 输出：Device_ID:2025-CIMC-2025478430
    // 4. 系统就绪标识
    my_printf(&huart1, "====system ready====\r\n");
}
```

### 5.2 调度器集成

```c
// 在scheduler_init()中的调用顺序
void scheduler_init(void)
{
    // ... 其他初始化
    device_id_init();                    // 设备ID初始化
    device_id_load_from_flash();         // 从Flash加载设备ID
    system_startup_sequence();           // 系统启动序列（包含自检）
    // ... 后续初始化
}
```

## 6. 设计优势

### 6.1 可维护性
- **模块化设计**：功能独立，易于测试和维护
- **标准化接口**：统一的函数命名和参数规范
- **清晰的数据流**：数据结构驱动的设计模式

### 6.2 可扩展性
- **插件式架构**：新增硬件检测只需添加对应函数
- **配置化输出**：支持多种输出格式（串口、缓冲区）
- **分层验证**：可根据需求调整验证深度

### 6.3 可靠性
- **多重验证**：物理层+逻辑层双重检测
- **故障恢复**：自动重试和智能恢复机制
- **状态一致性**：确保检测结果的准确性

### 6.4 竞赛适配性
- **标准格式输出**：完全符合竞赛要求的输出格式
- **关键信息提取**：准确显示Flash ID、SD卡容量、RTC时间
- **错误处理**：友好的错误提示信息
