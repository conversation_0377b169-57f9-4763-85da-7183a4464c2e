# 采样控制模块程序设计

## 2.4.1 模块开发需求分析

### 改变调度方式

#### 定时采样周期支持

```c
// 文件：sysFunction/sampling_types.h (第21-26行)
// 采样周期枚举 - 支持竞赛要求的三种周期
typedef enum {
    CYCLE_5S = 0,           // 5秒周期
    CYCLE_10S,              // 10秒周期
    CYCLE_15S               // 15秒周期
} sampling_cycle_t;

// 文件：sysFunction/adc_app.c (第147-165行)
void adc_set_cycle(sampling_cycle_t cycle) //设测量间隔
{
    g_adc_control.cycle = cycle;
    
    switch (cycle) {
        case CYCLE_5S:
            g_adc_control.cycle_ms = 5000; //5秒
            break;
        case CYCLE_10S:
            g_adc_control.cycle_ms = 10000; //10秒
            break;
        case CYCLE_15S:
            g_adc_control.cycle_ms = 15000; //15秒
            break;
        default:
            g_adc_control.cycle_ms = 5000; //默认5秒
            break;
    }
}
```

#### 事件驱动与状态管理

```c
// 文件：sysFunction/sampling_types.h (第14-19行)
// 采样控制状态枚举
typedef enum {
    SAMPLING_IDLE = 0,      // 空闲状态
    SAMPLING_ACTIVE,        // 采样活动状态
    SAMPLING_PAUSED         // 采样暂停状态
} sampling_state_t;

// 文件：sysFunction/adc_app.h (第8-18行)
// 数据采集控制结构体
typedef struct {
    sampling_state_t state;         // 采样状态
    sampling_cycle_t cycle;         // 采样周期
    uint32_t last_sample_time;      // 上次采样时间
    uint32_t cycle_ms;              // 当前周期毫秒数
    float processed_voltage;        // 处理后的电压值（应用变比）
    float display_voltage;          // OLED显示用的电压值（只在串口输出时更新）
    uint8_t over_limit;             // 超限标志
    uint8_t led1_blink_state;       // LED1闪烁状态
} adc_control_t;
```

### 数据处理和输出

#### 精确的时间戳关联

```c
// 文件：sysFunction/adc_app.c (第167-205行)
void adc_process_sample(void) //处理定时测量数据
{
    uint32_t current_time = HAL_GetTick();
    if ((current_time - g_adc_control.last_sample_time) >= g_adc_control.cycle_ms) { //时间到了
        g_adc_control.last_sample_time += g_adc_control.cycle_ms; //更新下次时间
        char time_buffer[32] = {0}; //获取当前时间
        rtc_format_current_time_string(time_buffer, sizeof(time_buffer));
        if (g_hide_mode_enabled) { //加密模式
            hex_data_t hex_data;
            char hex_buffer[32] = {0};
            adc_convert_to_hex(&hex_data);
            adc_format_hex_string(&hex_data, hex_buffer, sizeof(hex_buffer));
            my_printf(&huart1, "%s\r\n", hex_buffer);
        } else { //正常模式
            if (g_adc_control.over_limit) {
                my_printf(&huart1, "%s ch0=%.2fV OverLimit(%.2f)!\r\n",
                         time_buffer, g_adc_control.processed_voltage, config_get_limit());
            } else {
                my_printf(&huart1, "%s ch0=%.2fV\r\n",
                         time_buffer, g_adc_control.processed_voltage);
            }
        }
        g_adc_control.display_voltage = g_adc_control.processed_voltage; //更新显示值
        if (g_adc_control.over_limit) { //保存数据
            sd_write_overlimit_data(time_buffer, g_adc_control.processed_voltage, config_get_limit());
        } else if (!g_hide_mode_enabled) {
            sd_write_sample_data(time_buffer, g_adc_control.processed_voltage);
        }
        if (g_hide_mode_enabled) { //加密数据
            sd_write_hidedata_with_voltage(g_adc_control.processed_voltage, g_adc_control.over_limit);
        }
    }
}
```

#### 超限判断与标记

```c
// 文件：sysFunction/adc_app.c (第207-219行)
void adc_check_over_limit(void) //检查是否超限
{
    float limit = config_get_limit(); //用户设的限制值
    extern uint8_t ucLed[6]; //LED数组
    
    if (g_adc_control.processed_voltage > limit) {
        g_adc_control.over_limit = 1; //超限了
        ucLed[1] = 1; //亮LED2
    } else {
        g_adc_control.over_limit = 0; //正常
        ucLed[1] = 0; //关LED2
    }
}
```

### 运行状态指示

#### 采样活动指示

```c
// 文件：sysFunction/adc_app.c (第249-260行)
void adc_led1_blink_task(void) //LED1闪烁任务
{
    extern uint8_t ucLed[6]; //LED数组
    
    if (g_adc_control.state == SAMPLING_ACTIVE) {
        g_adc_control.led1_blink_state = !g_adc_control.led1_blink_state; //切换状态
        ucLed[0] = g_adc_control.led1_blink_state;
    } else {
        ucLed[0] = 0; //关闭
        g_adc_control.led1_blink_state = 0;
    }
}

// 文件：sysFunction/scheduler.c (第22-30行)
// 调度器任务配置 - LED1闪烁任务每1000ms执行一次
static task_t scheduler_task[] =
{
    {led_task,           1,    0},
    {btn_task,           5,    0},
    {uart_task,          5,    0},
    {adc_task,           100,  0},
    {adc_led1_blink_task, 1000, 0},  // LED1闪烁任务
    {oled_task,          100, 0}
};
```

## 2.4.2 模块设计理念与实现方案

### 2.4.2.1 基于状态机的确定性行为

#### 明确的状态定义

```c
// 文件：sysFunction/adc_app.c (第21-29行)
adc_control_t g_adc_control = { //电压测量总控制器
    .state = SAMPLING_IDLE, //开始时待机
    .cycle = CYCLE_5S, //默认5秒测一次
    .last_sample_time = 0, //上次测量时间
    .cycle_ms = 5000, //5秒=5000毫秒
    .processed_voltage = 0.0f, //测到的电压
    .over_limit = 0, //是否超限
    .led1_blink_state = 0 //LED闪烁状态
};

// 文件：sysFunction/adc_app.c (第80-103行)
void adc_task(void) //电压测量主函数，每0.1秒跑一次
{
    uint32_t adc_sum = 0; //累加测量结果
    for (uint16_t i = 0; i < ADC_DMA_BUFFER_SIZE; i++) { //把所有测量值加起来
        adc_sum += adc_dma_buffer[i];
    }
    adc_val = adc_sum / ADC_DMA_BUFFER_SIZE; //算平均值
    float raw_voltage = ((float)adc_val * 3.3f) / 4096.0f; //转成电压值
    float median_filtered = voltage_median_filter(raw_voltage); //去异常值
    voltage = voltage_sliding_average_filter(median_filtered); //平滑处理
    float ratio = config_get_ratio(); //用户设的比例
    g_adc_control.processed_voltage = voltage * ratio;
    adc_check_over_limit(); //检查是否超限
    if (g_adc_control.state == SAMPLING_ACTIVE) { //如果在定时测量
        adc_process_sample();
    }
}
```

#### 状态切换控制

```c
// 文件：sysFunction/adc_app.c (第129-135行)
void adc_start_sampling(void) //开始定时测量
{
    g_adc_control.state = SAMPLING_ACTIVE; //设为测量状态
    g_adc_control.last_sample_time = HAL_GetTick(); //记录开始时间
    
    adc_process_sample(); //立即测一次
}

// 文件：sysFunction/adc_app.c (第137-145行)
void adc_stop_sampling(void) //停止测量
{
    g_adc_control.state = SAMPLING_IDLE; //回到待机
    g_adc_control.over_limit = 0; //清除超限
    
    extern uint8_t ucLed[6]; //LED数组
    ucLed[0] = 0; //关LED1
    ucLed[1] = 0; //关LED2
}
```

### 2.4.2.2 分层解耦的协作式架构

#### 配置分离

```c
// 文件：sysFunction/adc_app.c (第95行)
float ratio = config_get_ratio(); //用户设的比例

// 文件：sysFunction/adc_app.c (第209行)
float limit = config_get_limit(); //用户设的限制值

// 文件：sysFunction/adc_app.c (第125行)
sampling_cycle_t saved_cycle = config_get_sample_cycle(); //加载用户设置
```

#### 时间服务分离

```c
// 文件：sysFunction/adc_app.c (第175行)
rtc_format_current_time_string(time_buffer, sizeof(time_buffer));

// 文件：sysFunction/adc_app.c (第225行)
hex_data->timestamp = rtc_get_unix_timestamp_now(); //时间戳
```

#### 存储分离

```c
// 文件：sysFunction/adc_app.c (第195-203行)
if (g_adc_control.over_limit) { //保存数据
    sd_write_overlimit_data(time_buffer, g_adc_control.processed_voltage, config_get_limit());
} else if (!g_hide_mode_enabled) {
    sd_write_sample_data(time_buffer, g_adc_control.processed_voltage);
}

if (g_hide_mode_enabled) { //加密数据
    sd_write_hidedata_with_voltage(g_adc_control.processed_voltage, g_adc_control.over_limit);
}
```

### 2.4.2.3 智能数据策略

#### 双模式输出与存储

```c
// 文件：sysFunction/adc_app.c (第31行)
uint8_t g_hide_mode_enabled = 0; //数据加密开关

// 文件：sysFunction/adc_app.h (第20-25行)
// HEX数据结构体
typedef struct {
    uint32_t timestamp;             // Unix时间戳
    uint16_t voltage_integer;       // 电压整数部分
    uint16_t voltage_decimal;       // 电压小数部分
} hex_data_t;

// 文件：sysFunction/adc_app.c (第221-229行)
void adc_convert_to_hex(hex_data_t *hex_data) //转成加密格式
{
    if (hex_data == NULL) return;
    
    hex_data->timestamp = rtc_get_unix_timestamp_now(); //时间戳
    hex_data->voltage_integer = (uint16_t)g_adc_control.processed_voltage; //整数部分
    float decimal_part = g_adc_control.processed_voltage - hex_data->voltage_integer;
    hex_data->voltage_decimal = (uint16_t)(decimal_part * 65536.0f); //小数部分
}

// 文件：sysFunction/adc_app.c (第231-247行)
void adc_format_hex_string(const hex_data_t *hex_data, char *buffer, size_t buffer_size) //格式化16进制字符串
{
    if (hex_data == NULL || buffer == NULL || buffer_size == 0) return;
    
    snprintf(buffer, buffer_size, "%08lX%04X%04X", //组合成字符串
             hex_data->timestamp, //时间戳8位
             hex_data->voltage_integer, //电压整数4位
             hex_data->voltage_decimal); //电压小数4位
    
    if (g_adc_control.over_limit) { //超限加*号
        size_t len = strlen(buffer);
        if (len < buffer_size - 1) {
            buffer[len] = '*';
            buffer[len + 1] = '\0';
        }
    }
}
```

#### 超限状态的整合

```c
// 文件：sysFunction/adc_app.c (第207-219行)
void adc_check_over_limit(void) //检查是否超限
{
    float limit = config_get_limit(); //用户设的限制值
    extern uint8_t ucLed[6]; //LED数组
    
    if (g_adc_control.processed_voltage > limit) {
        g_adc_control.over_limit = 1; //超限了
        ucLed[1] = 1; //亮LED2 - 改变LED2状态
    } else {
        g_adc_control.over_limit = 0; //正常
        ucLed[1] = 0; //关LED2
    }
}

// 超限状态同时影响三个方面：
// 1. LED2状态：点亮报警灯
// 2. 串口输出格式：在正常模式下附加OverLimit(X.XX)
// 3. SD卡存储路径：调用sd_write_overlimit_data()函数，将数据写入overLimit.txt
```

## 2.4.3 数据滤波与处理

### 多级滤波算法

```c
// 文件：sysFunction/adc_app.c (第33-54行)
static float voltage_median_filter(float new_value) //去掉奇怪的电压值
{
    median_filter_buffer[median_filter_index] = new_value; //存新值
    median_filter_index = (median_filter_index + 1) % MEDIAN_FILTER_SIZE;
    
    float temp_buffer[MEDIAN_FILTER_SIZE]; //复制数据来排序
    for (uint8_t i = 0; i < MEDIAN_FILTER_SIZE; i++) {
        temp_buffer[i] = median_filter_buffer[i];
    }
    
    for (uint8_t i = 0; i < MEDIAN_FILTER_SIZE - 1; i++) { //冒泡排序
        for (uint8_t j = 0; j < MEDIAN_FILTER_SIZE - 1 - i; j++) {
            if (temp_buffer[j] > temp_buffer[j + 1]) {
                float temp = temp_buffer[j];
                temp_buffer[j] = temp_buffer[j + 1];
                temp_buffer[j + 1] = temp;
            }
        }
    }
    
    return temp_buffer[MEDIAN_FILTER_SIZE / 2]; //返回中间值
}

// 文件：sysFunction/adc_app.c (第56-73行)
static float voltage_sliding_average_filter(float new_voltage) //让显示稳定不跳
{
    voltage_filter_buffer[voltage_filter_index] = new_voltage; //存新值
    voltage_filter_index = (voltage_filter_index + 1) % VOLTAGE_FILTER_SIZE;
    
    if (!voltage_filter_filled && voltage_filter_index == 0) { //记录是否存满
        voltage_filter_filled = 1;
    }
    
    float sum = 0.0f; //计算平均值
    uint8_t count = voltage_filter_filled ? VOLTAGE_FILTER_SIZE : voltage_filter_index;
    
    for (uint8_t i = 0; i < count; i++) {
        sum += voltage_filter_buffer[i];
    }
    
    return sum / count; //返回平均值
}
```

## 2.4.4 系统集成与调度

### 任务调度配置

```c
// 文件：sysFunction/scheduler.c (第22-30行)
static task_t scheduler_task[] =
{
    {led_task,           1,    0},      // 1ms - LED状态更新
    {btn_task,           5,    0},      // 5ms - 按键扫描
    {uart_task,          5,    0},      // 5ms - 串口通信
    {adc_task,           100,  0},      // 100ms - ADC采样和处理
    {adc_led1_blink_task, 1000, 0},    // 1000ms - LED1闪烁指示
    {oled_task,          100, 0}       // 100ms - OLED显示
};
```

### 模块初始化

```c
// 文件：sysFunction/scheduler.c (第33-55行)
void scheduler_init(void)
{
    task_num = sizeof(scheduler_task) / sizeof(task_t);
    
    flash_app_init();
    config_app_init();
    adc_control_init();  // 采样控制初始化
    device_id_init();
    
    // ... 其他初始化
}
```
