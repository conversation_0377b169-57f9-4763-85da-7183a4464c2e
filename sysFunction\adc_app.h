#ifndef __ADC_APP_H_
#define __ADC_APP_H_
#define ADC_DMA_BUFFER_SIZE 32 // DMA缓冲区大小，可以根据需要调整
#include "stdint.h"
#include "mydefine.h"
#include "sampling_types.h"
#include "gd30ad3344.h"  // GD30AD3344硬件接口

// === 硬件接口配置 ===
// 取消注释下面的宏定义来启用真实硬件接口
#define USE_REAL_HARDWARE_ADC

// 硬件校准系数 (在启用真实硬件时使用)
#define CH0_CALIBRATION_FACTOR  1.0f    // 通道0校准系数
#define CH1_CALIBRATION_FACTOR  1.0f    // 通道1校准系数
#define CH2_CALIBRATION_FACTOR  1.0f    // 通道2校准系数

// 硬件通道映射 (可根据实际硬件连接调整)
#define HARDWARE_CH0_CHANNEL    GD30AD3344_CH_AIN0_GND  // 通道0映射到AIN0
#define HARDWARE_CH1_CHANNEL    GD30AD3344_CH_AIN1_GND  // 通道1映射到AIN1
#define HARDWARE_CH2_CHANNEL    GD30AD3344_CH_AIN2_GND  // 通道2映射到AIN2

// 硬件增益设置 (可根据信号范围调整)
#define HARDWARE_CH0_GAIN       GD30AD3344_PGA_6V144    // 通道0增益
#define HARDWARE_CH1_GAIN       GD30AD3344_PGA_6V144    // 通道1增益
#define HARDWARE_CH2_GAIN       GD30AD3344_PGA_6V144    // 通道2增益  // 包含采样相关类型定义

// 数据采集控制结构体 - 保持向后兼容
typedef struct {
    sampling_state_t state;         // 采样状态
    sampling_cycle_t cycle;         // 采样周期
    uint32_t last_sample_time;      // 上次采样时间
    uint32_t cycle_ms;              // 当前周期毫秒数
    float processed_voltage;        // 处理后的电压值（应用变比）- 兼容性字段
    float display_voltage;          // OLED显示用的电压值（只在串口输出时更新）
    uint8_t over_limit;             // 超限标志 - 兼容性字段
    uint8_t led1_blink_state;       // LED1闪烁状态
} adc_control_t;

// 多通道数据结构体 - 新增支持3通道
typedef struct {
    // 原始数据 (未应用变比)
    float ch0_raw;                  // 通道0原始数据
    float ch1_raw;                  // 通道1原始数据
    float ch2_raw;                  // 通道2原始数据

    // 变比后数据 (应用变比计算)
    float ch0_processed;            // 通道0变比后数据
    float ch1_processed;            // 通道1变比后数据
    float ch2_processed;            // 通道2变比后数据

    // 超限标志
    uint8_t ch0_over_limit;         // 通道0超限标志
    uint8_t ch1_over_limit;         // 通道1超限标志
    uint8_t ch2_over_limit;         // 通道2超限标志

    // 时间戳和状态
    uint32_t timestamp;             // 采样时间戳
    uint8_t data_valid;             // 数据有效性标志
    uint8_t sampling_active;        // 连续采样活动标志
    uint8_t binary_mode;            // 连续采样模式: 0=文本格式, 1=二进制格式
} multi_channel_data_t;

// HEX数据结构体
typedef struct {
    uint32_t timestamp;             // Unix时间戳
    uint16_t voltage_integer;       // 电压整数部分
    uint16_t voltage_decimal;       // 电压小数部分
} hex_data_t;

// 全局控制变量
extern adc_control_t g_adc_control;
extern multi_channel_data_t g_multi_channel_data;  // 多通道数据
extern uint8_t g_hide_mode_enabled;  // 加密存储模式标志

// 原有函数声明
void adc_task(void);
void dac_sin_init(void);
void adc_dma_init(void);
void adc_tim_dma_init(void);

// 新增函数声明
void adc_control_init(void);                                    // 采样控制初始化
void adc_start_sampling(void);                                  // 开始采样
void adc_stop_sampling(void);                                   // 停止采样
void adc_set_cycle(sampling_cycle_t cycle);                     // 设置采样周期
void adc_process_sample(void);                                  // 处理采样数据
void adc_check_over_limit(void);                                // 检查超限
void adc_convert_to_hex(hex_data_t *hex_data);                  // 转换为HEX格式
void adc_format_hex_string(const hex_data_t *hex_data, char *buffer, size_t buffer_size); // 格式化HEX字符串
void adc_led1_blink_task(void);                                 // LED1闪烁任务

// 多通道数据采集函数声明
void multi_channel_init(void);                                 // 多通道初始化
void multi_channel_read_data(void);                            // 读取3通道数据
void multi_channel_apply_ratios(void);                         // 应用变比计算
void multi_channel_check_limits(void);                         // 检查超限状态
void multi_channel_update_timestamp(void);                     // 更新时间戳
void multi_channel_format_output(char *buffer, size_t buffer_size, uint8_t include_timestamp); // 格式化输出
void multi_channel_get_data(float *ch0, float *ch1, float *ch2, uint8_t processed); // 获取通道数据
uint8_t multi_channel_get_over_limit_flags(void);              // 获取超限标志
void multi_channel_start_continuous_sampling(void);            // 开始连续采样 (兼容性，默认文本模式)
void multi_channel_start_continuous_sampling_text(void);       // 开始文本模式连续采样 (第8题)
void multi_channel_start_continuous_sampling_binary(void);     // 开始二进制模式连续采样 (第16题)
void multi_channel_stop_continuous_sampling(void);             // 停止连续采样
void multi_channel_send_binary_data(void);                     // 发送二进制格式数据

// 硬件接口管理函数
void multi_channel_hardware_init(void);                        // 硬件接口初始化
const char* multi_channel_get_hardware_status(void);           // 获取硬件状态
void multi_channel_enable_real_hardware(void);                 // 启用真实硬件
void multi_channel_enable_test_mode(void);                     // 启用测试模式

extern uint32_t adc_dma_buffer[ADC_DMA_BUFFER_SIZE]; // DMA 目标缓冲区
extern __IO uint32_t adc_val;                        // 用于存储计算后的平均 ADC 值
extern __IO float voltage;                           // 用于存储计算后的电压值
#endif /* __ADC_APP_H_ */
