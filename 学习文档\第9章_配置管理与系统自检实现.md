# 第9章：配置管理与系统自检实现

## 🎯 学习目标
- 深入理解INI文件格式和解析算法
- 掌握配置参数验证和范围检查机制
- 理解系统自检流程和硬件检测方法
- 学会设备ID管理和启动计数机制
- 掌握错误处理和配置恢复策略

## 📋 目录
1. [配置管理基础概念](#1-配置管理基础概念)
2. [INI文件格式与解析](#2-ini文件格式与解析)
3. [参数验证机制](#3-参数验证机制)
4. [系统自检流程](#4-系统自检流程)
5. [设备ID管理](#5-设备id管理)
6. [错误处理与恢复](#6-错误处理与恢复)
7. [Flash配置存储](#7-flash配置存储)
8. [系统启动序列](#8-系统启动序列)
9. [实践练习](#9-实践练习)

---

## 1. 配置管理基础概念

### 1.1 什么是配置管理？
配置管理是指对系统参数进行统一管理、存储、验证和恢复的机制。

**配置管理的作用：**
- **参数持久化**: 断电后配置不丢失
- **参数验证**: 确保配置值在有效范围内
- **配置恢复**: 配置损坏时自动恢复默认值
- **多存储支持**: Flash和SD卡双重备份

### 1.2 项目中的配置参数
```c
typedef struct {
    float ratio;                    // 变比参数 (0.0 ~ 100.0)
    float limit;                    // 阈值参数 (0.0 ~ 200.0)
    sampling_cycle_t sample_cycle;  // 采样周期 (5s/10s/15s)
} config_params_t;

// 全局配置参数
config_params_t g_config_params = {
    .ratio = 1.0f,          // 默认变比1.0
    .limit = 1.0f,          // 默认阈值1.0V
    .sample_cycle = CYCLE_5S // 默认5秒采样
};
```

### 1.3 配置存储策略
```
配置参数存储层次：
1. 内存 (g_config_params) ← 运行时使用
2. Flash (CONFIG_FLASH_ADDR) ← 掉电保持
3. SD卡 (config.ini) ← 用户可编辑
```

---

## 2. INI文件格式与解析

### 2.1 INI文件格式规范
```ini
[Ratio]
Ch0 = 2.5

[Limit]
Ch0 = 10.00
```

**格式要求：**
- **节标题**: 用方括号包围，如`[Ratio]`
- **键值对**: 格式为`Key = Value`
- **注释**: 以`;`或`#`开头的行
- **空行**: 允许存在，解析时忽略

### 2.2 INI解析算法实现

#### 2.2.1 逐行解析主函数
```c
config_status_t parse_ini_file(const char *file_content, config_params_t *params)
{
    char line[CONFIG_MAX_LINE_LENGTH];
    char section[CONFIG_MAX_SECTION_NAME] = {0};
    char key[CONFIG_MAX_KEY_NAME];
    char value[CONFIG_MAX_VALUE_NAME];
    const char *ptr = file_content;
    int line_start = 0;
    int line_end = 0;

    // 初始化参数为默认值
    params->ratio = 1.0f;
    params->limit = 1.0f;
    params->sample_cycle = CYCLE_5S;

    // 逐行解析
    while (*ptr != '\0') {
        // 找到行结束
        line_end = line_start;
        while (ptr[line_end] != '\0' && ptr[line_end] != '\n' && ptr[line_end] != '\r') {
            line_end++;
        }

        // 复制行内容
        int line_length = line_end - line_start;
        if (line_length >= CONFIG_MAX_LINE_LENGTH) {
            line_length = CONFIG_MAX_LINE_LENGTH - 1;
        }
        strncpy(line, ptr + line_start, line_length);
        line[line_length] = '\0';

        // 解析行
        config_status_t status = parse_ini_line(line, section, key, value);
        if (status == CONFIG_OK) {
            // 处理解析结果
            if (strcmp(section, "Ratio") == 0 && strcmp(key, "Ch0") == 0) {
                params->ratio = atof(value);
            } else if (strcmp(section, "Limit") == 0 && strcmp(key, "Ch0") == 0) {
                params->limit = atof(value);
            }
        }

        // 移动到下一行
        while (ptr[line_end] == '\n' || ptr[line_end] == '\r') {
            line_end++;
        }
        line_start = line_end;
        ptr = file_content + line_start;
    }

    return CONFIG_OK;
}
```

#### 2.2.2 单行解析函数
```c
config_status_t parse_ini_line(const char *line, char *section, char *key, char *value)
{
    char temp_line[CONFIG_MAX_LINE_LENGTH];
    strncpy(temp_line, line, sizeof(temp_line) - 1);
    temp_line[sizeof(temp_line) - 1] = '\0';

    // 去除前后空格
    trim_string(temp_line);

    // 跳过空行和注释行
    if (strlen(temp_line) == 0 || temp_line[0] == ';' || temp_line[0] == '#') {
        return CONFIG_ERROR;
    }

    // 检查是否是节标题 [SectionName]
    if (temp_line[0] == '[') {
        char *end_bracket = strchr(temp_line, ']');
        if (end_bracket != NULL) {
            *end_bracket = '\0';
            strncpy(section, temp_line + 1, CONFIG_MAX_SECTION_NAME - 1);
            section[CONFIG_MAX_SECTION_NAME - 1] = '\0';
            trim_string(section);
            return CONFIG_ERROR; // 不是键值对，返回错误但section已更新
        }
    }

    // 检查是否是键值对 Key = Value
    char *equal_sign = strchr(temp_line, '=');
    if (equal_sign != NULL) {
        *equal_sign = '\0';

        // 提取键名
        strncpy(key, temp_line, CONFIG_MAX_KEY_NAME - 1);
        key[CONFIG_MAX_KEY_NAME - 1] = '\0';
        trim_string(key);

        // 提取值
        strncpy(value, equal_sign + 1, CONFIG_MAX_VALUE_NAME - 1);
        value[CONFIG_MAX_VALUE_NAME - 1] = '\0';
        trim_string(value);

        return CONFIG_OK;
    }

    return CONFIG_ERROR;
}
```

---

## 3. 参数验证机制

### 3.1 参数范围定义
```c
// 配置参数范围定义
#define RATIO_MIN 0.0f      // 变比最小值
#define RATIO_MAX 100.0f    // 变比最大值
#define LIMIT_MIN 0.0f      // 阈值最小值
#define LIMIT_MAX 200.0f    // 阈值最大值
```

### 3.2 参数验证函数

#### 3.2.1 变比验证
```c
config_status_t config_validate_ratio(float ratio)
{
    if (ratio >= RATIO_MIN && ratio <= RATIO_MAX) {
        return CONFIG_OK;
    }
    return CONFIG_INVALID_PARAM;
}

config_status_t config_set_ratio(float ratio)
{
    // 竞赛要求：ratio有效范围为0-100
    if (ratio < 0.0f || ratio > 100.0f) {
        return CONFIG_INVALID_PARAM;
    }

    g_config_params.ratio = ratio;
    return CONFIG_OK;
}
```

#### 3.2.2 阈值验证
```c
config_status_t config_validate_limit(float limit)
{
    if (limit >= LIMIT_MIN && limit <= LIMIT_MAX) {
        return CONFIG_OK;
    }
    return CONFIG_INVALID_PARAM;
}

config_status_t config_set_limit(float limit)
{
    // 竞赛要求：limit有效范围为0-200
    if (limit < 0.0f || limit > 200.0f) {
        return CONFIG_INVALID_PARAM;
    }

    g_config_params.limit = limit;
    return CONFIG_OK;
}
```

---

## 4. 系统自检流程

### 4.1 自检信息结构体
```c
typedef struct {
    uint8_t flash_ok;       // Flash检测结果
    uint8_t sd_ok;          // SD卡检测结果
    uint8_t rtc_ok;         // RTC检测结果
    uint32_t flash_id;      // Flash硬件ID
    uint32_t sd_capacity;   // SD卡容量(KB)
    char rtc_time[32];      // RTC时间字符串
} selftest_info_t;
```

### 4.2 完整自检流程
```c
selftest_result_t selftest_run_all(void)
{
    selftest_info_t info = {0};
    
    // 检测Flash
    selftest_check_flash_simple(&info);
    
    // 检测SD卡
    selftest_check_sd(&info);
    
    // 检测RTC
    selftest_check_rtc(&info);
    
    // 打印结果
    selftest_print_results(&info);
    
    // 判断整体结果
    if (!info.flash_ok) return SELFTEST_FLASH_ERROR;
    if (!info.sd_ok) return SELFTEST_SD_ERROR;
    if (!info.rtc_ok) return SELFTEST_RTC_ERROR;
    
    return SELFTEST_OK;
}
```

---

## 5. 设备ID管理

### 5.1 设备信息结构体
```c
typedef struct {
    char team_number[16];       // 队伍编号
    uint32_t power_on_count;    // 上电次数
    uint8_t initialized;        // 初始化标志
} device_info_t;

device_info_t g_device_info = {
    .team_number = DEFAULT_TEAM_NUMBER,  // "0000000000"
    .power_on_count = 0,
    .initialized = 0
};
```

### 5.2 设备ID初始化
```c
void device_id_init(void)
{
    // 设置实际的队伍ID（竞赛时的真实队伍编号）
    strcpy(g_device_info.team_number, "2025478430");
    g_device_info.power_on_count = 1;
    g_device_info.initialized = 1;
}
```

---

## 📝 本章小结

通过本章学习，您应该掌握：

✅ **INI文件解析** - 格式规范、解析算法、字符串处理
✅ **参数验证机制** - 范围检查、错误处理、配置恢复
✅ **系统自检流程** - 硬件检测、状态报告、错误诊断
✅ **设备ID管理** - Flash存储、初始化、输出格式
✅ **错误处理策略** - 配置恢复、写入验证、备份机制

**下一章预告：** 我们将学习系统集成与项目构建优化的实现。

---

## 🔗 相关文件
- `sysFunction/config_app.c` - 配置管理实现
- `sysFunction/selftest_app.c` - 系统自检实现
