# 数据存储模块程序设计

## 2.6.1 模块开发需求分析

### 数据分类和管理

#### 四类数据分离

```c
// 文件：sysFunction/sd_app.h (第36-42行)
// 数据存储类型枚举 - 四种竞赛要求的数据类型
typedef enum {
    DATA_TYPE_SAMPLE = 0,       // 采集数据：正常采样数据存储
    DATA_TYPE_OVERLIMIT,        // 超限数据：超过阈值的数据存储
    DATA_TYPE_LOG,              // 日志数据：系统操作记录存储
    DATA_TYPE_HIDEDATA          // 加密数据：HEX格式加密数据存储
} data_type_t;

// 文件：sysFunction/sd_app.h (第19-23行)
// 文件夹名称定义 - 符合竞赛要求的目录结构
#define FOLDER_SAMPLE    "sample"      // 采集数据存储目录
#define FOLDER_OVERLIMIT "overLimit"   // 超限数据存储目录
#define FOLDER_LOG       "log"         // 日志数据存储目录
#define FOLDER_HIDEDATA  "hideData"    // 加密数据存储目录

// 文件：sysFunction/sd_app.c (第16-22行)
// 四个文件夹的名字
static const char *g_folder_names[4] = {
    FOLDER_SAMPLE,      // "sample" - 存放普通测量数据
    FOLDER_OVERLIMIT,   // "overLimit" - 存放超限数据
    FOLDER_LOG,         // "log" - 存放操作日志
    FOLDER_HIDEDATA     // "hideData" - 存放加密数据
};
```

#### 文件管理结构体

```c
// 文件：sysFunction/sd_app.h (第44-49行)
// 文件管理结构体 - 管理每种数据类型的文件状态
typedef struct {
    char current_filename[MAX_FILENAME_LEN];    // 当前活动文件名
    uint8_t record_count;                       // 当前文件中的记录数量
    uint32_t file_creation_time;                // 文件创建时间戳
} file_manager_t;

// 文件：sysFunction/sd_app.c (第9行)
static file_manager_t g_file_managers[4];   // 四种文件的管理器
```

### 防止数据丢失

#### 关键日志的安全

```c
// 文件：sysFunction/sd_app.h (第157-172行)
// 标准日志内容定义 - 确保与测试流程文档完全一致
#define LOG_SYSTEM_INIT         "system init"                   // 系统初始化
#define LOG_SYSTEM_READY        "system ready"                  // 系统就绪
#define LOG_RTC_CONFIG          "RTC Config"                    // RTC配置
#define LOG_TEST_START          "system hardware test"          // 系统自检开始
#define LOG_TEST_OK             "test ok"                       // 测试通过
#define LOG_TEST_ERROR_TF       "test error: tf card not found" // TF卡错误
#define LOG_TEST_ERROR_FLASH    "test error: flash error"       // Flash错误
#define LOG_TEST_ERROR_RTC      "test error: rtc error"         // RTC错误
#define LOG_RATIO_CONFIG        "ratio config"                  // 变比配置
#define LOG_START_SAMPLING      "start sampling"                // 开始采样
#define LOG_STOP_SAMPLING       "stop sampling"                 // 停止采样
#define LOG_LIMIT_CONFIG        "limit config"                  // 阈值配置
#define LOG_CONFIG_SAVE         "config save"                   // 配置保存
#define LOG_HIDE_DATA           "hide data"                     // 数据加密
#define LOG_UNHIDE_DATA         "unhide data"                   // 取消加密
```

#### 掉电重启后的状态连续性

```c
// 文件：sysFunction/sd_app.c (第276-304行)
/**
 * @brief 恢复文件管理器状态（查找现有的未满文件）
 * @details 系统启动时调用，扫描各个目录查找未满的文件并恢复文件管理器状态
 *          确保重启后能继续在未满的文件中存储数据，而不是总是创建新文件
 * @retval None
 */
void sd_restore_file_managers(void)
{
    // 只处理非日志类型的文件（sample, overLimit, hideData）
    // 日志文件有自己的管理逻辑，不需要恢复
    for (data_type_t type = DATA_TYPE_SAMPLE; type <= DATA_TYPE_HIDEDATA; type++) {
        if (type == DATA_TYPE_LOG) continue;  // 跳过日志类型
        file_manager_t *manager = &g_file_managers[type];
        // 查找最新的未满文件
        char existing_filename[MAX_FILENAME_LEN];
        uint8_t existing_records = find_latest_incomplete_file(type, existing_filename, sizeof(existing_filename));
        if (existing_records > 0) {
            // 找到未满的文件，恢复文件管理器状态
            strncpy(manager->current_filename, existing_filename, MAX_FILENAME_LEN - 1);
            manager->current_filename[MAX_FILENAME_LEN - 1] = '\0';
            manager->record_count = existing_records;
            manager->file_creation_time = HAL_GetTick();
        }
        // 如果没有找到未满的文件，保持文件名为空，下次写入时会创建新文件
    }
}
```

### 存储服务接口

```c
// 文件：sysFunction/sd_app.h (第102-108行)
// 数据写入函数 - 竞赛要求的四种数据类型
FRESULT sd_write_data(data_type_t type, const char *data);                  // 通用数据写入接口
FRESULT sd_write_sample_data(const char *time_str, float voltage);          // 写入采样数据到sample文件夹
FRESULT sd_write_overlimit_data(const char *time_str, float voltage, float limit); // 写入超限数据到overLimit文件夹
FRESULT sd_write_log_data(const char *log_content);                         // 写入日志数据到log文件夹
FRESULT sd_write_hidedata(const char *hex_data);                            // 写入HEX数据到hideData文件夹（兼容接口）
FRESULT sd_write_hidedata_with_voltage(float voltage, uint8_t is_overlimit); // 写入加密数据（推荐接口）
```

## 2.6.2 模块设计理念与实现方案

### 四轨并行存储架构

#### 统一入口分发机制

```c
// 文件：sysFunction/sd_app.c (第476-520行)
/**
 * @brief 通用文件写入函数
 * @param type 数据类型
 * @param data 要写入的数据
 * @retval FRESULT
 */
FRESULT sd_write_data(data_type_t type, const char *data)
{
    if (data == NULL || type >= 4) return FR_INVALID_PARAMETER;
    
    FRESULT res;
    FIL file;
    UINT bytes_written;
    file_manager_t *manager = &g_file_managers[type];
    
    // 检查并创建新文件
    res = sd_check_and_create_new_file(type);
    if (res != FR_OK) return res;
    
    // 构建完整路径：目录/文件名
    char full_path[MAX_FILEPATH_LEN];
    snprintf(full_path, sizeof(full_path), "%s/%s", g_folder_names[type], manager->current_filename);
    
    // 打开文件进行追加写入
    res = f_open(&file, full_path, FA_WRITE | FA_OPEN_APPEND);
    if (res != FR_OK) {
        // 如果文件不存在，创建新文件
        res = f_open(&file, full_path, FA_WRITE | FA_CREATE_NEW);
        if (res != FR_OK) return res;
    }
    
    // 写入数据
    res = f_write(&file, data, strlen(data), &bytes_written);
    f_close(&file);
    
    if (res == FR_OK) {
        manager->record_count++;
    }
    
    return res;
}
```

### SD卡+Flash双重冗余存储机制

#### Flash日志缓存结构体

```c
// 文件：sysFunction/sd_app.h (第74-81行)
// Flash日志缓存结构体 - 第一次上电无SD卡时的日志临时存储
typedef struct {
    uint32_t magic;                             // 魔数标识：0x4C4F4731 ("LOG1")
    uint32_t count;                             // 已缓存的日志条目数量
    uint32_t next_index;                        // 下一个写入位置索引
    char logs[MAX_CACHED_LOGS][LOG_ENTRY_SIZE]; // 日志条目数组
    uint32_t checksum;                          // 数据校验和
} flash_log_cache_t;

// 文件：sysFunction/sd_app.h (第30-34行)
// Flash日志缓存配置 - 解决第一次上电无SD卡时的日志存储问题
#define MAX_CACHED_LOGS 30             // 最大缓存30条日志（足够第一次上电的操作）
#define LOG_ENTRY_SIZE 128             // 每条日志最大128字节
#define FLASH_LOG_CACHE_MAGIC 0x4C4F4731  // 魔数标识："LOG1"
#define FLASH_LOG_CACHE_ADDR 0x6000    // Flash中日志缓存存储地址
```

#### Flash缓存写入机制

```c
// 文件：sysFunction/sd_app.c (第1285-1330行)
/**
 * @brief 缓存日志到Flash
 * @param log_data 格式化后的日志数据
 * @retval FRESULT
 */
FRESULT sd_cache_log_to_flash(const char *log_data)
{
    if (log_data == NULL) return FR_INVALID_PARAMETER;
    
    flash_log_cache_t cache;
    
    // 尝试从Flash读取现有缓存
    if (flash_direct_read(FLASH_LOG_CACHE_ADDR, &cache, sizeof(cache)) != FLASH_OK) {
        // Flash读取失败或首次使用，初始化缓存结构
        cache.magic = FLASH_LOG_CACHE_MAGIC;
        cache.count = 0;
        cache.next_index = 0;
        memset(cache.logs, 0, sizeof(cache.logs));
    }
    
    // 验证魔数
    if (cache.magic != FLASH_LOG_CACHE_MAGIC) {
        // 魔数不匹配，重新初始化
        cache.magic = FLASH_LOG_CACHE_MAGIC;
        cache.count = 0;
        cache.next_index = 0;
        memset(cache.logs, 0, sizeof(cache.logs));
    }
    
    // 检查缓存是否已满
    if (cache.count >= MAX_CACHED_LOGS) {
        return FR_DENIED;  // 缓存已满
    }
    
    // 添加新日志条目
    strncpy(cache.logs[cache.next_index], log_data, LOG_ENTRY_SIZE - 1);
    cache.logs[cache.next_index][LOG_ENTRY_SIZE - 1] = '\0';
    
    cache.next_index = (cache.next_index + 1) % MAX_CACHED_LOGS;
    cache.count++;
    
    // 计算校验和（不包括校验和字段本身）
    cache.checksum = calculate_checksum(&cache, sizeof(cache) - sizeof(cache.checksum));
    
    // 写入Flash
    if (flash_direct_write(FLASH_LOG_CACHE_ADDR, &cache, sizeof(cache)) == FLASH_OK) {
        return FR_OK;
    } else {
        return FR_DISK_ERR;
    }
}
```

#### Flash缓存恢复机制

```c
// 文件：sysFunction/sd_app.c (第1338-1395行)
/**
 * @brief 从Flash恢复日志到SD卡
 * @retval FRESULT
 * @note 在SD卡插入并挂载成功后调用，将缓存的日志写入log0.txt
 */
FRESULT sd_restore_logs_from_flash(void)
{
    extern UART_HandleTypeDef huart1;
    if (!g_filesystem_mounted) {
        return FR_NOT_READY;  // SD卡未挂载
    }
    // 检查是否已经同步过Flash缓存
    if (g_flash_cache_synced) {
        my_printf(&huart1, "[FLASH_RESTORE] Flash cache already synced, skipping\r\n");
        return FR_OK;  // 已经同步过，直接返回成功
    }
    my_printf(&huart1, "[FLASH_RESTORE] Proceeding with Flash cache restore to log%lu.txt\r\n", g_log_id_manager.log_id);
    flash_log_cache_t cache;
    // 从Flash读取缓存
    if (flash_direct_read(FLASH_LOG_CACHE_ADDR, &cache, sizeof(cache)) != FLASH_OK) {
        return FR_DISK_ERR;  // Flash读取失败
    }
    // 验证魔数和校验和
    if (cache.magic != FLASH_LOG_CACHE_MAGIC) {
        return FR_OK;  // 没有有效的缓存数据，直接返回成功
    }
    uint32_t expected_checksum = calculate_checksum(&cache, sizeof(cache) - sizeof(cache.checksum));
    if (cache.checksum != expected_checksum) {
        my_printf(&huart1, "[FLASH_RESTORE] Checksum mismatch, cache corrupted\r\n");
        return FR_OK;  // 校验和不匹配，缓存损坏，直接返回成功
    }
    // 恢复缓存的日志到SD卡
    for (uint32_t i = 0; i < cache.count; i++) {
        uint32_t log_index = (cache.next_index - cache.count + i) % MAX_CACHED_LOGS;
        if (strlen(cache.logs[log_index]) > 0) {
            // 直接写入到当前log文件
            sd_write_data(DATA_TYPE_LOG, cache.logs[log_index]);
        }
    }
    // 标记Flash缓存已同步
    g_flash_cache_synced = 1;
    my_printf(&huart1, "[FLASH_RESTORE] %lu cached logs restored successfully\r\n", cache.count);
    return FR_OK;
}
```

### 智能文件周期管理

#### 自动切换机制

```c
// 文件：sysFunction/sd_app.c (第413-468行)
/**
 * @brief 检查并创建新文件（如果当前文件数据已满）
 * @param type 数据类型
 * @retval FRESULT
 */
FRESULT sd_check_and_create_new_file(data_type_t type)
{
    if (type >= 4) return FR_INVALID_PARAMETER;
    file_manager_t *manager = &g_file_managers[type];
    // 如果文件名为空，先尝试查找现有的未满文件
    if (strlen(manager->current_filename) == 0) {
        char existing_filename[MAX_FILENAME_LEN];
        uint8_t existing_records = find_latest_incomplete_file(type, existing_filename, sizeof(existing_filename));
        if (existing_records > 0) {
            // 找到未满的文件，恢复使用
            strncpy(manager->current_filename, existing_filename, MAX_FILENAME_LEN - 1);
            manager->current_filename[MAX_FILENAME_LEN - 1] = '\0';
            manager->record_count = existing_records;
            manager->file_creation_time = HAL_GetTick();
            return FR_OK;
        }
    }
    // 检查是否需要创建新文件
    uint8_t need_new_file = 0;
    if (type == DATA_TYPE_LOG) {
        // log文件：检查文件名为空或当前文件名与log_id不匹配
        if (strlen(manager->current_filename) == 0) {
            need_new_file = 1;  // 文件名为空，需要创建新文件
        } else {
            // 检查当前文件名是否与log_id匹配
            char expected_filename[MAX_FILENAME_LEN];
            snprintf(expected_filename, sizeof(expected_filename), "log%lu.txt", g_log_id_manager.log_id);
            if (strcmp(manager->current_filename, expected_filename) != 0) {
                need_new_file = 1;  // 文件名不匹配，需要切换到新文件
            }
        }
    } else {
        // 其他文件：达到10条数据或文件名为空时创建新文件
        need_new_file = (manager->record_count >= MAX_RECORDS_PER_FILE || strlen(manager->current_filename) == 0);
    }
    if (need_new_file) {
        // 生成新文件名
        char filename[MAX_FILENAME_LEN];
        sd_generate_filename(type, filename, sizeof(filename));
        // 更新文件管理器
        strncpy(manager->current_filename, filename, MAX_FILENAME_LEN - 1);
        manager->current_filename[MAX_FILENAME_LEN - 1] = '\0';
        manager->record_count = 0;
        manager->file_creation_time = HAL_GetTick();
    }
    return FR_OK;
}
```

#### 断电恢复机制

```c
// 文件：sysFunction/sd_app.c (第350-406行)
/**
 * @brief 查找最新的未满文件
 * @param type 数据类型
 * @param latest_filename 输出最新文件名
 * @param filename_size 文件名缓冲区大小
 * @retval 找到的记录数，0表示未找到未满文件
 */
static uint8_t find_latest_incomplete_file(data_type_t type, char *latest_filename, size_t filename_size)
{
    if (latest_filename == NULL || type >= 4) return 0;
    DIR dir;
    FILINFO fno;
    FRESULT res;
    
    char latest_file[MAX_FILENAME_LEN] = {0};
    uint8_t latest_records = 0;
    uint32_t latest_time = 0;
    
    // 打开对应的目录
    res = f_opendir(&dir, g_folder_names[type]);
    if (res != FR_OK) return 0;
    
    // 遍历目录中的所有文件
    while (1) {
        res = f_readdir(&dir, &fno);
        if (res != FR_OK || fno.fname[0] == 0) break;
        
        // 跳过目录
        if (fno.fattrib & AM_DIR) continue;
        
        // 检查文件扩展名是否为.txt
        char *ext = strrchr(fno.fname, '.');
        if (ext == NULL || strcmp(ext, ".txt") != 0) continue;
        
        // 构建完整文件路径
        char full_path[MAX_FILEPATH_LEN];
        snprintf(full_path, sizeof(full_path), "%s/%s", g_folder_names[type], fno.fname);
        
        // 计算文件中的记录数
        uint8_t record_count = count_records_in_file(full_path, type);
        
        // 如果文件未满（小于10条记录）
        if (record_count < MAX_RECORDS_PER_FILE && record_count > 0) {
            // 比较文件修改时间，找到最新的未满文件
            if (fno.fdate > latest_time || (fno.fdate == latest_time && fno.ftime > latest_time)) {
                latest_time = fno.fdate;
                strncpy(latest_file, fno.fname, sizeof(latest_file) - 1);
                latest_file[sizeof(latest_file) - 1] = '\0';
                latest_records = record_count;
            }
        }
    }
    
    f_closedir(&dir);
    
    if (strlen(latest_file) > 0) {
        strncpy(latest_filename, latest_file, filename_size - 1);
        latest_filename[filename_size - 1] = '\0';
        return latest_records;
    }
    
    return 0;
}
```

## 2.6.3 程序设计特点

### 数据完整性保障
- **魔数验证**：使用0x4C4F4731魔数标识有效的Flash缓存
- **校验和验证**：确保Flash缓存数据的完整性
- **多重重试**：SD卡初始化失败时自动重试机制

### 存储效率优化
- **智能文件管理**：自动查找未满文件继续写入
- **分类存储**：四种数据类型独立管理，避免混乱
- **缓存机制**：Flash缓存解决SD卡不可用时的数据丢失问题

### 系统健壮性
- **错误恢复**：SD卡故障时自动切换到Flash缓存模式
- **状态恢复**：系统重启后自动恢复文件管理器状态
- **数据同步**：Flash缓存与SD卡之间的智能同步机制
