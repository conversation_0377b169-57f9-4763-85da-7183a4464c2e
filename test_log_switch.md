# 竞赛日志文件切换测试方案

## 问题描述
按照赛题要求，系统需要实现以下流程：
1. 系统上电烧录程序后，按下复位键开始赛事测评流程
2. 在未插入SD卡状态下，生成log0.txt文件，记录RTC Config和RTC now命令
3. 系统断电，插入SD卡，系统上电
4. SD卡中生成新的log1.txt文件，test指令的日志应当被保存在log1.txt中

## 解决方案

### 核心修改
1. **修改了`sd_check_and_increment_boot_count`函数**
   - 重命名为更准确的功能描述：检查并切换到下一个日志文件
   - 检测Flash缓存状态来判断是否需要从log0切换到log1
   - 有Flash缓存说明之前是无SD卡状态，现在需要切换到log1

2. **修改了`sd_write_log_data`函数**
   - 在SD卡可用时检查是否需要执行日志文件切换
   - 基于Flash缓存状态触发日志文件切换

3. **修改了RTC now命令处理**
   - 添加了日志记录功能，确保RTC now命令也记录到log0.txt中

4. **优化了Flash缓存恢复逻辑**
   - 确保Flash缓存恢复到正确的log0.txt文件
   - 恢复后切换到log1用于新命令

### 测试流程

#### 阶段1：无SD卡启动（log0阶段）
1. 确保SD卡未插入
2. 烧录程序并按下复位键
3. 执行以下命令：
   ```
   RTC Config
   2025-01-01 12:00:30
   RTC now
   ```
4. 预期结果：
   - 所有日志缓存到Flash中
   - 串口显示正确的RTC配置和时间信息

#### 阶段2：插入SD卡（log1阶段）
1. 系统断电
2. 插入SD卡
3. 系统上电
4. 执行test命令
5. 预期结果：
   - Flash缓存恢复到log0.txt文件
   - test命令记录到log1.txt文件
   - 日志文件正确分离

### 验证要点
1. **log0.txt内容**应包含：
   - system init
   - rtc config相关日志
   - rtc now相关日志

2. **log1.txt内容**应包含：
   - system hardware test
   - test ok (或相应的测试结果)

3. **文件切换逻辑**：
   - 检查Flash缓存状态
   - 正确的log_id管理
   - boot_count的正确递增

### 关键代码变更

#### sd_check_and_increment_boot_count函数
- 检测Flash缓存来判断是否需要切换日志文件
- 从log0切换到log1的逻辑
- 保存下次启动的boot_count

#### sd_write_log_data函数
- 延迟执行日志文件切换检查
- 基于Flash缓存状态触发切换

#### RTC命令处理
- RTC now命令添加日志记录
- 确保RTC相关日志记录到log0阶段

## 预期效果
通过这些修改，系统能够正确实现：
1. 无SD卡时所有日志缓存到Flash（log0阶段）
2. SD卡插入后Flash缓存恢复到log0.txt
3. 新命令（如test）记录到log1.txt
4. 符合竞赛要求的日志文件分离逻辑
