/**
 * @file    第3章_实践练习与代码示例.c
 * @brief   GPIO控制与LED按键模块实现 - 实践练习代码
 * @details 通过实际代码示例帮助理解GPIO配置、LED控制和按键防抖的实现原理
 * <AUTHOR>
 * @date    2025-01-07
 */

#include "main.h"
#include "stdio.h"
#include "gpio.h"

// ============================================================================
// 练习1：GPIO配置详解
// ============================================================================

/**
 * @brief 演示GPIO_InitTypeDef结构体的配置
 */
void practice_gpio_config_demo(void)
{
    printf("=== GPIO配置演示 ===\r\n");
    
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    // 使能GPIOD时钟
    __HAL_RCC_GPIOD_CLK_ENABLE();
    
    printf("GPIO_InitTypeDef结构体大小: %d 字节\r\n", sizeof(GPIO_InitTypeDef));
    
    // 配置LED引脚（推挽输出）
    GPIO_InitStruct.Pin = GPIO_PIN_8;                    // 选择PD8引脚
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;          // 推挽输出模式
    GPIO_InitStruct.Pull = GPIO_NOPULL;                  // 无上拉下拉
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;         // 低速输出
    HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);
    
    printf("LED引脚配置完成:\r\n");
    printf("  引脚: PD8 (0x%04X)\r\n", GPIO_InitStruct.Pin);
    printf("  模式: 推挽输出 (0x%08X)\r\n", GPIO_InitStruct.Mode);
    printf("  上拉: 无 (0x%08X)\r\n", GPIO_InitStruct.Pull);
    printf("  速度: 低速 (0x%08X)\r\n", GPIO_InitStruct.Speed);
    
    // 使能GPIOE时钟
    __HAL_RCC_GPIOE_CLK_ENABLE();
    
    // 配置按键引脚（输入模式）
    GPIO_InitStruct.Pin = GPIO_PIN_15;                   // 选择PE15引脚
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;              // 输入模式
    GPIO_InitStruct.Pull = GPIO_PULLUP;                  // 内部上拉
    HAL_GPIO_Init(GPIOE, &GPIO_InitStruct);
    
    printf("按键引脚配置完成:\r\n");
    printf("  引脚: PE15 (0x%04X)\r\n", GPIO_InitStruct.Pin);
    printf("  模式: 输入 (0x%08X)\r\n", GPIO_InitStruct.Mode);
    printf("  上拉: 上拉 (0x%08X)\r\n", GPIO_InitStruct.Pull);
}

/**
 * @brief 演示不同GPIO模式的配置
 */
void practice_gpio_modes_demo(void)
{
    printf("=== GPIO模式配置演示 ===\r\n");
    
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    // 模式1：推挽输出（LED控制）
    printf("1. 推挽输出模式（LED控制）:\r\n");
    GPIO_InitStruct.Pin = GPIO_PIN_8;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    printf("   特点: 可输出强高电平和强低电平\r\n");
    printf("   应用: LED、继电器控制\r\n");
    
    // 模式2：开漏输出（I2C通信）
    printf("2. 开漏输出模式（I2C通信）:\r\n");
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_OD;
    GPIO_InitStruct.Pull = GPIO_PULLUP;  // 需要上拉电阻
    printf("   特点: 只能输出低电平，高电平需要外部上拉\r\n");
    printf("   应用: I2C、1-Wire通信\r\n");
    
    // 模式3：输入上拉（按键检测）
    printf("3. 输入上拉模式（按键检测）:\r\n");
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    printf("   特点: 默认高电平，按键按下时为低电平\r\n");
    printf("   应用: 按键、开关检测\r\n");
    
    // 模式4：复用推挽（UART、SPI）
    printf("4. 复用推挽模式（UART、SPI）:\r\n");
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF7_USART1;  // 复用为USART1
    printf("   特点: 由外设控制的推挽输出\r\n");
    printf("   应用: UART、SPI、PWM输出\r\n");
}

// ============================================================================
// 练习2：LED控制实现
// ============================================================================

/**
 * @brief 简单LED控制演示
 */
void practice_simple_led_demo(void)
{
    printf("=== 简单LED控制演示 ===\r\n");
    
    // 确保GPIO已配置
    __HAL_RCC_GPIOD_CLK_ENABLE();
    
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = GPIO_PIN_8 | GPIO_PIN_9 | GPIO_PIN_10;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);
    
    printf("LED控制演示开始...\r\n");
    
    // 逐个点亮LED
    for (uint8_t i = 0; i < 3; i++) {
        uint16_t led_pin = GPIO_PIN_8 << i;  // 计算对应的引脚
        
        printf("点亮LED%d (PD%d)\r\n", i, 8 + i);
        HAL_GPIO_WritePin(GPIOD, led_pin, GPIO_PIN_SET);
        HAL_Delay(500);
        
        printf("熄灭LED%d\r\n", i);
        HAL_GPIO_WritePin(GPIOD, led_pin, GPIO_PIN_RESET);
        HAL_Delay(200);
    }
    
    // 演示翻转功能
    printf("LED翻转演示:\r\n");
    for (uint8_t i = 0; i < 6; i++) {
        printf("翻转LED0状态\r\n");
        HAL_GPIO_TogglePin(GPIOD, GPIO_PIN_8);
        HAL_Delay(300);
    }
}

/**
 * @brief 高级LED控制演示（仿照项目实现）
 */
typedef struct {
    uint8_t led_state[6];     // LED状态数组
    uint8_t last_state;       // 上次状态缓存
} led_controller_t;

static led_controller_t led_ctrl = {0};

void practice_advanced_led_demo(void)
{
    printf("=== 高级LED控制演示 ===\r\n");
    
    // 初始化LED控制器
    memset(&led_ctrl, 0, sizeof(led_controller_t));
    led_ctrl.last_state = 0xFF;  // 强制第一次更新
    
    printf("LED控制器初始化完成\r\n");
    printf("LED状态数组大小: %d 字节\r\n", sizeof(led_ctrl.led_state));
    
    // 演示不同的LED模式
    printf("\n1. 流水灯模式:\r\n");
    for (uint8_t cycle = 0; cycle < 2; cycle++) {
        for (uint8_t i = 0; i < 6; i++) {
            // 清除所有LED
            memset(led_ctrl.led_state, 0, sizeof(led_ctrl.led_state));
            // 点亮当前LED
            led_ctrl.led_state[i] = 1;
            
            // 更新LED显示
            update_led_display(&led_ctrl);
            printf("  LED%d点亮\r\n", i);
            HAL_Delay(200);
        }
    }
    
    printf("\n2. 二进制计数模式:\r\n");
    for (uint8_t count = 0; count < 16; count++) {
        // 将计数值转换为LED状态
        for (uint8_t i = 0; i < 6; i++) {
            led_ctrl.led_state[i] = (count >> i) & 0x01;
        }
        
        update_led_display(&led_ctrl);
        printf("  计数: %d, LED状态: ", count);
        for (uint8_t i = 0; i < 6; i++) {
            printf("%d", led_ctrl.led_state[i]);
        }
        printf("\r\n");
        HAL_Delay(300);
    }
}

/**
 * @brief 更新LED显示（仿照项目实现）
 */
void update_led_display(led_controller_t *ctrl)
{
    uint8_t current_state = 0;
    
    // 将LED数组转换为位图
    for (uint8_t i = 0; i < 6; i++) {
        if (ctrl->led_state[i]) {
            current_state |= (1 << i);
        }
    }
    
    // 只有状态改变时才更新GPIO（性能优化）
    if (ctrl->last_state != current_state) {
        // 更新所有LED状态
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_8, 
                         (current_state & 0x01) ? GPIO_PIN_SET : GPIO_PIN_RESET);
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_9, 
                         (current_state & 0x02) ? GPIO_PIN_SET : GPIO_PIN_RESET);
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_10, 
                         (current_state & 0x04) ? GPIO_PIN_SET : GPIO_PIN_RESET);
        
        ctrl->last_state = current_state;
        
        printf("GPIO更新: 0x%02X -> 0x%02X\r\n", ctrl->last_state, current_state);
    } else {
        printf("状态未变化，跳过GPIO更新\r\n");
    }
}

// ============================================================================
// 练习3：按键检测与防抖
// ============================================================================

/**
 * @brief 简单按键检测演示
 */
void practice_simple_button_demo(void)
{
    printf("=== 简单按键检测演示 ===\r\n");
    
    // 配置按键GPIO
    __HAL_RCC_GPIOE_CLK_ENABLE();
    
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = GPIO_PIN_15;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;  // 内部上拉
    HAL_GPIO_Init(GPIOE, &GPIO_InitStruct);
    
    printf("按键配置完成，开始检测...\r\n");
    printf("按下按键(PE15)查看效果，10秒后自动结束\r\n");
    
    uint32_t start_time = HAL_GetTick();
    GPIO_PinState last_state = GPIO_PIN_SET;
    uint32_t press_count = 0;
    
    while ((HAL_GetTick() - start_time) < 10000) {  // 运行10秒
        GPIO_PinState current_state = HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_15);
        
        // 检测按键按下（下降沿）
        if (last_state == GPIO_PIN_SET && current_state == GPIO_PIN_RESET) {
            press_count++;
            printf("按键按下！第%d次，时间: %d ms\r\n", press_count, HAL_GetTick());
        }
        
        // 检测按键释放（上升沿）
        if (last_state == GPIO_PIN_RESET && current_state == GPIO_PIN_SET) {
            printf("按键释放！时间: %d ms\r\n", HAL_GetTick());
        }
        
        last_state = current_state;
        HAL_Delay(1);  // 1ms检测间隔
    }
    
    printf("按键检测结束，总共检测到 %d 次按键\r\n", press_count);
}

/**
 * @brief 软件防抖算法演示
 */
typedef struct {
    GPIO_PinState current_state;    // 当前状态
    GPIO_PinState stable_state;     // 稳定状态
    uint32_t last_change_time;      // 上次状态变化时间
    uint32_t debounce_time;         // 防抖时间(ms)
    uint8_t press_count;            // 按键计数
} button_debounce_t;

void practice_debounce_algorithm_demo(void)
{
    printf("=== 软件防抖算法演示 ===\r\n");
    
    button_debounce_t btn = {
        .current_state = GPIO_PIN_SET,
        .stable_state = GPIO_PIN_SET,
        .last_change_time = 0,
        .debounce_time = 50,  // 50ms防抖时间
        .press_count = 0
    };
    
    printf("防抖参数:\r\n");
    printf("  防抖时间: %d ms\r\n", btn.debounce_time);
    printf("  检测间隔: 5 ms\r\n");
    printf("开始防抖检测，10秒后自动结束...\r\n");
    
    uint32_t start_time = HAL_GetTick();
    
    while ((HAL_GetTick() - start_time) < 10000) {  // 运行10秒
        GPIO_PinState raw_state = HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_15);
        uint32_t current_time = HAL_GetTick();
        
        // 检测状态变化
        if (raw_state != btn.current_state) {
            btn.current_state = raw_state;
            btn.last_change_time = current_time;
            printf("检测到状态变化: %s，开始防抖计时\r\n", 
                   raw_state == GPIO_PIN_RESET ? "按下" : "释放");
        }
        
        // 防抖时间到，确认状态
        if ((current_time - btn.last_change_time) >= btn.debounce_time) {
            if (btn.current_state != btn.stable_state) {
                btn.stable_state = btn.current_state;
                
                if (btn.stable_state == GPIO_PIN_RESET) {
                    btn.press_count++;
                    printf("✓ 确认按键按下！第%d次，时间: %d ms\r\n", 
                           btn.press_count, current_time);
                } else {
                    printf("✓ 确认按键释放！时间: %d ms\r\n", current_time);
                }
            }
        }
        
        HAL_Delay(5);  // 5ms检测间隔
    }
    
    printf("防抖检测结束，有效按键次数: %d\r\n", btn.press_count);
}

// ============================================================================
// 练习4：上拉下拉电阻效果演示
// ============================================================================

/**
 * @brief 演示上拉下拉电阻的效果
 */
void practice_pull_resistor_demo(void)
{
    printf("=== 上拉下拉电阻效果演示 ===\r\n");
    
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = GPIO_PIN_15;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    
    // 测试1：无上拉下拉（浮空）
    printf("1. 无上拉下拉（浮空状态）:\r\n");
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(GPIOE, &GPIO_InitStruct);
    HAL_Delay(10);  // 等待稳定
    
    for (uint8_t i = 0; i < 5; i++) {
        GPIO_PinState state = HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_15);
        printf("  读取%d: %s\r\n", i+1, state == GPIO_PIN_SET ? "高电平" : "低电平");
        HAL_Delay(100);
    }
    printf("  结果: 浮空状态，读取值不确定\r\n");
    
    // 测试2：内部上拉
    printf("\n2. 内部上拉电阻:\r\n");
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(GPIOE, &GPIO_InitStruct);
    HAL_Delay(10);  // 等待稳定
    
    for (uint8_t i = 0; i < 5; i++) {
        GPIO_PinState state = HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_15);
        printf("  读取%d: %s\r\n", i+1, state == GPIO_PIN_SET ? "高电平" : "低电平");
        HAL_Delay(100);
    }
    printf("  结果: 默认高电平，按键按下时为低电平\r\n");
    
    // 测试3：内部下拉
    printf("\n3. 内部下拉电阻:\r\n");
    GPIO_InitStruct.Pull = GPIO_PULLDOWN;
    HAL_GPIO_Init(GPIOE, &GPIO_InitStruct);
    HAL_Delay(10);  // 等待稳定
    
    for (uint8_t i = 0; i < 5; i++) {
        GPIO_PinState state = HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_15);
        printf("  读取%d: %s\r\n", i+1, state == GPIO_PIN_SET ? "高电平" : "低电平");
        HAL_Delay(100);
    }
    printf("  结果: 默认低电平，适用于特殊传感器\r\n");
    
    // 恢复原始配置
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(GPIOE, &GPIO_InitStruct);
}

// ============================================================================
// 主练习函数
// ============================================================================

/**
 * @brief 第3章所有练习的入口函数
 */
void chapter3_practice_all(void)
{
    printf("\r\n");
    printf("========================================\r\n");
    printf("    第3章：GPIO控制与LED按键模块实现\r\n");
    printf("           实践练习开始\r\n");
    printf("========================================\r\n");
    
    practice_gpio_config_demo();
    printf("\r\n");
    
    practice_gpio_modes_demo();
    printf("\r\n");
    
    practice_simple_led_demo();
    printf("\r\n");
    
    practice_advanced_led_demo();
    printf("\r\n");
    
    practice_simple_button_demo();
    printf("\r\n");
    
    practice_debounce_algorithm_demo();
    printf("\r\n");
    
    practice_pull_resistor_demo();
    printf("\r\n");
    
    printf("========================================\r\n");
    printf("           实践练习结束\r\n");
    printf("========================================\r\n");
    printf("\r\n");
}
