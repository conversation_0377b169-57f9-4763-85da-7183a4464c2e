/**
 * @file    flash_app.h
 * @brief   Flash存储应用层头文件
 * @details 为2025 CIMC西门子杯竞赛项目提供Flash存储管理功能
 *          包含LittleFS文件系统、设备信息存储、配置参数管理等
 * <AUTHOR> CIMC西门子杯竞赛项目组
 * @date    2025-01-01
 * @version 1.0
 */

#ifndef __FLASH_APP_H__
#define __FLASH_APP_H__

#include "stdint.h"
#include "stddef.h"         // 包含size_t定义

// Flash存储区域定义
#define FLASH_SECTOR_SIZE       4096        // Flash扇区大小（4KB）

// Flash应用层错误码
typedef enum {
    FLASH_OK = 0,                   // 操作成功
    FLASH_ERROR_INIT,               // 初始化失败
    FLASH_ERROR_MOUNT,              // 挂载失败
    FLASH_ERROR_FORMAT,             // 格式化失败
    FLASH_ERROR_READ,               // 读取失败
    FLASH_ERROR_WRITE,              // 写入失败
    FLASH_ERROR_DELETE,             // 删除失败
    FLASH_ERROR_NOT_FOUND,          // 文件未找到
    FLASH_ERROR_NO_SPACE            // 空间不足
} flash_result_t;

// Flash存储信息结构体
typedef struct {
    uint32_t total_size;            // 总容量（字节）
    uint32_t used_size;             // 已使用容量（字节）
    uint32_t free_size;             // 剩余容量（字节）
    uint32_t block_count;           // 总块数
    uint32_t block_size;            // 块大小
    uint8_t  mounted;               // 挂载状态
} flash_info_t;

// 全局变量声明
extern flash_info_t g_flash_info;  // Flash信息

// Flash应用层函数声明
flash_result_t flash_app_init(void);                    // Flash应用层初始化
flash_result_t flash_mount(void);                       // 挂载文件系统
flash_result_t flash_format(void);                      // 格式化Flash
flash_result_t flash_get_info(flash_info_t *info);      // 获取Flash信息
flash_result_t flash_check_health(void);                // 检查Flash健康状态

// 注意：文件操作函数已移除，现在使用直接Flash操作

// 兼容性函数（保持原有接口）
void initialize_filesystem(void);                                                      // 原有初始化函数

// Flash存储地址布局定义
#define DEVICE_ID_FLASH_ADDR    0x0000      // 设备ID存储地址
#define CONFIG_FLASH_ADDR       0x1000      // 配置参数存储地址
#define BOOT_COUNT_FLASH_ADDR   0x2000      // 上电次数存储地址
#define PROGRAM_VERSION_FLASH_ADDR 0x3000   // 程序版本标志存储地址
#define TEST_STAGE_FLASH_ADDR   0x4000      // 测试阶段状态存储地址
#define SYSTEM_INIT_FLAG_ADDR   0x5000      // 系统初始化标志存储地址（防止重复记录system init）

// 程序版本标志（每次烧录程序时应该更新这个值）
#define CURRENT_PROGRAM_VERSION 0x20250604	  // 格式：YYYYMMDD，每次烧录时更新

// 直接Flash操作函数声明
flash_result_t flash_direct_write(uint32_t addr, const void* data, size_t size);       // 直接写入Flash
flash_result_t flash_direct_read(uint32_t addr, void* data, size_t size);              // 直接读取Flash
flash_result_t flash_direct_erase_sector(uint32_t addr);                               // 直接擦除Flash扇区

#endif /* __FLASH_APP_H__ */

