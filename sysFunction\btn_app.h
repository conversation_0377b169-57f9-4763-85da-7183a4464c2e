#ifndef __BTN_APP_H
#define __BTN_APP_H

#ifdef __cplusplus
extern "C" {
#endif

#include "mydefine.h"

// 显示模式枚举 - 支持测评要求18项
typedef enum {
    DISPLAY_CH0_RAW = 0,        // 按键1：显示Ch0原始数据
    DISPLAY_CH1_RAW,            // 按键2：显示Ch1原始数据
    DISPLAY_CH2_RAW,            // 按键3：显示Ch2原始数据
    DISPLAY_CH0_RATIO,          // 按键4：显示Ch0变比后数据
    DISPLAY_CH1_RATIO,          // 按键5：显示Ch1变比后数据
    DISPLAY_CH2_RATIO,          // 按键6：显示Ch2变比后数据
    DISPLAY_MODE_MAX
} display_mode_t;

// 全局显示模式变量
extern display_mode_t g_display_mode;

void app_btn_init(void);
void btn_task(void);
void display_mode_set(display_mode_t mode);     // 设置显示模式
display_mode_t display_mode_get(void);          // 获取显示模式
const char* display_mode_get_name(display_mode_t mode); // 获取显示模式名称

#ifdef __cplusplus
}
#endif

#endif /* __BTN_APP_H */
