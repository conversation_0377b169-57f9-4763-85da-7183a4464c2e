#include <stdint.h>
#include <math.h>
#include "adc_app.h"
#include "tim.h"
#include "config_app.h"
#include "rtc_app.h"
#include "led_app.h"
#include "sd_app.h"
#include "sampling_board_app.h"  // 添加您的采样板头文件

uint32_t adc_dma_buffer[ADC_DMA_BUFFER_SIZE]; //原始测量数据存这里
const uint32_t ADC_DMA_BUFFER_SIZE_CONST = ADC_DMA_BUFFER_SIZE; //常量定义供其他文件使用
__IO uint32_t adc_val; //平均后的数字
__IO float voltage; //最终电压值

#define VOLTAGE_FILTER_SIZE 16 //保存16次历史数据让显示稳定
static float voltage_filter_buffer[VOLTAGE_FILTER_SIZE] = {0};
static uint8_t voltage_filter_index = 0;
static uint8_t voltage_filter_filled = 0;

#define MEDIAN_FILTER_SIZE 5 //去掉奇怪的测量值
static float median_filter_buffer[MEDIAN_FILTER_SIZE] = {0};
static uint8_t median_filter_index = 0;

adc_control_t g_adc_control = { //电压测量总控制器 - 保持向后兼容
    .state = SAMPLING_IDLE, //开始时待机
    .cycle = CYCLE_5S, //默认5秒测一次
    .last_sample_time = 0, //上次测量时间
    .cycle_ms = 5000, //5秒=5000毫秒
    .processed_voltage = 0.0f, //测到的电压
    .over_limit = 0, //是否超限
    .led1_blink_state = 0 //LED闪烁状态
};

// 现在直接使用您的sampling_board_app.c中的函数：
// - current_calibrate_linear() 电流校准
// - current_filter_update() 电流滤波
// - sampling_board_read_channel() 硬件读取

// 多通道数据全局变量 - 新增
multi_channel_data_t g_multi_channel_data = {
    // 原始数据初始化
    .ch0_raw = 0.0f,
    .ch1_raw = 0.0f,
    .ch2_raw = 0.0f,

    // 变比后数据初始化
    .ch0_processed = 0.0f,
    .ch1_processed = 0.0f,
    .ch2_processed = 0.0f,

    // 超限标志初始化
    .ch0_over_limit = 0,
    .ch1_over_limit = 0,
    .ch2_over_limit = 0,

    // 状态初始化
    .timestamp = 0,
    .data_valid = 0,
    .sampling_active = 0
};

uint8_t g_hide_mode_enabled = 0; //数据加密开关

static float voltage_median_filter(float new_value) //去掉奇怪的电压值
{
    median_filter_buffer[median_filter_index] = new_value; //存新值
    median_filter_index = (median_filter_index + 1) % MEDIAN_FILTER_SIZE;

    float temp_buffer[MEDIAN_FILTER_SIZE]; //复制数据来排序
    for (uint8_t i = 0; i < MEDIAN_FILTER_SIZE; i++) {
        temp_buffer[i] = median_filter_buffer[i];
    }

    for (uint8_t i = 0; i < MEDIAN_FILTER_SIZE - 1; i++) { //冒泡排序
        for (uint8_t j = 0; j < MEDIAN_FILTER_SIZE - 1 - i; j++) {
            if (temp_buffer[j] > temp_buffer[j + 1]) {
                float temp = temp_buffer[j];
                temp_buffer[j] = temp_buffer[j + 1];
                temp_buffer[j + 1] = temp;
            }
        }
    }

    return temp_buffer[MEDIAN_FILTER_SIZE / 2]; //返回中间值
}

static float voltage_sliding_average_filter(float new_voltage) //让显示稳定不跳
{
    voltage_filter_buffer[voltage_filter_index] = new_voltage; //存新值
    voltage_filter_index = (voltage_filter_index + 1) % VOLTAGE_FILTER_SIZE;

    if (!voltage_filter_filled && voltage_filter_index == 0) { //记录是否存满
        voltage_filter_filled = 1;
    }

    float sum = 0.0f; //计算平均值
    uint8_t count = voltage_filter_filled ? VOLTAGE_FILTER_SIZE : voltage_filter_index;

    for (uint8_t i = 0; i < count; i++) {
        sum += voltage_filter_buffer[i];
    }

    return sum / count; //返回平均值
}

void adc_dma_init(void) //启动电压测量系统
{
    HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_dma_buffer, ADC_DMA_BUFFER_SIZE); //开始测量
}

void adc_task(void) //电压测量主函数，每0.1秒跑一次
{
    uint32_t adc_sum = 0; //累加测量结果

    for (uint16_t i = 0; i < ADC_DMA_BUFFER_SIZE; i++) { //把所有测量值加起来
        adc_sum += adc_dma_buffer[i];
    }

    adc_val = adc_sum / ADC_DMA_BUFFER_SIZE; //算平均值

    float raw_voltage = ((float)adc_val * 3.3f) / 4096.0f; //转成电压值

    float median_filtered = voltage_median_filter(raw_voltage); //去异常值
    voltage = voltage_sliding_average_filter(median_filtered); //平滑处理

    float ratio = config_get_ratio(); //用户设的比例
    g_adc_control.processed_voltage = voltage * ratio;

    adc_check_over_limit(); //检查是否超限

    if (g_adc_control.state == SAMPLING_ACTIVE) { //如果在定时测量
        adc_process_sample();
    }
}

void adc_control_init(void) //初始化测量系统
{
    g_adc_control.state = SAMPLING_IDLE; //待机状态
    g_adc_control.last_sample_time = 0; //清空时间
    g_adc_control.cycle_ms = 5000; //默认5秒采样间隔
    g_adc_control.processed_voltage = 0.0f; //清空电压
    g_adc_control.display_voltage = 0.0f; //清空显示值
    g_adc_control.over_limit = 0; //清除超限
    g_adc_control.led1_blink_state = 0; //清除LED状态

    for (uint8_t i = 0; i < VOLTAGE_FILTER_SIZE; i++) { //清空历史数据
        voltage_filter_buffer[i] = 0.0f;
    }
    voltage_filter_index = 0;
    voltage_filter_filled = 0;

    for (uint8_t i = 0; i < MEDIAN_FILTER_SIZE; i++) { //清空过滤数据
        median_filter_buffer[i] = 0.0f;
    }
    median_filter_index = 0;

    sampling_cycle_t saved_cycle = config_get_sample_cycle(); //加载用户设置
    adc_set_cycle(saved_cycle);
}

void adc_start_sampling(void) //开始定时测量
{
    g_adc_control.state = SAMPLING_ACTIVE; //设为测量状态
    g_adc_control.last_sample_time = HAL_GetTick(); //记录开始时间

    adc_process_sample(); //立即测一次
}

void adc_stop_sampling(void) //停止测量
{
    g_adc_control.state = SAMPLING_IDLE; //回到待机
    g_adc_control.over_limit = 0; //清除超限

    extern uint8_t ucLed[6]; //LED数组
    ucLed[0] = 0; //关LED1
    ucLed[1] = 0; //关LED2
}

void adc_set_cycle(sampling_cycle_t cycle) //设测量间隔
{
    g_adc_control.cycle = cycle;

    switch (cycle) {
        case CYCLE_5S:
            g_adc_control.cycle_ms = 5000; //5秒
            break;
        case CYCLE_10S:
            g_adc_control.cycle_ms = 10000; //10秒
            break;
        case CYCLE_15S:
            g_adc_control.cycle_ms = 15000; //15秒
            break;
        default:
            g_adc_control.cycle_ms = 5000; //默认5秒
            break;
    }
}

void adc_process_sample(void) //处理定时测量数据
{
    uint32_t current_time = HAL_GetTick();

    // 调试输出已移除，保持代码简洁

    if ((current_time - g_adc_control.last_sample_time) >= g_adc_control.cycle_ms) { //时间到了
        g_adc_control.last_sample_time += g_adc_control.cycle_ms; //更新下次时间

        char time_buffer[32] = {0}; //获取当前时间
        rtc_format_current_time_string(time_buffer, sizeof(time_buffer));

        // 检查是否为多通道连续采样模式
        if (g_multi_channel_data.sampling_active) {
            // 多通道连续采样模式：自动发送数据
            multi_channel_read_data();        // 读取3通道数据
            multi_channel_apply_ratios();     // 应用变比计算
            multi_channel_check_limits();     // 检查超限状态
            multi_channel_update_timestamp(); // 更新时间戳

            // 根据模式发送数据
            if (g_multi_channel_data.binary_mode) {
                // 二进制模式 (第16题)
                multi_channel_send_binary_data();

                // 记录连续采样数据到日志
                char log_buffer[128] = {0};
                snprintf(log_buffer, sizeof(log_buffer), "binary continuous sample: ch0=%.2f,ch1=%.2f,ch2=%.2f",
                         g_multi_channel_data.ch0_processed,
                         g_multi_channel_data.ch1_processed,
                         g_multi_channel_data.ch2_processed);
                sd_write_log_data(log_buffer);
            } else {
                // 文本模式 (第8题)
                char output_buffer[128] = {0};
                multi_channel_format_output(output_buffer, sizeof(output_buffer), 1); // 包含时间戳
                my_printf(&huart1, "report:%s\r\n", output_buffer);

                // 记录连续采样数据到日志
                char log_buffer[128] = {0};
                snprintf(log_buffer, sizeof(log_buffer), "text continuous sample: %s", output_buffer);
                sd_write_log_data(log_buffer);
            }
        }
        // 注意：原有的单通道输出已关闭，符合测评要求

        g_adc_control.display_voltage = g_adc_control.processed_voltage; //更新显示值

        if (g_adc_control.over_limit) { //保存数据
            sd_write_overlimit_data(time_buffer, g_adc_control.processed_voltage, config_get_limit());
        } else if (!g_hide_mode_enabled) {
            sd_write_sample_data(time_buffer, g_adc_control.processed_voltage);
        }

        if (g_hide_mode_enabled) { //加密数据
            sd_write_hidedata_with_voltage(g_adc_control.processed_voltage, g_adc_control.over_limit);
        }
    }
}

void adc_check_over_limit(void) //检查是否超限
{
    float limit = config_get_limit(); //用户设的限制值
    extern uint8_t ucLed[6]; //LED数组

    if (g_adc_control.processed_voltage > limit) {
        g_adc_control.over_limit = 1; //超限了
        ucLed[1] = 1; //亮LED2
    } else {
        g_adc_control.over_limit = 0; //正常
        ucLed[1] = 0; //关LED2
    }
}

void adc_convert_to_hex(hex_data_t *hex_data) //转成加密格式
{
    if (hex_data == NULL) return;

    hex_data->timestamp = rtc_get_unix_timestamp_now(); //时间戳
    hex_data->voltage_integer = (uint16_t)g_adc_control.processed_voltage; //整数部分
    float decimal_part = g_adc_control.processed_voltage - hex_data->voltage_integer;
    hex_data->voltage_decimal = (uint16_t)(decimal_part * 65536.0f); //小数部分
}

void adc_format_hex_string(const hex_data_t *hex_data, char *buffer, size_t buffer_size) //格式化16进制字符串
{
    if (hex_data == NULL || buffer == NULL || buffer_size == 0) return;

    snprintf(buffer, buffer_size, "%08X%04X%04X", //组合成字符串
             (unsigned int)hex_data->timestamp, //时间戳8位
             hex_data->voltage_integer, //电压整数4位
             hex_data->voltage_decimal); //电压小数4位
}

// --- 电流校准函数实现区域 ---

// --- 多通道数据采集函数实现区域 ---

/**
 * @brief 多通道数据采集初始化
 * @details 初始化多通道数据结构和相关参数
 */
void multi_channel_init(void)
{
    // 初始化多通道数据
    g_multi_channel_data.ch0_raw = 0.0f;
    g_multi_channel_data.ch1_raw = 0.0f;
    g_multi_channel_data.ch2_raw = 0.0f;

    g_multi_channel_data.ch0_processed = 0.0f;
    g_multi_channel_data.ch1_processed = 0.0f;
    g_multi_channel_data.ch2_processed = 0.0f;

    g_multi_channel_data.ch0_over_limit = 0;
    g_multi_channel_data.ch1_over_limit = 0;
    g_multi_channel_data.ch2_over_limit = 0;

    g_multi_channel_data.timestamp = 0;
    g_multi_channel_data.data_valid = 0;
    g_multi_channel_data.sampling_active = 0;
    g_multi_channel_data.binary_mode = 0; // 默认文本模式
}

/**
 * @brief 读取3通道数据
 * @details 从GD30AD3344芯片读取3通道的原始数据
 */
void multi_channel_read_data(void)
{
    // 选择数据源：真实硬件 vs 模拟数据
    #ifdef USE_REAL_HARDWARE_ADC
        // === 真实硬件接口实现 ===
        // 参考您的成功代码：只采集电流通道（CH1），其他通道设为0

        // 切换为电压采集验证模式
        // CH0（电压通道）- 使用您的采样板函数读取
        g_multi_channel_data.ch0_raw = sampling_board_read_channel(SAMPLING_BOARD_CH0);

        // CH1（电流通道）- 未接入，设为0
        g_multi_channel_data.ch1_raw = 0.0f;

        // CH2（电阻通道）- 未接入，设为0
        g_multi_channel_data.ch2_raw = 0.0f;

        // CH1数据采集完成

        // 应用硬件校准系数（只对CH1应用校准）
        if (g_multi_channel_data.ch1_raw > 0.0f) {
            g_multi_channel_data.ch1_raw *= CH1_CALIBRATION_FACTOR;
        }

    #else
        // === 测试模式：使用模拟数据 ===
        // 当前用于串口测试，生成不同的模拟数据
        static uint32_t sample_counter = 0;
        sample_counter++;

        // 生成变化的模拟数据，便于测试验证
        float base_voltage = voltage; // 使用现有的电压值作为基础

        // 通道0: 电压 (基础值 + 小幅波动)
        g_multi_channel_data.ch0_raw = base_voltage + 0.1f * sinf(sample_counter * 0.1f);

        // 通道1: 电流 (模拟电流传感器，有不同的变化模式)
        g_multi_channel_data.ch1_raw = (base_voltage * 6.06f) + 0.5f * cosf(sample_counter * 0.15f);

        // 通道2: 电阻 (模拟电阻测量，有更大的变化范围)
        g_multi_channel_data.ch2_raw = (base_voltage * 3030.0f) + 100.0f * sinf(sample_counter * 0.08f);

        // 确保数值在合理范围内
        if (g_multi_channel_data.ch0_raw < 0) g_multi_channel_data.ch0_raw = 0;
        if (g_multi_channel_data.ch1_raw < 0) g_multi_channel_data.ch1_raw = 0;
        if (g_multi_channel_data.ch2_raw < 0) g_multi_channel_data.ch2_raw = 0;
    #endif

    // 标记数据有效
    g_multi_channel_data.data_valid = 1;
}

/**
 * @brief 应用变比计算
 * @details 将原始数据乘以对应通道的变比
 */
void multi_channel_apply_ratios(void)
{
    if (!g_multi_channel_data.data_valid) return;

    // 获取3通道变比
    float ch0_ratio, ch1_ratio, ch2_ratio;
    config_get_all_ratios(&ch0_ratio, &ch1_ratio, &ch2_ratio);

    // 变比获取完成

    // 应用变比计算
    // CH0: 电压通道 - 使用您的电压校准函数
    if (g_multi_channel_data.ch0_raw > 0.0f) {
        // 使用您的电压校准函数
        float calibrated_voltage = voltage_calibrate_segmented(g_multi_channel_data.ch0_raw);
        // 应用变比
        g_multi_channel_data.ch0_processed = calibrated_voltage * ch0_ratio;
    } else {
        g_multi_channel_data.ch0_processed = 0.0f;
    }

    // CH1: 电流通道 - 未接入，设为0
    g_multi_channel_data.ch1_processed = 0.0f;

    // CH2: 电阻通道 - 直接应用变比
    g_multi_channel_data.ch2_processed = g_multi_channel_data.ch2_raw * ch2_ratio;

    // 更新兼容性字段
    g_adc_control.processed_voltage = g_multi_channel_data.ch0_processed;
}

/**
 * @brief 检查超限状态
 * @details 检查各通道是否超过设定的阈值
 */
void multi_channel_check_limits(void)
{
    if (!g_multi_channel_data.data_valid) return;

    // 获取3通道阈值
    float ch0_limit, ch1_limit, ch2_limit;
    config_get_all_limits(&ch0_limit, &ch1_limit, &ch2_limit);

    // 检查超限状态
    g_multi_channel_data.ch0_over_limit = (g_multi_channel_data.ch0_processed > ch0_limit) ? 1 : 0;
    g_multi_channel_data.ch1_over_limit = (g_multi_channel_data.ch1_processed > ch1_limit) ? 1 : 0;
    g_multi_channel_data.ch2_over_limit = (g_multi_channel_data.ch2_processed > ch2_limit) ? 1 : 0;

    // 更新兼容性字段 (任一通道超限则标记超限)
    g_adc_control.over_limit = (g_multi_channel_data.ch0_over_limit ||
                               g_multi_channel_data.ch1_over_limit ||
                               g_multi_channel_data.ch2_over_limit) ? 1 : 0;
}

/**
 * @brief 更新时间戳
 * @details 更新采样时间戳
 */
void multi_channel_update_timestamp(void)
{
    g_multi_channel_data.timestamp = rtc_get_unix_timestamp();
}

/**
 * @brief 格式化多通道输出
 * @param buffer 输出缓冲区
 * @param buffer_size 缓冲区大小
 * @param include_timestamp 是否包含时间戳
 */
void multi_channel_format_output(char *buffer, size_t buffer_size, uint8_t include_timestamp)
{
    if (buffer == NULL || buffer_size == 0) return;

    if (include_timestamp) {
        // 格式化时间戳
        char time_str[32] = {0};
        rtc_format_current_time_string(time_str, sizeof(time_str));

        // 包含时间戳的格式: "2025-01-01 12:00:00 ch0=xx.xx,ch1=xx.xx,ch2=xx.xx"
        snprintf(buffer, buffer_size, "%s ch0%s=%.2f,ch1%s=%.2f,ch2%s=%.2f",
                 time_str,
                 g_multi_channel_data.ch0_over_limit ? "*" : "", g_multi_channel_data.ch0_processed,
                 g_multi_channel_data.ch1_over_limit ? "*" : "", g_multi_channel_data.ch1_processed,
                 g_multi_channel_data.ch2_over_limit ? "*" : "", g_multi_channel_data.ch2_processed);
    } else {
        // 不包含时间戳的格式: "ch0=xx.xx,ch1=xx.xx,ch2=xx.xx"
        snprintf(buffer, buffer_size, "ch0%s=%.2f,ch1%s=%.2f,ch2%s=%.2f",
                 g_multi_channel_data.ch0_over_limit ? "*" : "", g_multi_channel_data.ch0_processed,
                 g_multi_channel_data.ch1_over_limit ? "*" : "", g_multi_channel_data.ch1_processed,
                 g_multi_channel_data.ch2_over_limit ? "*" : "", g_multi_channel_data.ch2_processed);
    }
}

/**
 * @brief 发送二进制格式的连续采样数据
 * @details 按照第16题要求的格式发送24字节二进制数据
 */
void multi_channel_send_binary_data(void)
{
    // 按照第16题要求构建24字节响应
    uint8_t response_bytes[24];

    // 设备ID (2字节) - 大端序
    extern uint16_t device_id_get(void);
    uint16_t device_id = device_id_get();
    response_bytes[0] = (device_id >> 8) & 0xFF; // 高字节
    response_bytes[1] = device_id & 0xFF;        // 低字节

    // 消息类型 (1字节) - 0x01 数据
    response_bytes[2] = 0x01;

    // 报文长度 (2字节) - 0x0018 (24字节) 大端序
    response_bytes[3] = 0x00;  // 高字节
    response_bytes[4] = 0x18;  // 低字节

    // 协议版本 (1字节) - 0x01
    response_bytes[5] = 0x01;

    // 时间戳 (4字节) - UNIX时间戳 大端序
    uint32_t timestamp = g_multi_channel_data.timestamp;
    response_bytes[6] = (timestamp >> 24) & 0xFF;
    response_bytes[7] = (timestamp >> 16) & 0xFF;
    response_bytes[8] = (timestamp >> 8) & 0xFF;
    response_bytes[9] = timestamp & 0xFF;

    // 通道0数据 (4字节) - IEEE 754浮点数 大端序
    union { float f; uint32_t i; } ch0_union;
    ch0_union.f = g_multi_channel_data.ch0_processed;
    response_bytes[10] = (ch0_union.i >> 24) & 0xFF;
    response_bytes[11] = (ch0_union.i >> 16) & 0xFF;
    response_bytes[12] = (ch0_union.i >> 8) & 0xFF;
    response_bytes[13] = ch0_union.i & 0xFF;

    // 通道1数据 (4字节) - IEEE 754浮点数 大端序
    union { float f; uint32_t i; } ch1_union;
    ch1_union.f = g_multi_channel_data.ch1_processed;
    response_bytes[14] = (ch1_union.i >> 24) & 0xFF;
    response_bytes[15] = (ch1_union.i >> 16) & 0xFF;
    response_bytes[16] = (ch1_union.i >> 8) & 0xFF;
    response_bytes[17] = ch1_union.i & 0xFF;

    // 通道2数据 (4字节) - IEEE 754浮点数 大端序
    union { float f; uint32_t i; } ch2_union;
    ch2_union.f = g_multi_channel_data.ch2_processed;
    response_bytes[18] = (ch2_union.i >> 24) & 0xFF;
    response_bytes[19] = (ch2_union.i >> 16) & 0xFF;
    response_bytes[20] = (ch2_union.i >> 8) & 0xFF;
    response_bytes[21] = ch2_union.i & 0xFF;

    // 计算CRC校验 (前22字节) - 使用题目专用算法
    extern uint16_t crc16_calculate_exam(const uint8_t *data, size_t length);
    uint16_t crc = crc16_calculate_exam(response_bytes, 22);

    // CRC校验 (2字节) - 大端序
    response_bytes[22] = (crc >> 8) & 0xFF; // 高字节
    response_bytes[23] = crc & 0xFF;        // 低字节

    // 转换为十六进制字符串
    char hex_response[64] = {0};
    for (int i = 0; i < 24; i++) {
        sprintf(hex_response + i * 2, "%02X", response_bytes[i]);
    }

    // 发送响应 - 按照题目要求格式
    extern UART_HandleTypeDef huart1;
    extern int my_printf(UART_HandleTypeDef *huart, const char *format, ...);
    my_printf(&huart1, "report:%s\r\n", hex_response);
}

/**
 * @brief 获取通道数据
 * @param ch0 通道0数据指针
 * @param ch1 通道1数据指针
 * @param ch2 通道2数据指针
 * @param processed 是否获取变比后数据 (1: 变比后, 0: 原始数据)
 */
void multi_channel_get_data(float *ch0, float *ch1, float *ch2, uint8_t processed)
{
    if (processed) {
        if (ch0) *ch0 = g_multi_channel_data.ch0_processed;
        if (ch1) *ch1 = g_multi_channel_data.ch1_processed;
        if (ch2) *ch2 = g_multi_channel_data.ch2_processed;
    } else {
        if (ch0) *ch0 = g_multi_channel_data.ch0_raw;
        if (ch1) *ch1 = g_multi_channel_data.ch1_raw;
        if (ch2) *ch2 = g_multi_channel_data.ch2_raw;
    }
}

/**
 * @brief 获取超限标志
 * @retval 超限标志位组合 (bit0: ch0, bit1: ch1, bit2: ch2)
 */
uint8_t multi_channel_get_over_limit_flags(void)
{
    uint8_t flags = 0;
    if (g_multi_channel_data.ch0_over_limit) flags |= 0x01;
    if (g_multi_channel_data.ch1_over_limit) flags |= 0x02;
    if (g_multi_channel_data.ch2_over_limit) flags |= 0x04;
    return flags;
}

/**
 * @brief 开始连续采样
 */
/**
 * @brief 启动文本模式连续采样 (第8题)
 */
void multi_channel_start_continuous_sampling_text(void)
{
    g_multi_channel_data.sampling_active = 1;
    g_multi_channel_data.binary_mode = 0; // 文本模式
    g_adc_control.state = SAMPLING_ACTIVE;

    // 设置连续采样的时间间隔 (默认5秒，可通过配置调整)
    if (g_adc_control.cycle_ms == 0) {
        g_adc_control.cycle_ms = 5000; // 默认5秒间隔
    }

    // 重置采样时间戳，立即开始采样
    g_adc_control.last_sample_time = HAL_GetTick();
}

/**
 * @brief 启动二进制模式连续采样 (第16题)
 */
void multi_channel_start_continuous_sampling_binary(void)
{
    g_multi_channel_data.sampling_active = 1;
    g_multi_channel_data.binary_mode = 1; // 二进制模式
    g_adc_control.state = SAMPLING_ACTIVE;

    // 设置连续采样的时间间隔 (默认5秒，可通过配置调整)
    if (g_adc_control.cycle_ms == 0) {
        g_adc_control.cycle_ms = 5000; // 默认5秒间隔
    }

    // 重置采样时间戳，立即开始采样
    g_adc_control.last_sample_time = HAL_GetTick();
}

/**
 * @brief 启动连续采样 (兼容性函数，默认文本模式)
 */
void multi_channel_start_continuous_sampling(void)
{
    multi_channel_start_continuous_sampling_text();
}

/**
 * @brief 停止连续采样
 */
void multi_channel_stop_continuous_sampling(void)
{
    g_multi_channel_data.sampling_active = 0;
    g_adc_control.state = SAMPLING_IDLE;
}

/**
 * @brief 初始化多通道硬件接口
 * @details 根据配置初始化真实硬件或测试模式
 */
void multi_channel_hardware_init(void)
{
    #ifdef USE_REAL_HARDWARE_ADC
        // === 真实硬件初始化 ===
        // 初始化GD30AD3344芯片
        GD30AD3344_Init();

        // 可以在这里添加额外的硬件配置
        // 例如：设置采样频率、滤波器等

        // 记录硬件初始化状态
        char log_buffer[64] = {0};
        snprintf(log_buffer, sizeof(log_buffer), "Multi-channel hardware initialized (GD30AD3344)");
        sd_write_log_data(log_buffer);

    #else
        // === 测试模式初始化 ===
        // 初始化模拟数据生成器
        // 可以在这里设置模拟数据的参数

        // 记录测试模式状态
        char log_buffer[64] = {0};
        snprintf(log_buffer, sizeof(log_buffer), "Multi-channel test mode initialized (simulated data)");
        sd_write_log_data(log_buffer);
    #endif
}

/**
 * @brief 获取硬件状态信息
 * @retval 硬件状态字符串
 */
const char* multi_channel_get_hardware_status(void)
{
    #ifdef USE_REAL_HARDWARE_ADC
        return "Real Hardware (GD30AD3344)";
    #else
        return "Test Mode (Simulated Data)";
    #endif
}

/**
 * @brief 切换到真实硬件模式 (运行时切换)
 * @note 这个函数可以在运行时动态切换数据源
 */
void multi_channel_enable_real_hardware(void)
{
    // 这里可以添加运行时切换逻辑
    // 例如：设置全局标志、重新初始化硬件等

    // 记录切换操作
    char log_buffer[64] = {0};
    snprintf(log_buffer, sizeof(log_buffer), "Switched to real hardware mode");
    sd_write_log_data(log_buffer);
}

/**
 * @brief 切换到测试模式 (运行时切换)
 * @note 这个函数可以在运行时动态切换数据源
 */
void multi_channel_enable_test_mode(void)
{
    // 这里可以添加运行时切换逻辑

    // 记录切换操作
    char log_buffer[64] = {0};
    snprintf(log_buffer, sizeof(log_buffer), "Switched to test mode");
    sd_write_log_data(log_buffer);
}

void adc_led1_blink_task(void) //LED1闪烁任务
{
    extern uint8_t ucLed[6]; //LED数组

    if (g_adc_control.state == SAMPLING_ACTIVE) {
        g_adc_control.led1_blink_state = !g_adc_control.led1_blink_state; //切换状态
        ucLed[0] = g_adc_control.led1_blink_state;
    } else {
        ucLed[0] = 0; //关闭
        g_adc_control.led1_blink_state = 0;
    }
}

