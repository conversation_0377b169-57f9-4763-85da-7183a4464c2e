# 第7章：ADC数据采集与滤波算法实现

## 🎯 学习目标
- 深入理解ADC模数转换的工作原理
- 掌握ADC_HandleTypeDef结构体配置
- 理解DMA连续采集的实现机制
- 学会数字滤波算法的设计和实现
- 掌握数据处理流程和状态管理

## 📋 目录
1. [ADC基础概念](#1-adc基础概念)
2. [ADC_HandleTypeDef结构体详解](#2-adc_handletypedef结构体详解)
3. [DMA连续采集机制](#3-dma连续采集机制)
4. [数字滤波算法实现](#4-数字滤波算法实现)
5. [数据处理流程分析](#5-数据处理流程分析)
6. [采样控制与状态管理](#6-采样控制与状态管理)
7. [性能优化技巧](#7-性能优化技巧)
8. [实践练习](#8-实践练习)

---

## 1. ADC基础概念

### 1.1 什么是ADC？
ADC (Analog-to-Digital Converter) 模数转换器，将连续的模拟信号转换为离散的数字信号。

**ADC特点：**
- **量化精度**: 12位ADC提供4096个量化级别
- **采样速度**: 最高2.4MSPS采样率
- **多通道**: 支持16个外部通道
- **参考电压**: 内部或外部参考电压

### 1.2 STM32F429 ADC特性
- **分辨率**: 12位、10位、8位、6位可选
- **转换时间**: 最快1.5μs (12位)
- **工作电压**: 2.4V ~ 3.6V
- **输入范围**: 0V ~ VREF+ (通常3.3V)

### 1.3 ADC转换公式
```
数字值 = (模拟电压 / 参考电压) × (2^分辨率 - 1)

例如：12位ADC，参考电压3.3V
输入1.65V → 数字值 = (1.65 / 3.3) × 4095 = 2047
```

---

## 2. ADC_HandleTypeDef结构体详解

### 2.1 结构体定义分析
```c
typedef struct {
    ADC_TypeDef *Instance;          // ADC外设基地址 ⭐
    ADC_InitTypeDef Init;           // 初始化参数 ⭐
    DMA_HandleTypeDef *DMA_Handle;  // DMA句柄
    HAL_LockTypeDef Lock;           // 锁定状态
    HAL_ADC_StateTypeDef State;     // ADC状态
    uint32_t ErrorCode;            // 错误代码
} ADC_HandleTypeDef;
```

### 2.2 项目中的ADC配置
```c
// 来自adc.c的配置
ADC_HandleTypeDef hadc1;

void MX_ADC1_Init(void)
{
    hadc1.Instance = ADC1;                              // 使用ADC1外设
    hadc1.Init.ClockPrescaler = ADC_CLOCK_SYNC_PCLK_DIV4;  // 时钟4分频
    hadc1.Init.Resolution = ADC_RESOLUTION_12B;         // 12位分辨率
    hadc1.Init.ScanConvMode = DISABLE;                  // 禁用扫描模式
    hadc1.Init.ContinuousConvMode = ENABLE;             // 连续转换模式 ⭐
    hadc1.Init.DiscontinuousConvMode = DISABLE;         // 禁用间断模式
    hadc1.Init.ExternalTrigConvEdge = ADC_EXTERNALTRIGCONVEDGE_NONE;  // 无外部触发
    hadc1.Init.ExternalTrigConv = ADC_SOFTWARE_START;   // 软件触发
    hadc1.Init.DataAlign = ADC_DATAALIGN_RIGHT;         // 右对齐
    hadc1.Init.NbrOfConversion = 1;                     // 转换通道数
    hadc1.Init.DMAContinuousRequests = ENABLE;          // DMA连续请求 ⭐
    hadc1.Init.EOCSelection = ADC_EOC_SINGLE_CONV;      // 单次转换结束
}
```

### 2.3 关键配置参数解析

#### 2.3.1 时钟配置
```c
hadc1.Init.ClockPrescaler = ADC_CLOCK_SYNC_PCLK_DIV4;  // 4分频
```
**时钟计算：**
```
APB2时钟 = 144MHz
ADC时钟 = 144MHz / 4 = 36MHz
转换时间 = (采样时间 + 转换时间) / ADC时钟
```

#### 2.3.2 连续转换模式
```c
hadc1.Init.ContinuousConvMode = ENABLE;  // 连续转换
```
**优势：**
- 自动连续采样，无需软件干预
- 配合DMA实现高效数据采集
- 减少CPU负载

#### 2.3.3 采样时间配置
```c
sConfig.SamplingTime = ADC_SAMPLETIME_480CYCLES;  // 480个时钟周期
```
**采样时间选择：**
- 更长采样时间 → 更高精度，更低速度
- 更短采样时间 → 更高速度，可能精度下降

---

## 3. DMA连续采集机制

### 3.1 DMA配置分析
```c
// DMA句柄定义
DMA_HandleTypeDef hdma_adc1;

// DMA配置
hdma_adc1.Instance = DMA2_Stream0;                    // DMA2数据流0
hdma_adc1.Init.Channel = DMA_CHANNEL_0;               // 通道0
hdma_adc1.Init.Direction = DMA_PERIPH_TO_MEMORY;      // 外设到内存
hdma_adc1.Init.PeriphInc = DMA_PINC_DISABLE;          // 外设地址不递增
hdma_adc1.Init.MemInc = DMA_MINC_ENABLE;              // 内存地址递增
hdma_adc1.Init.PeriphDataAlignment = DMA_PDATAALIGN_WORD;  // 外设32位
hdma_adc1.Init.MemDataAlignment = DMA_MDATAALIGN_WORD;     // 内存32位
hdma_adc1.Init.Mode = DMA_CIRCULAR;                   // 循环模式 ⭐
hdma_adc1.Init.Priority = DMA_PRIORITY_HIGH;          // 高优先级
```

### 3.2 DMA缓冲区设计
```c
#define ADC_DMA_BUFFER_SIZE 32  // 缓冲区大小
uint32_t adc_dma_buffer[ADC_DMA_BUFFER_SIZE];  // DMA目标缓冲区
```

**缓冲区设计考虑：**
- **大小选择**: 32个采样点平衡精度和内存使用
- **数据类型**: uint32_t匹配ADC数据寄存器
- **循环覆盖**: DMA循环模式自动覆盖旧数据

### 3.3 DMA启动流程
```c
void adc_dma_init(void)
{
    // 启动ADC DMA连续采集
    HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_dma_buffer, ADC_DMA_BUFFER_SIZE);
}
```

**工作流程：**
1. ADC连续转换产生数据
2. DMA自动将数据搬移到缓冲区
3. 缓冲区满后从头开始覆盖
4. CPU定期读取缓冲区进行处理

---

## 4. 数字滤波算法实现

### 4.1 中值滤波算法

#### 4.1.1 算法原理
中值滤波用于去除突发的异常值（脉冲噪声）。

```c
#define MEDIAN_FILTER_SIZE 5  // 滤波窗口大小
static float median_filter_buffer[MEDIAN_FILTER_SIZE] = {0};
static uint8_t median_filter_index = 0;

static float voltage_median_filter(float new_value)
{
    // 1. 存储新值到环形缓冲区
    median_filter_buffer[median_filter_index] = new_value;
    median_filter_index = (median_filter_index + 1) % MEDIAN_FILTER_SIZE;

    // 2. 复制数据用于排序
    float temp_buffer[MEDIAN_FILTER_SIZE];
    for (uint8_t i = 0; i < MEDIAN_FILTER_SIZE; i++) {
        temp_buffer[i] = median_filter_buffer[i];
    }

    // 3. 冒泡排序
    for (uint8_t i = 0; i < MEDIAN_FILTER_SIZE - 1; i++) {
        for (uint8_t j = 0; j < MEDIAN_FILTER_SIZE - 1 - i; j++) {
            if (temp_buffer[j] > temp_buffer[j + 1]) {
                float temp = temp_buffer[j];
                temp_buffer[j] = temp_buffer[j + 1];
                temp_buffer[j + 1] = temp;
            }
        }
    }

    // 4. 返回中位数
    return temp_buffer[MEDIAN_FILTER_SIZE / 2];
}
```

#### 4.1.2 算法特点
- ✅ 有效去除脉冲噪声
- ✅ 保持信号边沿特性
- ❌ 计算复杂度较高O(n²)
- ❌ 对高斯噪声效果一般

### 4.2 滑动平均滤波算法

#### 4.2.1 算法原理
滑动平均滤波用于平滑信号，减少随机噪声。

```c
#define VOLTAGE_FILTER_SIZE 16  // 滤波窗口大小
static float voltage_filter_buffer[VOLTAGE_FILTER_SIZE] = {0};
static uint8_t voltage_filter_index = 0;
static uint8_t voltage_filter_filled = 0;

static float voltage_sliding_average_filter(float new_voltage)
{
    // 1. 存储新值
    voltage_filter_buffer[voltage_filter_index] = new_voltage;
    voltage_filter_index = (voltage_filter_index + 1) % VOLTAGE_FILTER_SIZE;

    // 2. 检查缓冲区是否已满
    if (!voltage_filter_filled && voltage_filter_index == 0) {
        voltage_filter_filled = 1;
    }

    // 3. 计算平均值
    float sum = 0.0f;
    uint8_t count = voltage_filter_filled ? VOLTAGE_FILTER_SIZE : voltage_filter_index;

    for (uint8_t i = 0; i < count; i++) {
        sum += voltage_filter_buffer[i];
    }

    return sum / count;
}
```

#### 4.2.2 算法特点
- ✅ 有效减少随机噪声
- ✅ 计算简单，效率高O(n)
- ✅ 实现简单，内存占用固定
- ❌ 会平滑信号边沿
- ❌ 响应速度较慢

### 4.3 滤波器级联设计
```c
void adc_task(void)
{
    // 1. 计算DMA缓冲区平均值
    uint32_t adc_sum = 0;
    for (uint16_t i = 0; i < ADC_DMA_BUFFER_SIZE; i++) {
        adc_sum += adc_dma_buffer[i];
    }
    adc_val = adc_sum / ADC_DMA_BUFFER_SIZE;

    // 2. ADC值转换为电压
    float raw_voltage = ((float)adc_val * 3.3f) / 4096.0f;

    // 3. 级联滤波处理
    float median_filtered = voltage_median_filter(raw_voltage);      // 先中值滤波
    voltage = voltage_sliding_average_filter(median_filtered);       // 再平均滤波

    // 4. 应用用户设置的变比
    float ratio = config_get_ratio();
    g_adc_control.processed_voltage = voltage * ratio;
}
```

**级联优势：**
- 中值滤波去除突发异常值
- 平均滤波平滑剩余噪声
- 两级滤波效果更佳

---

## 5. 数据处理流程分析

### 5.1 完整数据流程
```
硬件采集 → DMA传输 → 缓冲区平均 → 电压转换 → 中值滤波 → 平均滤波 → 变比应用 → 超限检测
    ↓         ↓         ↓          ↓         ↓         ↓         ↓         ↓
  ADC转换 → DMA搬移 → 32点平均 → ADC→V公式 → 去异常值 → 平滑处理 → 用户变比 → 阈值比较
```

### 5.2 ADC值到电压转换
```c
// ADC值转电压公式
float raw_voltage = ((float)adc_val * 3.3f) / 4096.0f;

// 公式推导：
// 电压 = (ADC数字值 / ADC最大值) × 参考电压
// 电压 = (adc_val / 4095) × 3.3V
// 
// 注意：4096是2^12，但实际最大值是4095
// 项目中使用4096.0f是为了简化计算，误差很小
```

### 5.3 变比应用
```c
float ratio = config_get_ratio();  // 获取用户设置的变比
g_adc_control.processed_voltage = voltage * ratio;
```

**变比作用：**
- 适配不同的传感器量程
- 实现单位转换
- 用户可通过命令设置

### 5.4 超限检测
```c
void adc_check_over_limit(void)
{
    float limit = config_get_limit();
    extern uint8_t ucLed[6];

    if (g_adc_control.processed_voltage > limit) {
        g_adc_control.over_limit = 1;  // 设置超限标志
        ucLed[1] = 1;                  // 点亮LED2指示
    } else {
        g_adc_control.over_limit = 0;  // 清除超限标志
        ucLed[1] = 0;                  // 熄灭LED2
    }
}
```

---

## 6. 采样控制与状态管理

### 6.1 采样状态定义
```c
typedef enum {
    SAMPLING_IDLE,      // 空闲状态
    SAMPLING_ACTIVE     // 采样状态
} sampling_state_t;

typedef enum {
    CYCLE_5S,           // 5秒周期
    CYCLE_10S,          // 10秒周期
    CYCLE_15S           // 15秒周期
} sampling_cycle_t;
```

### 6.2 控制结构体设计
```c
typedef struct {
    sampling_state_t state;         // 采样状态
    sampling_cycle_t cycle;         // 采样周期
    uint32_t last_sample_time;      // 上次采样时间
    uint32_t cycle_ms;              // 当前周期毫秒数
    float processed_voltage;        // 处理后的电压值
    float display_voltage;          // 显示用电压值
    uint8_t over_limit;             // 超限标志
    uint8_t led1_blink_state;       // LED1闪烁状态
} adc_control_t;
```

### 6.3 定时采样实现
```c
void adc_process_sample(void)
{
    uint32_t current_time = HAL_GetTick();

    // 检查是否到了采样时间
    if ((current_time - g_adc_control.last_sample_time) >= g_adc_control.cycle_ms) {
        g_adc_control.last_sample_time += g_adc_control.cycle_ms;  // 更新时间

        // 获取当前时间字符串
        char time_buffer[32] = {0};
        rtc_format_current_time_string(time_buffer, sizeof(time_buffer));

        // 输出采样数据
        if (g_adc_control.over_limit) {
            my_printf(&huart1, "%s ch0=%.2fV OverLimit(%.2f)!\r\n",
                     time_buffer, g_adc_control.processed_voltage, config_get_limit());
        } else {
            my_printf(&huart1, "%s ch0=%.2fV\r\n",
                     time_buffer, g_adc_control.processed_voltage);
        }

        // 保存数据到SD卡
        if (g_adc_control.over_limit) {
            sd_write_overlimit_data(time_buffer, g_adc_control.processed_voltage, config_get_limit());
        } else {
            sd_write_sample_data(time_buffer, g_adc_control.processed_voltage);
        }
    }
}
```

---

## 7. 性能优化技巧

### 7.1 ADC稳定性优化
```c
// 1. 时钟分频降低噪声
hadc1.Init.ClockPrescaler = ADC_CLOCK_SYNC_PCLK_DIV4;

// 2. 增加采样时间提高精度
sConfig.SamplingTime = ADC_SAMPLETIME_480CYCLES;

// 3. 启动延时确保稳定
HAL_Delay(10);  // 等待ADC稳定
```

### 7.2 滤波算法优化
```c
// 1. 缓冲区大小优化
#define ADC_DMA_BUFFER_SIZE 32      // 硬件平均：32点
#define MEDIAN_FILTER_SIZE 5        // 中值滤波：5点
#define VOLTAGE_FILTER_SIZE 16      // 平均滤波：16点

// 2. 计算优化
// 避免浮点除法，使用乘法
float inv_count = 1.0f / count;
return sum * inv_count;
```

### 7.3 内存使用优化
```c
// 1. 静态分配避免动态内存
static float voltage_filter_buffer[VOLTAGE_FILTER_SIZE];

// 2. 合理的数据类型选择
uint32_t adc_dma_buffer[ADC_DMA_BUFFER_SIZE];  // ADC数据用32位
float voltage_filter_buffer[VOLTAGE_FILTER_SIZE];  // 电压用浮点
```

---

## 8. 实践练习

### 练习1：ADC基本配置
配置不同分辨率和采样时间的ADC。

### 练习2：滤波算法实现
自己实现中值滤波和平均滤波算法。

### 练习3：数据采集系统
设计完整的数据采集和处理系统。

---

## 📝 本章小结

通过本章学习，您应该掌握：

✅ **ADC基础概念** - 模数转换原理、分辨率、采样速度
✅ **ADC_HandleTypeDef配置** - 连续模式、DMA请求、时钟分频
✅ **DMA连续采集** - 循环模式、缓冲区设计、自动搬移
✅ **数字滤波算法** - 中值滤波、平均滤波、级联设计
✅ **数据处理流程** - ADC转换、滤波处理、变比应用、超限检测

**下一章预告：** 我们将学习Flash与SD卡存储系统的实现。

---

## 🔗 相关文件
- `sysFunction/adc_app.c` - ADC采集和滤波实现
- `sysFunction/adc_app.h` - ADC应用层头文件
- `sysFunction/sampling_types.h` - 采样相关类型定义
- `Core/Src/adc.c` - ADC底层配置
