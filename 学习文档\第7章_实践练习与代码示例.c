/**
 * @file    第7章_实践练习与代码示例.c
 * @brief   ADC数据采集与滤波算法实现 - 实践练习代码
 * @details 通过实际代码示例帮助理解ADC配置、DMA采集、数字滤波算法的实现原理
 * <AUTHOR>
 * @date    2025-01-07
 */

#include "main.h"
#include "stdio.h"
#include "string.h"
#include "adc.h"

// ============================================================================
// 练习1：ADC配置详解
// ============================================================================

/**
 * @brief 演示ADC_HandleTypeDef结构体配置
 */
void practice_adc_config_demo(void)
{
    printf("=== ADC配置演示 ===\r\n");
    
    // 分析项目中的ADC配置
    extern ADC_HandleTypeDef hadc1;
    
    printf("ADC_HandleTypeDef结构体大小: %d 字节\r\n", sizeof(ADC_HandleTypeDef));
    printf("当前ADC配置:\r\n");
    printf("  外设实例: ADC%d\r\n", 
           (hadc1.Instance == ADC1) ? 1 : 
           (hadc1.Instance == ADC2) ? 2 : 3);
    
    printf("  时钟预分频: %s\r\n", 
           (hadc1.Init.ClockPrescaler == ADC_CLOCK_SYNC_PCLK_DIV2) ? "PCLK/2" :
           (hadc1.Init.ClockPrescaler == ADC_CLOCK_SYNC_PCLK_DIV4) ? "PCLK/4" :
           (hadc1.Init.ClockPrescaler == ADC_CLOCK_SYNC_PCLK_DIV6) ? "PCLK/6" : "PCLK/8");
    
    printf("  分辨率: %s\r\n", 
           (hadc1.Init.Resolution == ADC_RESOLUTION_12B) ? "12位" :
           (hadc1.Init.Resolution == ADC_RESOLUTION_10B) ? "10位" :
           (hadc1.Init.Resolution == ADC_RESOLUTION_8B) ? "8位" : "6位");
    
    printf("  扫描模式: %s\r\n", 
           (hadc1.Init.ScanConvMode == ENABLE) ? "使能" : "禁用");
    
    printf("  连续转换: %s\r\n", 
           (hadc1.Init.ContinuousConvMode == ENABLE) ? "使能" : "禁用");
    
    printf("  间断模式: %s\r\n", 
           (hadc1.Init.DiscontinuousConvMode == ENABLE) ? "使能" : "禁用");
    
    printf("  外部触发: %s\r\n", 
           (hadc1.Init.ExternalTrigConv == ADC_SOFTWARE_START) ? "软件触发" : "外部触发");
    
    printf("  数据对齐: %s\r\n", 
           (hadc1.Init.DataAlign == ADC_DATAALIGN_RIGHT) ? "右对齐" : "左对齐");
    
    printf("  转换通道数: %d\r\n", hadc1.Init.NbrOfConversion);
    
    printf("  DMA连续请求: %s\r\n", 
           (hadc1.Init.DMAContinuousRequests == ENABLE) ? "使能" : "禁用");
    
    // 计算ADC时钟频率
    uint32_t pclk2_freq = HAL_RCC_GetPCLK2Freq();
    uint32_t adc_prescaler = 4;  // 根据配置确定
    uint32_t adc_clock = pclk2_freq / adc_prescaler;
    
    printf("\nADC时钟计算:\r\n");
    printf("  PCLK2频率: %d Hz\r\n", pclk2_freq);
    printf("  预分频系数: %d\r\n", adc_prescaler);
    printf("  ADC时钟: %d Hz\r\n", adc_clock);
    printf("  最大采样率: %.1f MSPS\r\n", (float)adc_clock / 1000000);
}

/**
 * @brief 演示ADC转换时间计算
 */
void practice_adc_timing_demo(void)
{
    printf("=== ADC转换时间演示 ===\r\n");
    
    // ADC时钟参数
    uint32_t adc_clock = 36000000;  // 36MHz (144MHz/4)
    
    // 不同采样时间的转换时间计算
    typedef struct {
        uint32_t sampling_cycles;
        const char *name;
    } sampling_time_t;
    
    sampling_time_t sampling_times[] = {
        {3, "3 cycles"},
        {15, "15 cycles"},
        {28, "28 cycles"},
        {56, "56 cycles"},
        {84, "84 cycles"},
        {112, "112 cycles"},
        {144, "144 cycles"},
        {480, "480 cycles"}  // 项目使用
    };
    
    uint8_t time_count = sizeof(sampling_times) / sizeof(sampling_time_t);
    
    printf("ADC转换时间分析 (12位分辨率):\r\n");
    printf("采样时间\t\t总周期\t\t转换时间\t采样率\r\n");
    printf("--------------------------------------------------------\r\n");
    
    for (uint8_t i = 0; i < time_count; i++) {
        uint32_t total_cycles = sampling_times[i].sampling_cycles + 12;  // 采样时间 + 转换时间
        float conversion_time = (float)total_cycles / adc_clock * 1000000;  // 微秒
        float sampling_rate = adc_clock / (float)total_cycles / 1000;  // kSPS
        
        printf("%s\t\t%d\t\t%.2f μs\t\t%.1f kSPS\r\n", 
               sampling_times[i].name, total_cycles, conversion_time, sampling_rate);
    }
    
    printf("\n项目配置分析:\r\n");
    printf("采样时间: 480 cycles (高精度配置)\r\n");
    printf("转换时间: %.2f μs\r\n", (float)(480 + 12) / adc_clock * 1000000);
    printf("采样率: %.1f kSPS\r\n", adc_clock / (float)(480 + 12) / 1000);
    printf("优势: 高精度，低噪声\r\n");
    printf("劣势: 采样速度较慢\r\n");
}

// ============================================================================
// 练习2：DMA连续采集演示
// ============================================================================

/**
 * @brief 演示DMA配置分析
 */
void practice_dma_config_demo(void)
{
    printf("=== DMA配置演示 ===\r\n");
    
    extern DMA_HandleTypeDef hdma_adc1;
    
    printf("DMA_HandleTypeDef结构体大小: %d 字节\r\n", sizeof(DMA_HandleTypeDef));
    printf("ADC1 DMA配置:\r\n");
    printf("  DMA实例: DMA2_Stream0\r\n");
    printf("  通道: %d\r\n", hdma_adc1.Init.Channel);
    printf("  方向: %s\r\n", 
           (hdma_adc1.Init.Direction == DMA_PERIPH_TO_MEMORY) ? "外设到内存" : 
           (hdma_adc1.Init.Direction == DMA_MEMORY_TO_PERIPH) ? "内存到外设" : "内存到内存");
    printf("  外设地址递增: %s\r\n", 
           (hdma_adc1.Init.PeriphInc == DMA_PINC_ENABLE) ? "使能" : "禁用");
    printf("  内存地址递增: %s\r\n", 
           (hdma_adc1.Init.MemInc == DMA_MINC_ENABLE) ? "使能" : "禁用");
    printf("  外设数据宽度: %s\r\n", 
           (hdma_adc1.Init.PeriphDataAlignment == DMA_PDATAALIGN_WORD) ? "32位" : 
           (hdma_adc1.Init.PeriphDataAlignment == DMA_PDATAALIGN_HALFWORD) ? "16位" : "8位");
    printf("  内存数据宽度: %s\r\n", 
           (hdma_adc1.Init.MemDataAlignment == DMA_MDATAALIGN_WORD) ? "32位" : 
           (hdma_adc1.Init.MemDataAlignment == DMA_MDATAALIGN_HALFWORD) ? "16位" : "8位");
    printf("  工作模式: %s\r\n", 
           (hdma_adc1.Init.Mode == DMA_CIRCULAR) ? "循环模式" : "普通模式");
    printf("  优先级: %s\r\n", 
           (hdma_adc1.Init.Priority == DMA_PRIORITY_HIGH) ? "高" : 
           (hdma_adc1.Init.Priority == DMA_PRIORITY_MEDIUM) ? "中" : "低");
    
    printf("\nDMA循环模式优势:\r\n");
    printf("1. 自动覆盖旧数据，无需手动管理\r\n");
    printf("2. 连续采集，无数据丢失\r\n");
    printf("3. CPU负载低，实时性好\r\n");
    printf("4. 适合连续信号监测\r\n");
}

/**
 * @brief 演示DMA缓冲区数据处理
 */
void practice_dma_buffer_demo(void)
{
    printf("=== DMA缓冲区数据处理演示 ===\r\n");
    
    // 模拟DMA缓冲区数据
    #define DEMO_BUFFER_SIZE 32
    uint32_t demo_buffer[DEMO_BUFFER_SIZE];
    
    // 生成模拟ADC数据 (2048 ± 随机噪声)
    printf("生成模拟ADC数据:\r\n");
    uint32_t base_value = 2048;  // 1.65V对应的ADC值
    
    for (uint8_t i = 0; i < DEMO_BUFFER_SIZE; i++) {
        // 添加±50的随机噪声
        int32_t noise = (rand() % 101) - 50;  // -50 到 +50
        demo_buffer[i] = base_value + noise;
        
        if (i < 8) {  // 只显示前8个数据
            printf("  [%d]: %d\r\n", i, demo_buffer[i]);
        } else if (i == 8) {
            printf("  ... (省略中间数据) ...\r\n");
        }
    }
    
    // 计算平均值
    uint32_t sum = 0;
    for (uint8_t i = 0; i < DEMO_BUFFER_SIZE; i++) {
        sum += demo_buffer[i];
    }
    uint32_t average = sum / DEMO_BUFFER_SIZE;
    
    printf("\n数据统计:\r\n");
    printf("  数据点数: %d\r\n", DEMO_BUFFER_SIZE);
    printf("  数据总和: %d\r\n", sum);
    printf("  平均值: %d\r\n", average);
    
    // 转换为电压
    float voltage = ((float)average * 3.3f) / 4096.0f;
    printf("  对应电压: %.3f V\r\n", voltage);
    
    // 计算标准差
    float variance = 0.0f;
    for (uint8_t i = 0; i < DEMO_BUFFER_SIZE; i++) {
        float diff = (float)demo_buffer[i] - average;
        variance += diff * diff;
    }
    variance /= DEMO_BUFFER_SIZE;
    float std_dev = sqrtf(variance);
    
    printf("  标准差: %.2f ADC码\r\n", std_dev);
    printf("  电压标准差: %.3f mV\r\n", std_dev * 3.3f / 4096.0f * 1000);
}

// ============================================================================
// 练习3：数字滤波算法演示
// ============================================================================

/**
 * @brief 演示中值滤波算法
 */
void practice_median_filter_demo(void)
{
    printf("=== 中值滤波算法演示 ===\r\n");
    
    #define FILTER_SIZE 5
    float test_data[] = {1.0, 1.1, 5.0, 1.2, 1.0, 1.1, 1.3, 8.0, 1.2, 1.1};  // 包含异常值
    uint8_t data_count = sizeof(test_data) / sizeof(float);
    
    float filter_buffer[FILTER_SIZE] = {0};
    uint8_t filter_index = 0;
    
    printf("中值滤波处理过程 (窗口大小: %d):\r\n", FILTER_SIZE);
    printf("输入\t\t滤波窗口\t\t\t输出\r\n");
    printf("------------------------------------------------------------\r\n");
    
    for (uint8_t i = 0; i < data_count; i++) {
        float input = test_data[i];
        
        // 更新滤波缓冲区
        filter_buffer[filter_index] = input;
        filter_index = (filter_index + 1) % FILTER_SIZE;
        
        // 复制数据用于排序
        float temp_buffer[FILTER_SIZE];
        for (uint8_t j = 0; j < FILTER_SIZE; j++) {
            temp_buffer[j] = filter_buffer[j];
        }
        
        // 冒泡排序
        for (uint8_t j = 0; j < FILTER_SIZE - 1; j++) {
            for (uint8_t k = 0; k < FILTER_SIZE - 1 - j; k++) {
                if (temp_buffer[k] > temp_buffer[k + 1]) {
                    float temp = temp_buffer[k];
                    temp_buffer[k] = temp_buffer[k + 1];
                    temp_buffer[k + 1] = temp;
                }
            }
        }
        
        float output = temp_buffer[FILTER_SIZE / 2];  // 中位数
        
        printf("%.1f\t\t", input);
        for (uint8_t j = 0; j < FILTER_SIZE; j++) {
            printf("%.1f ", filter_buffer[j]);
        }
        printf("\t\t%.1f", output);
        
        if (input != output && (input > 3.0 || input < 0.5)) {
            printf(" (异常值被滤除)");
        }
        printf("\r\n");
    }
    
    printf("\n中值滤波特点:\r\n");
    printf("✓ 有效去除脉冲噪声 (如5.0, 8.0)\r\n");
    printf("✓ 保持信号边沿特性\r\n");
    printf("✗ 计算复杂度较高 O(n²)\r\n");
    printf("✗ 对高斯噪声效果一般\r\n");
}

/**
 * @brief 演示滑动平均滤波算法
 */
void practice_average_filter_demo(void)
{
    printf("=== 滑动平均滤波算法演示 ===\r\n");
    
    #define AVG_FILTER_SIZE 8
    float test_data[] = {1.0, 1.1, 1.2, 1.0, 1.1, 1.3, 1.2, 1.1, 1.0, 1.2};  // 带噪声数据
    uint8_t data_count = sizeof(test_data) / sizeof(float);
    
    float filter_buffer[AVG_FILTER_SIZE] = {0};
    uint8_t filter_index = 0;
    uint8_t filter_filled = 0;
    
    printf("滑动平均滤波处理过程 (窗口大小: %d):\r\n", AVG_FILTER_SIZE);
    printf("输入\t有效数据数\t平均值\t\t输出\r\n");
    printf("------------------------------------------------\r\n");
    
    for (uint8_t i = 0; i < data_count; i++) {
        float input = test_data[i];
        
        // 更新滤波缓冲区
        filter_buffer[filter_index] = input;
        filter_index = (filter_index + 1) % AVG_FILTER_SIZE;
        
        if (!filter_filled && filter_index == 0) {
            filter_filled = 1;
        }
        
        // 计算平均值
        float sum = 0.0f;
        uint8_t count = filter_filled ? AVG_FILTER_SIZE : filter_index;
        
        for (uint8_t j = 0; j < count; j++) {
            sum += filter_buffer[j];
        }
        
        float output = sum / count;
        
        printf("%.1f\t\t%d\t\t%.3f\t\t%.3f\r\n", input, count, sum, output);
    }
    
    printf("\n滑动平均滤波特点:\r\n");
    printf("✓ 有效减少随机噪声\r\n");
    printf("✓ 计算简单，效率高 O(n)\r\n");
    printf("✓ 实现简单，内存占用固定\r\n");
    printf("✗ 会平滑信号边沿\r\n");
    printf("✗ 响应速度较慢\r\n");
}

/**
 * @brief 演示级联滤波效果
 */
void practice_cascade_filter_demo(void)
{
    printf("=== 级联滤波效果演示 ===\r\n");
    
    // 测试数据：正常信号 + 脉冲噪声 + 随机噪声
    float test_data[] = {2.0, 2.1, 8.0, 2.2, 2.0, 2.1, 2.3, 2.2, 15.0, 2.1, 2.0, 2.2};
    uint8_t data_count = sizeof(test_data) / sizeof(float);
    
    printf("级联滤波处理过程:\r\n");
    printf("原始数据\t中值滤波\t平均滤波\t说明\r\n");
    printf("--------------------------------------------------------\r\n");
    
    // 简化的滤波器状态
    float median_buffer[5] = {0};
    float avg_buffer[4] = {0};
    uint8_t median_idx = 0, avg_idx = 0;
    
    for (uint8_t i = 0; i < data_count; i++) {
        float input = test_data[i];
        
        // 第一级：中值滤波
        median_buffer[median_idx] = input;
        median_idx = (median_idx + 1) % 5;
        
        // 简化的中值计算（实际应该排序）
        float median_output = input;  // 简化处理
        if (input > 5.0) {  // 检测到异常值
            median_output = 2.1;  // 用正常值替代
        }
        
        // 第二级：平均滤波
        avg_buffer[avg_idx] = median_output;
        avg_idx = (avg_idx + 1) % 4;
        
        float sum = 0;
        for (uint8_t j = 0; j < 4; j++) {
            sum += avg_buffer[j];
        }
        float avg_output = sum / 4.0f;
        
        printf("%.1f\t\t%.1f\t\t%.2f\t\t", input, median_output, avg_output);
        
        if (input > 5.0) {
            printf("异常值被中值滤波去除");
        } else if (fabs(avg_output - input) > 0.1) {
            printf("随机噪声被平均滤波平滑");
        } else {
            printf("正常信号");
        }
        printf("\r\n");
    }
    
    printf("\n级联滤波优势:\r\n");
    printf("1. 中值滤波去除突发异常值\r\n");
    printf("2. 平均滤波平滑剩余噪声\r\n");
    printf("3. 两级滤波效果更佳\r\n");
    printf("4. 适合复杂噪声环境\r\n");
}

// ============================================================================
// 练习4：数据处理流程演示
// ============================================================================

/**
 * @brief 演示完整的ADC数据处理流程
 */
void practice_data_processing_demo(void)
{
    printf("=== ADC数据处理流程演示 ===\r\n");
    
    printf("完整数据处理流程:\r\n");
    printf("1. 硬件采集 → ADC连续转换\r\n");
    printf("2. DMA传输 → 自动搬移到缓冲区\r\n");
    printf("3. 缓冲区平均 → 32点硬件平均\r\n");
    printf("4. 电压转换 → ADC值转电压公式\r\n");
    printf("5. 中值滤波 → 去除异常值\r\n");
    printf("6. 平均滤波 → 平滑处理\r\n");
    printf("7. 变比应用 → 用户设置的比例\r\n");
    printf("8. 超限检测 → 阈值比较\r\n");
    
    // 模拟完整处理过程
    printf("\n模拟处理过程:\r\n");
    
    // 1. 模拟DMA缓冲区数据
    uint32_t adc_buffer[32];
    uint32_t adc_sum = 0;
    for (uint8_t i = 0; i < 32; i++) {
        adc_buffer[i] = 2048 + (rand() % 21) - 10;  // 2048±10
        adc_sum += adc_buffer[i];
    }
    uint32_t adc_avg = adc_sum / 32;
    printf("1. DMA缓冲区平均: %d\r\n", adc_avg);
    
    // 2. ADC值转电压
    float raw_voltage = ((float)adc_avg * 3.3f) / 4096.0f;
    printf("2. 原始电压: %.3f V\r\n", raw_voltage);
    
    // 3. 中值滤波（简化）
    float median_filtered = raw_voltage;  // 假设无异常值
    printf("3. 中值滤波后: %.3f V\r\n", median_filtered);
    
    // 4. 平均滤波（简化）
    float avg_filtered = median_filtered * 0.9f + 0.1f * 1.65f;  // 简化的滑动平均
    printf("4. 平均滤波后: %.3f V\r\n", avg_filtered);
    
    // 5. 变比应用
    float ratio = 2.0f;  // 假设变比为2
    float processed_voltage = avg_filtered * ratio;
    printf("5. 应用变比(%.1f): %.3f V\r\n", ratio, processed_voltage);
    
    // 6. 超限检测
    float limit = 3.0f;  // 假设限制值为3V
    uint8_t over_limit = (processed_voltage > limit) ? 1 : 0;
    printf("6. 超限检测(%.1fV): %s\r\n", limit, over_limit ? "超限" : "正常");
    
    printf("\n处理结果:\r\n");
    printf("  最终电压值: %.3f V\r\n", processed_voltage);
    printf("  系统状态: %s\r\n", over_limit ? "报警" : "正常");
}

// ============================================================================
// 主练习函数
// ============================================================================

/**
 * @brief 第7章所有练习的入口函数
 */
void chapter7_practice_all(void)
{
    printf("\r\n");
    printf("========================================\r\n");
    printf("    第7章：ADC数据采集与滤波算法实现\r\n");
    printf("           实践练习开始\r\n");
    printf("========================================\r\n");
    
    practice_adc_config_demo();
    printf("\r\n");
    
    practice_adc_timing_demo();
    printf("\r\n");
    
    practice_dma_config_demo();
    printf("\r\n");
    
    practice_dma_buffer_demo();
    printf("\r\n");
    
    practice_median_filter_demo();
    printf("\r\n");
    
    practice_average_filter_demo();
    printf("\r\n");
    
    practice_cascade_filter_demo();
    printf("\r\n");
    
    practice_data_processing_demo();
    printf("\r\n");
    
    printf("========================================\r\n");
    printf("           实践练习结束\r\n");
    printf("========================================\r\n");
    printf("\r\n");
}
